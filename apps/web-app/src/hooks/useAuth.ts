import { useAppSelector, useAppDispatch } from "@/store";
import {
	registerUser,
	loginUser,
	logoutUser,
	verifyEmail,
	resendVerification,
	sendOtp,
	refreshAccessToken,
	clearError,
} from "@/store/slices/authSlice";
import {
	RegisterRequest,
	LoginRequest,
	VerifyEmailRequest,
	ResendVerificationRequest,
} from "@/types/auth";

export const useAuth = () => {
	const dispatch = useAppDispatch();
	const auth = useAppSelector((state) => state.auth);

	const register = async (userData: RegisterRequest) => {
		return dispatch(registerUser(userData));
	};

	const login = async (credentials: LoginRequest) => {
		return dispatch(loginUser(credentials));
	};

	const logout = async () => {
		return dispatch(logoutUser());
	};

	const verify = async (verificationData: VerifyEmailRequest) => {
		return dispatch(verifyEmail(verificationData));
	};

	const resendCode = async (data: ResendVerificationRequest) => {
		return dispatch(resendVerification(data));
	};

	const sendOtpCode = async (data: ResendVerificationRequest) => {
		return dispatch(sendOtp(data));
	};

	const clearAuthError = () => {
		dispatch(clearError());
	};

	const refreshToken = () => {
		return dispatch(refreshAccessToken());
	};

	return {
		// State
		user: auth.user,
		company: auth.company,
		token: auth.token,
		accessToken: auth.accessToken,
		refreshToken: auth.refreshToken,
		expiresIn: auth.expiresIn,
		isAuthenticated: auth.isAuthenticated,
		isLoading: auth.isLoading,
		error: auth.error,
		isEmailVerified: auth.isEmailVerified,

		// Actions
		register,
		login,
		logout,
		verify,
		resendCode,
		sendOtpCode,
		refreshAccessToken: refreshToken,
		clearAuthError,
	};
};
