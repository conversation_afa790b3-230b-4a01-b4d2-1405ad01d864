import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import {
	AuthState,
	User,
	Company,
	RegisterRequest,
	LoginRequest,
	AuthResponse,
	VerifyEmailRequest,
	ResendVerificationRequest,
	ApiResponse,
} from "@/types/auth";
import { apiMethods, endpoints } from "@/lib/api";
import { SendFeedback, SendCatchFeedback } from "@/lib/feedback";

// Initial state
const initialState: AuthState = {
	user: null,
	company: null,
	token: null,
	accessToken: null,
	refreshToken: null,
	expiresIn: null,
	isAuthenticated: false,
	isLoading: false,
	error: null,
	isEmailVerified: false,
};

// Async thunks for API calls
export const registerUser = createAsyncThunk(
	"auth/register",
	async (userData: RegisterRequest, { rejectWithValue }) => {
		try {
			const response = await apiMethods.post<ApiResponse<null>>(
				endpoints.auth.register,
				userData
			);

			if (response.data.success) {
				SendFeedback(
					response.data.message ||
						"Registration successful! Please verify your email.",
					"success"
				);

				// For registration, we return the user data from the form since API returns null
				// The user will need to verify email before getting full user data
				return {
					user: {
						id: "", // Will be populated after email verification
						email: userData.email,
						firstName: userData.firstName,
						lastName: userData.lastName,
						userType: userData.role,
						isEmailVerified: false,
						createdAt: new Date().toISOString(),
						updatedAt: new Date().toISOString(),
					} as User,
					token: "", // No token until email verification
					company: userData.organizationName
						? ({
								id: "",
								name: userData.organizationName,
								createdAt: new Date().toISOString(),
								updatedAt: new Date().toISOString(),
						  } as Company)
						: null,
				};
			} else {
				throw new Error(response.data.message || "Registration failed");
			}
		} catch (error: unknown) {
			const axiosError = error as {
				response?: { data?: { error?: string[]; message?: string } };
				message?: string;
			};
			console.error(
				"Registration error:",
				axiosError.response?.data || axiosError.message
			);

			// Handle validation errors specifically
			if (
				axiosError.response?.data?.error &&
				Array.isArray(axiosError.response.data.error)
			) {
				const validationErrors = axiosError.response.data.error.join(", ");
				SendFeedback(`Validation failed: ${validationErrors}`, "error");
				return rejectWithValue(validationErrors);
			}

			const errorMessage =
				axiosError.response?.data?.message ||
				axiosError.message ||
				"Registration failed";
			SendCatchFeedback(error);
			return rejectWithValue(errorMessage);
		}
	}
);

export const loginUser = createAsyncThunk(
	"auth/login",
	async (credentials: LoginRequest, { rejectWithValue }) => {
		try {
			const response = await apiMethods.post<ApiResponse<AuthResponse>>(
				endpoints.auth.login,
				credentials
			);

			if (response.data.success && response.data.data) {
				const responseData = response.data.data;

				// Debug: Log the actual API response structure
				console.log(
					"🔍 Login API Response Structure:",
					JSON.stringify(responseData, null, 2)
				);

				// Extract tokens from the nested tokens object
				const { tokens, user, company } = responseData;
				const { accessToken, refreshToken, expiresIn } = tokens || {};

				// Debug: Log individual token fields
				console.log("🔑 Token fields:", {
					accessToken,
					refreshToken,
					expiresIn,
					hasUser: !!user,
					hasCompany: !!company,
				});

				// Store tokens in localStorage and cookies with safe handling
				if (typeof window !== "undefined") {
					// Store the access token as the primary auth token
					if (accessToken) {
						localStorage.setItem("authToken", accessToken);
						localStorage.setItem("accessToken", accessToken);

						// Also store in cookies for middleware access
						document.cookie = `authToken=${accessToken}; path=/; max-age=${
							expiresIn || 3600
						}; SameSite=Lax`;

						console.log(
							"✅ Stored authToken:",
							accessToken.substring(0, 20) + "..."
						);
					} else {
						console.warn("⚠️ No accessToken found in API response");
					}

					// Store refresh token
					if (refreshToken) {
						localStorage.setItem("refreshToken", refreshToken);
						console.log("✅ Stored refreshToken");
					}

					// Store expiration time
					if (expiresIn !== undefined && expiresIn !== null) {
						localStorage.setItem("expiresIn", expiresIn.toString());
						console.log("✅ Stored expiresIn:", expiresIn);
					}

					// Store user data
					localStorage.setItem("user", JSON.stringify(user));
					if (company) {
						localStorage.setItem("company", JSON.stringify(company));
					}
				}

				SendFeedback("Login successful!", "success");

				// Return the data in the expected format for the reducer
				return {
					user,
					company,
					token: accessToken, // Use accessToken as the main token
					accessToken,
					refreshToken,
					expiresIn,
				};
			} else {
				throw new Error(response.data.message || "Login failed");
			}
		} catch (error: unknown) {
			const axiosError = error as {
				response?: { data?: { message?: string } };
				message?: string;
			};
			SendCatchFeedback(error);
			return rejectWithValue(
				axiosError.response?.data?.message ||
					axiosError.message ||
					"Login failed"
			);
		}
	}
);

export const verifyEmail = createAsyncThunk(
	"auth/verifyEmail",
	async (verificationData: VerifyEmailRequest, { rejectWithValue }) => {
		try {
			console.log("Sending verification request:", verificationData);

			const response = await apiMethods.post<ApiResponse<{ user: User }>>(
				endpoints.auth.verifyEmail,
				verificationData
			);

			console.log("Verification response:", response.data);

			if (response.data.success) {
				// Handle case where data might be null (verification successful but no user data returned)
				if (response.data.data) {
					const { user } = response.data.data;

					// Update user in localStorage
					if (typeof window !== "undefined") {
						localStorage.setItem("user", JSON.stringify(user));
					}

					SendFeedback("Email verified successfully!", "success");
					return user;
				} else {
					// Verification successful but no user data returned
					SendFeedback(
						response.data.message || "Email verified successfully!",
						"success"
					);
					return null; // Return null to indicate successful verification without user data
				}
			} else {
				throw new Error(response.data.message || "Email verification failed");
			}
		} catch (error: unknown) {
			const axiosError = error as {
				response?: { data?: { message?: string } };
				message?: string;
			};
			console.error(
				"Verification error:",
				axiosError.response?.data || axiosError.message
			);
			SendCatchFeedback(error);
			return rejectWithValue(
				axiosError.response?.data?.message ||
					axiosError.message ||
					"Email verification failed"
			);
		}
	}
);

export const resendVerification = createAsyncThunk(
	"auth/resendVerification",
	async (data: ResendVerificationRequest, { rejectWithValue }) => {
		try {
			const response = await apiMethods.post<ApiResponse>(
				endpoints.auth.resendVerification,
				data
			);

			if (response.data.success) {
				SendFeedback("Verification code sent successfully!", "success");
				return response.data.message || "Verification code sent";
			} else {
				throw new Error(
					response.data.message || "Failed to resend verification code"
				);
			}
		} catch (error: unknown) {
			const axiosError = error as {
				response?: { data?: { message?: string } };
				message?: string;
			};
			SendCatchFeedback(error);
			return rejectWithValue(
				axiosError.response?.data?.message ||
					axiosError.message ||
					"Failed to resend verification code"
			);
		}
	}
);

export const sendOtp = createAsyncThunk(
	"auth/sendOtp",
	async (data: ResendVerificationRequest, { rejectWithValue }) => {
		try {
			console.log("Sending OTP request:", data);

			const response = await apiMethods.post<ApiResponse>(
				endpoints.auth.sendOtp,
				data
			);

			console.log("Send OTP response:", response.data);

			if (response.data.success) {
				SendFeedback("OTP sent successfully!", "success");
				return response.data.message || "OTP sent successfully";
			} else {
				throw new Error(response.data.message || "Failed to send OTP");
			}
		} catch (error: unknown) {
			const axiosError = error as {
				response?: { data?: { message?: string } };
				message?: string;
			};
			console.error(
				"Send OTP error:",
				axiosError.response?.data || axiosError.message
			);
			SendCatchFeedback(error);
			return rejectWithValue(
				axiosError.response?.data?.message ||
					axiosError.message ||
					"Failed to send OTP"
			);
		}
	}
);

export const logoutUser = createAsyncThunk("auth/logout", async () => {
	try {
		await apiMethods.post(endpoints.auth.logout);

		// Clear localStorage and cookies
		if (typeof window !== "undefined") {
			localStorage.removeItem("authToken");
			localStorage.removeItem("accessToken");
			localStorage.removeItem("refreshToken");
			localStorage.removeItem("expiresIn");
			localStorage.removeItem("user");
			localStorage.removeItem("company");

			// Clear auth cookie
			document.cookie =
				"authToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";

			// Redirect to website login page after successful logout
			window.location.href =
				(process.env.NEXT_PUBLIC_WEBSITE_URL || "http://localhost:3000") +
				"/auth/login";
		}

		SendFeedback("Logged out successfully", "success");
		return null;
	} catch {
		// Even if API call fails, clear local storage and cookies
		if (typeof window !== "undefined") {
			localStorage.removeItem("authToken");
			localStorage.removeItem("accessToken");
			localStorage.removeItem("refreshToken");
			localStorage.removeItem("expiresIn");
			localStorage.removeItem("user");
			localStorage.removeItem("company");

			// Clear auth cookie
			document.cookie =
				"authToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";

			// Redirect to website login page even if API call fails
			window.location.href =
				(process.env.NEXT_PUBLIC_WEBSITE_URL || "http://localhost:3000") +
				"/auth/login";
		}

		SendFeedback("Logged out successfully", "success");
		return null;
	}
});

// Refresh token thunk
export const refreshAccessToken = createAsyncThunk(
	"auth/refreshToken",
	async (_, { rejectWithValue, getState }) => {
		try {
			const state = getState() as { auth: AuthState };
			const refreshToken =
				state.auth.refreshToken ||
				(typeof window !== "undefined"
					? localStorage.getItem("refreshToken")
					: null);

			if (!refreshToken) {
				throw new Error("No refresh token available");
			}

			console.log("🔄 Attempting to refresh access token...");

			const response = await apiMethods.post<ApiResponse<AuthResponse>>(
				endpoints.auth.refreshToken,
				{ refreshToken }
			);

			if (response.data.success && response.data.data) {
				// Handle both new nested structure and legacy flat structure
				const responseData = response.data.data;
				const tokens = responseData.tokens || responseData;
				const { accessToken, expiresIn } = tokens;

				console.log("✅ Token refresh successful");

				// Store new tokens in localStorage and cookies
				if (typeof window !== "undefined") {
					if (accessToken) {
						localStorage.setItem("authToken", accessToken);
						localStorage.setItem("accessToken", accessToken);

						// Update auth cookie
						document.cookie = `authToken=${accessToken}; path=/; max-age=${
							expiresIn || 3600
						}; SameSite=Lax`;
					}
					if (expiresIn) {
						localStorage.setItem("expiresIn", expiresIn.toString());
					}
				}

				return {
					token: accessToken,
					accessToken,
					expiresIn,
				};
			} else {
				throw new Error(response.data.message || "Token refresh failed");
			}
		} catch (error: unknown) {
			const axiosError = error as {
				response?: { data?: { message?: string } };
				message?: string;
			};
			console.error("❌ Token refresh failed:", error);
			SendCatchFeedback(error);
			return rejectWithValue(
				axiosError.response?.data?.message ||
					axiosError.message ||
					"Token refresh failed"
			);
		}
	}
);

// Auth slice
const authSlice = createSlice({
	name: "auth",
	initialState,
	reducers: {
		clearError: (state) => {
			state.error = null;
		},
		setLoading: (state, action: PayloadAction<boolean>) => {
			state.isLoading = action.payload;
		},
		initializeAuth: (state) => {
			// Initialize auth state from localStorage
			if (typeof window !== "undefined") {
				const token = localStorage.getItem("authToken");
				const accessToken = localStorage.getItem("accessToken");
				const refreshToken = localStorage.getItem("refreshToken");
				const expiresIn = localStorage.getItem("expiresIn");
				const userStr = localStorage.getItem("user");
				const companyStr = localStorage.getItem("company");

				if (token && userStr) {
					try {
						const user = JSON.parse(userStr);
						const company = companyStr ? JSON.parse(companyStr) : null;

						state.token = token;
						state.accessToken = accessToken;
						state.refreshToken = refreshToken;
						state.expiresIn = expiresIn ? parseInt(expiresIn) : null;
						state.user = user;
						state.company = company;
						state.isAuthenticated = true;
						state.isEmailVerified = user.isEmailVerified || false;
					} catch {
						// Clear corrupted data
						localStorage.removeItem("authToken");
						localStorage.removeItem("accessToken");
						localStorage.removeItem("refreshToken");
						localStorage.removeItem("expiresIn");
						localStorage.removeItem("user");
						localStorage.removeItem("company");
					}
				}
			}
		},
	},
	extraReducers: (builder) => {
		// Register user
		builder
			.addCase(registerUser.pending, (state) => {
				state.isLoading = true;
				state.error = null;
			})
			.addCase(registerUser.fulfilled, (state, action) => {
				state.isLoading = false;
				state.user = action.payload.user;
				state.company = action.payload.company || null;
				state.token = action.payload.token;
				// Don't set isAuthenticated to true until email is verified and we have a token
				state.isAuthenticated = false;
				state.isEmailVerified =
					action.payload.user.isEmailVerified ||
					action.payload.user.emailVerified ||
					false;
				state.error = null;
			})
			.addCase(registerUser.rejected, (state, action) => {
				state.isLoading = false;
				state.error = action.payload as string;
			});

		// Login user
		builder
			.addCase(loginUser.pending, (state) => {
				state.isLoading = true;
				state.error = null;
			})
			.addCase(loginUser.fulfilled, (state, action) => {
				state.isLoading = false;
				state.user = action.payload.user;
				state.company = action.payload.company || null;
				state.token = action.payload.token || null;
				state.accessToken = action.payload.accessToken || null;
				state.refreshToken = action.payload.refreshToken || null;
				state.expiresIn = action.payload.expiresIn || null;
				state.isAuthenticated = true;
				// Handle both new and legacy email verification fields
				state.isEmailVerified =
					action.payload.user.emailVerified ||
					action.payload.user.isEmailVerified ||
					false;
				state.error = null;
			})
			.addCase(loginUser.rejected, (state, action) => {
				state.isLoading = false;
				state.error = action.payload as string;
			});

		// Verify email
		builder
			.addCase(verifyEmail.pending, (state) => {
				state.isLoading = true;
				state.error = null;
			})
			.addCase(verifyEmail.fulfilled, (state, action) => {
				state.isLoading = false;
				// Only update user if payload contains user data
				if (action.payload) {
					state.user = action.payload;
				}
				state.isEmailVerified = true;
				state.error = null;
			})
			.addCase(verifyEmail.rejected, (state, action) => {
				state.isLoading = false;
				state.error = action.payload as string;
			});

		// Resend verification
		builder
			.addCase(resendVerification.pending, (state) => {
				state.isLoading = true;
				state.error = null;
			})
			.addCase(resendVerification.fulfilled, (state) => {
				state.isLoading = false;
				state.error = null;
			})
			.addCase(resendVerification.rejected, (state, action) => {
				state.isLoading = false;
				state.error = action.payload as string;
			});

		// Send OTP
		builder
			.addCase(sendOtp.pending, (state) => {
				state.isLoading = true;
				state.error = null;
			})
			.addCase(sendOtp.fulfilled, (state) => {
				state.isLoading = false;
				state.error = null;
			})
			.addCase(sendOtp.rejected, (state, action) => {
				state.isLoading = false;
				state.error = action.payload as string;
			});

		// Refresh token
		builder
			.addCase(refreshAccessToken.pending, (state) => {
				state.isLoading = true;
				state.error = null;
			})
			.addCase(refreshAccessToken.fulfilled, (state, action) => {
				state.isLoading = false;
				state.token = action.payload.token || null;
				state.accessToken = action.payload.accessToken || null;
				state.expiresIn = action.payload.expiresIn || null;
				state.error = null;
			})
			.addCase(refreshAccessToken.rejected, (state, action) => {
				state.isLoading = false;
				state.error = action.payload as string;
				// If refresh fails, user needs to log in again
				state.user = null;
				state.company = null;
				state.token = null;
				state.accessToken = null;
				state.refreshToken = null;
				state.expiresIn = null;
				state.isAuthenticated = false;
				state.isEmailVerified = false;
			});

		// Logout user
		builder.addCase(logoutUser.fulfilled, (state) => {
			state.user = null;
			state.company = null;
			state.token = null;
			state.accessToken = null;
			state.refreshToken = null;
			state.expiresIn = null;
			state.isAuthenticated = false;
			state.isEmailVerified = false;
			state.isLoading = false;
			state.error = null;
		});
	},
});

export const { clearError, setLoading, initializeAuth } = authSlice.actions;
export default authSlice.reducer;
