"use client";
import {
	AfricaSkillzLogoColoredIcon,
	EmployerIcon,
	FacilitatorIcon,
	RecruiterIcon,
} from "@africaskillz/ui/icons";
import { JobSeekerIcon } from "@africaskillz/ui";
import { ReactNode } from "react";
import Link from "next/link";
type DecisionCardProps = {
	icon: ReactNode;
	title: string;
	subtitle: string;
	link: string;
};

const userTypes = [
	{
		icon: <JobSeekerIcon />,
		title: "Job Seeker",
		subtitle:
			"Search and apply for job opportunities, resume management and more.",
		link: "/auth/sign-up/job-seeker",
	},
	{
		icon: <RecruiterIcon />,
		title: "Recruiter",
		subtitle: "Create, edit, search, archive, manage job listings and more.",
		link: "/auth/sign-up/recruiter",
	},
	{
		icon: <EmployerIcon />,
		title: "Employer",
		subtitle: "Post RFQs and RFPs for procurement opportunities and more.",
		link: "/auth/sign-up/employer",
	},
	{
		icon: <FacilitatorIcon />,
		title: "Facilitator",
		subtitle: "Post and manage events, scholarships, courses and more.",
		link: "/auth/sign-up/facilitator",
	},
];

const DecisionCard = ({ icon, title, subtitle, link }: DecisionCardProps) => {
	return (
		<Link href={link}>
			<div className="rounded-[16px] p-3 bg-[#C0CCFF] hover:shadow-lg transition-all duration-400 ease-in-out ">
				<div className="rounded-[8px] p-5 flex flex-col gap-.5 bg-white ">
					<div className="w-5 h-5">{icon}</div>
					<p className="font-semibold text-base text-[#3F3F46]">{title}</p>
					<p className="font-medium text-[14px] leading-[22px] text-[#71717A]">
						{subtitle}
					</p>
				</div>
			</div>
		</Link>
	);
};
export default function OnboardingDecisionPage() {
	return (
		<main className="w-full h-screen bg-white p-8 relative">
			<div className="flex items-center justify-between">
				<AfricaSkillzLogoColoredIcon className="h-[42px] w-[116px]" />
				<a
					href={`${
						process.env.NEXT_PUBLIC_WEBSITE_URL || "http://localhost:3000"
					}/auth/login`}
					className=" text-gray-700 font-medium text-sm transition-colors">
					Already have an account? <span className="text-[#335CFF]">Sign in</span>
				</a>
			</div>

			<div className="flex flex-col gap-10 items-start max-w-[724px] m-auto mt-[150px]">
				<div>
					<h2 className="font-semibold text-[18px] leading-[24px] text-[#27272A]">
						Tell us who you are to get started
					</h2>
					<p className="font-medium text-[14px] leading-[22px] text-[#71717A]">
						Choose the option that best describes you:
					</p>
				</div>

				<div className="grid grid-cols-2 gap-x-4 gap-y-6">
					{userTypes.map((user, index) => (
						<DecisionCard {...user} key={index} />
					))}
				</div>
			</div>

			<div className="w-full h-[326px] absolute left-0 bg-gradient-to-t from-[#A1B4FF] via-transparent to-transparent"></div>
		</main>
	);
}
