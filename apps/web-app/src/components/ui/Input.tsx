import { cn } from "@/lib/utils";
import { forwardRef, InputHTMLAttributes, ReactNode } from "react";

interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  variant?: 'default' | 'filled' | 'outlined';
  inputSize?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
}

const Input = forwardRef<HTMLInputElement, InputProps>(({
  className,
  type = 'text',
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  variant = 'default',
  inputSize = 'md',
  fullWidth = false,
  disabled,
  ...props
}, ref) => {
  const baseStyles = "transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-0";
  
  const variantStyles = {
    default: "border border-brandGray-300 bg-white focus:ring-brand-500 focus:border-brand-500",
    filled: "border-0 bg-brandGray-100 focus:ring-brand-500 focus:bg-white",
    outlined: "border-2 border-brandGray-300 bg-transparent focus:ring-brand-500 focus:border-brand-500"
  };

  const sizeStyles = {
    sm: "px-3 py-2 text-sm rounded-md",
    md: "px-4 py-3 text-sm rounded-lg",
    lg: "px-5 py-4 text-base rounded-lg"
  };

  const errorStyles = error ? "border-error-500 focus:ring-error-500 focus:border-error-500" : "";
  const disabledStyles = disabled ? "opacity-50 cursor-not-allowed bg-brandGray-50" : "";
  const widthStyles = fullWidth ? "w-full" : "";

  const inputClasses = cn(
    baseStyles,
    variantStyles[variant],
    sizeStyles[inputSize],
    errorStyles,
    disabledStyles,
    widthStyles,
    leftIcon && "pl-10",
    rightIcon && "pr-10",
    className
  );

  return (
    <div className={cn("relative", fullWidth && "w-full")}>
      {label && (
        <label className="block text-sm font-medium text-brandGray-700 mb-2">
          {label}
          {props.required && <span className="text-error-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        {leftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <div className="text-brandGray-400">
              {leftIcon}
            </div>
          </div>
        )}
        
        <input
          type={type}
          className={inputClasses}
          ref={ref}
          disabled={disabled}
          {...props}
        />
        
        {rightIcon && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <div className="text-brandGray-400">
              {rightIcon}
            </div>
          </div>
        )}
      </div>
      
      {error && (
        <p className="mt-2 text-sm text-error-600 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {error}
        </p>
      )}
      
      {helperText && !error && (
        <p className="mt-2 text-sm text-brandGray-500">
          {helperText}
        </p>
      )}
    </div>
  );
});

Input.displayName = "Input";

export default Input;
