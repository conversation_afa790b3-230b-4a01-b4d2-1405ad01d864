import { cn } from "@/lib/utils";
import { ReactNode } from "react";
import Image from "next/image";

interface ProfileCardProps {
  name: string;
  title?: string;
  avatar?: string;
  location?: string;
  rating?: number;
  skills?: string[];
  stats?: {
    label: string;
    value: string | number;
  }[];
  actions?: ReactNode;
  className?: string;
  variant?: 'default' | 'compact' | 'detailed';
  verified?: boolean;
  online?: boolean;
}

const ProfileCard = ({
  name,
  title,
  avatar,
  location,
  rating,
  skills = [],
  stats = [],
  actions,
  className,
  variant = 'default',
  verified = false,
  online = false
}: ProfileCardProps) => {
  const renderStars = (rating: number) => {
    return [...Array(5)].map((_, i) => (
      <svg
        key={i}
        className={cn(
          "w-4 h-4",
          i < rating ? "text-yellow-400" : "text-brandGray-300"
        )}
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
      </svg>
    ));
  };

  if (variant === 'compact') {
    return (
      <div className={cn(
        "flex items-center space-x-3 p-4 bg-white rounded-lg border border-brandGray-200 hover:shadow-md transition-all duration-200",
        className
      )}>
        <div className="relative">
          <div className="w-12 h-12 rounded-full bg-brandGray-200 overflow-hidden">
            {avatar ? (
              <Image
                src={avatar}
                alt={name}
                width={48}
                height={48}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-brandGray-500 text-lg">
                {name.charAt(0).toUpperCase()}
              </div>
            )}
          </div>
          {online && (
            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
          )}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center">
            <h3 className="text-sm font-medium text-brandGray-900 truncate">{name}</h3>
            {verified && (
              <svg className="w-4 h-4 ml-1 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            )}
          </div>
          {title && (
            <p className="text-xs text-brandGray-500 truncate">{title}</p>
          )}
        </div>
        {actions}
      </div>
    );
  }

  return (
    <div className={cn(
      "bg-white rounded-xl border border-brandGray-200 p-6 hover:shadow-lg transition-all duration-200",
      className
    )}>
      {/* Header */}
      <div className="flex items-start space-x-4 mb-4">
        <div className="relative">
          <div className="w-16 h-16 rounded-full bg-brandGray-200 overflow-hidden">
            {avatar ? (
              <Image
                src={avatar}
                alt={name}
                width={64}
                height={64}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-brandGray-500 text-xl">
                {name.charAt(0).toUpperCase()}
              </div>
            )}
          </div>
          {online && (
            <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 border-2 border-white rounded-full"></div>
          )}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center">
            <h3 className="text-lg font-semibold text-brandGray-900 truncate">{name}</h3>
            {verified && (
              <svg className="w-5 h-5 ml-2 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            )}
          </div>
          {title && (
            <p className="text-sm text-brandGray-600 mb-1">{title}</p>
          )}
          {location && (
            <p className="text-sm text-brandGray-500 flex items-center">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
              </svg>
              {location}
            </p>
          )}
        </div>
      </div>

      {/* Rating */}
      {rating && (
        <div className="flex items-center mb-4">
          <div className="flex items-center">
            {renderStars(rating)}
          </div>
          <span className="ml-2 text-sm text-brandGray-600">
            {rating.toFixed(1)}
          </span>
        </div>
      )}

      {/* Skills */}
      {skills.length > 0 && (
        <div className="mb-4">
          <div className="flex flex-wrap gap-2">
            {skills.slice(0, 4).map((skill, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-brand-50 text-brand-700 text-xs font-medium rounded-full"
              >
                {skill}
              </span>
            ))}
            {skills.length > 4 && (
              <span className="px-3 py-1 bg-brandGray-100 text-brandGray-600 text-xs font-medium rounded-full">
                +{skills.length - 4} more
              </span>
            )}
          </div>
        </div>
      )}

      {/* Stats */}
      {stats.length > 0 && (
        <div className="grid grid-cols-2 gap-4 mb-4">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-lg font-semibold text-brandGray-900">
                {stat.value}
              </div>
              <div className="text-xs text-brandGray-500">
                {stat.label}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Actions */}
      {actions && (
        <div className="pt-4 border-t border-brandGray-200">
          {actions}
        </div>
      )}
    </div>
  );
};

export default ProfileCard;
