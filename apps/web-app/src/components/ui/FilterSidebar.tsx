import React from "react";
import Button from "./Button";
import { CheckboxDropdown } from "./LabelInput";

export type FilterSection = {
	type: "checkbox";
	title: string;
	options: { label: string; value: string }[];
	selected: string[];
	onChange: (selected: string[]) => void;
	placeholder?: string;
};

export type FilterSidebarProps = {
	sections: FilterSection[];
	onApplyFilters?: () => void;
	onClearAll?: () => void;
	header?: React.ReactNode;
};

const FilterSidebar: React.FC<FilterSidebarProps> = ({
	sections,
	onApplyFilters,
	onClearAll,
	header,
}) => {
	return (
		<aside className="w-full md:w-[260px] mb-6 md:mb-0 ">
			<div className="bg-white rounded-xl px-5 py-[25px] flex flex-col gap-5 shadow-sm border border-neutral-200">
				{header || (
					<h3 className="font-medium text-sm leading-[22px] text-[#71717A] mb-4">
						FILTERS
					</h3>
				)}
				{sections.map((section, idx) => (
					<div key={section.title + idx}>
						{section.type === "checkbox" && (
							<CheckboxDropdown
								title={section.title}
								options={section.options}
								selected={section.selected}
								onChange={section.onChange}
								placeholder={section.placeholder}
							/>
						)}
					</div>
				))}
				<div className="border-t border-[#E4E4E7] pt-4 flex flex-col gap-4">
					<Button
						variant="outline"
						size="sm"
						className="w-full cursor-pointer"
						onClick={onApplyFilters}>
						Apply Filters
					</Button>
					<button
						className="w-full text-xs leading-[22px] cursor-pointer font-medium text-neutral-400 "
						onClick={onClearAll}>
						Clear all
					</button>
				</div>
			</div>
		</aside>
	);
};

export default FilterSidebar;
