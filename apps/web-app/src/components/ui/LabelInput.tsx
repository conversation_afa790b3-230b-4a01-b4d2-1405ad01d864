"use client";
import React, { forwardRef, useState, useRef, useEffect } from "react";
import { ChevronDown, SearchIcon } from "@/components/common/icons";
import { cn } from "@/lib/utils";
import { Mail } from "lucide-react";
import { ReactNode } from "react";

export interface LabelInputProps
	extends Omit<
		React.InputHTMLAttributes<HTMLInputElement>,
		"type" | "onSelect"
	> {
	label?: string;
	inputType?: "text" | "email" | "search" | "dropdown" | "checkboxDropdown";
	placeholder?: string;
	data?: Array<{ value: string; label: string }>;
	error?: string;
	helperText?: string;
	className?: string;
	inputClassName?: string;
	labelClassName?: string;
	dropdownLeftIcon?: ReactNode;
	onSelect?: (value: string) => void;
	icon?: ReactNode; // Optional icon prop
	wrapperClassName?: string;
}

const LabelInput = forwardRef<HTMLInputElement, LabelInputProps>(
	(
		{
			label,
			inputType = "text",
			placeholder,
			data = [],
			error,
			helperText,
			className,
			inputClassName,
			labelClassName,
			onSelect,
			value,
			onChange,
			dropdownLeftIcon,
			icon,
			wrapperClassName,
			...props
		},
		ref
	) => {
		const [isOpen, setIsOpen] = React.useState(false);
		const [selectedValue, setSelectedValue] = React.useState(value || "");

		// Email validation helper
		const isValidEmail = (email: string) => {
			const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
			return emailRegex.test(email);
		};

		const handleDropdownSelect = (optionValue: string, optionLabel: string) => {
			setSelectedValue(optionLabel);
			setIsOpen(false);
			onSelect?.(optionValue);

			// Create synthetic event for onChange compatibility
			const syntheticEvent = {
				target: { value: optionValue },
				currentTarget: { value: optionValue },
			} as React.ChangeEvent<HTMLInputElement>;
			onChange?.(syntheticEvent);
		};

		const renderInput = () => {
			const baseInputClasses = cn(
				"w-full rounded-[10px] bg-[#FFFFFE] border border-[#E4E4E7] px-3 py-2.5 flex gap-2 items-center",
				"text-sm font-normal text-brandGray-900",
				"placeholder:text-[#A1A1AA] placeholder:font-normal placeholder:text-sm",
				"focus:outline-none ",
				"transition-colors duration-200",
				error && "border-red-500 focus:border-red-500 focus:ring-red-500/20",
				inputType === "search" && "pl-9",
				inputType === "email" && "pl-9 bg-neutral-50",
				inputType === "dropdown" && "pr-9 cursor-pointer",
				inputClassName
			);

			if (inputType === "dropdown") {
				return (
					<div className={cn("relative", wrapperClassName || "w-full")}>
						<div
							className={baseInputClasses}
							onClick={() => setIsOpen(!isOpen)}
							role="button"
							tabIndex={0}
							onKeyDown={(e) => {
								if (e.key === "Enter" || e.key === " ") {
									setIsOpen(!isOpen);
								}
							}}>
							{dropdownLeftIcon && dropdownLeftIcon}
							<span
								className={
									selectedValue ? "text-brandGray-900" : "text-[#A1A1AA]"
								}>
								{selectedValue || placeholder}
							</span>
						</div>

						{/* Chevron Icon */}
						<div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
							<ChevronDown
								width={20}
								height={20}
								className={cn(
									"transition-transform duration-200 text-neutral-400",
									isOpen && "rotate-180"
								)}
							/>
						</div>

						{/* Dropdown Menu */}
						{isOpen && (
							<div className="absolute top-full left-0 right-0 mt-1 bg-white border border-[#E4E4E7] rounded-[10px] shadow-lg z-50 max-h-60 overflow-y-auto min-w-full">
								{data.map((option) => (
									<div
										key={option.value}
										className="px-3 py-2.5 text-sm text-brandGray-900 hover:bg-brandGray-50 cursor-pointer first:rounded-t-[10px] last:rounded-b-[10px]"
										onClick={() =>
											handleDropdownSelect(option.value, option.label)
										}>
										{option.label}
									</div>
								))}
							</div>
						)}
					</div>
				);
			}

			return (
				<div className={cn("relative", wrapperClassName || "w-full")}>
					{inputType === "search" && (
						<div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
							<SearchIcon />
						</div>
					)}

					{inputType === "email" && (
						<div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
							<Mail className="w-5 h-5 text-neutral-400" />
						</div>
					)}

					<input
						ref={ref}
						type={inputType === "email" ? "email" : "text"}
						placeholder={placeholder}
						value={selectedValue}
						onChange={(e) => {
							const newValue = e.target.value;
							setSelectedValue(newValue);

							// Add email validation for email inputs
							if (
								inputType === "email" &&
								newValue &&
								!isValidEmail(newValue)
							) {
								// You can add custom validation logic here if needed
								// For now, we'll just pass the event through
							}

							onChange?.(e);
						}}
						className={baseInputClasses}
						{...props}
					/>
				</div>
			);
		};

		return (
			<div className={cn("space-y-1", className)}>
				{label && (
					<label
						className={cn(
							"block text-sm font-medium text-brandGray-700",
							labelClassName
						)}>
						{label}
					</label>
				)}

				<div className="relative flex items-center">
					{icon && <span className="mr-2 flex items-center">{icon}</span>}
					{renderInput()}
				</div>

				{error && <p className="text-sm text-red-600 mt-1">{error}</p>}

				{helperText && !error && (
					<p className="text-sm text-brandGray-500 mt-1">{helperText}</p>
				)}
			</div>
		);
	}
);

LabelInput.displayName = "LabelInput";

export default LabelInput;

interface CheckboxDropdownProps {
	title: string;
	options: { label: string; value: string }[];
	selected: string[];
	onChange: (selected: string[]) => void;
	placeholder?: string;
}

export function CheckboxDropdown({
	title,
	options,
	selected,
	onChange,
}: CheckboxDropdownProps) {
	const [open, setOpen] = useState(false);
	const ref = useRef<HTMLDivElement>(null);

	useEffect(() => {
		function handleClickOutside(event: MouseEvent) {
			if (ref.current && !ref.current.contains(event.target as Node)) {
				setOpen(false);
			}
		}
		document.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, []);

	return (
		<div className="w-full " ref={ref}>
			<button
				type="button"
				className="w-full cursor-pointer flex items-center justify-between  rounded px-3 py-2 text-left bg-white  "
				onClick={() => setOpen((o) => !o)}
				aria-expanded={open}
				aria-controls={`accordion-${title.replace(/\s+/g, "-")}`}>
				<span className="font-medium leading-[22px] text-[#A1A1AA] uppercase text-sm">
					{title}
				</span>
				<ChevronDown
					className={`w-4 h-4 ml-2 transition-transform ${
						open ? "rotate-180" : "rotate-0"
					}`}
				/>
			</button>
			<div
				id={`accordion-${title.replace(/\s+/g, "-")}`}
				className={`transition-all duration-300 overflow-hidden ${
					open ? "max-h-300" : "max-h-0"
				}`}
				style={
					{
						// For smooth transition, set maxHeight when open, 0 when closed
					}
				}>
				<div className="h-fit">
					{/* <div className="mb-2 text-xs text-gray-500">
						{selected.length > 0
							? options
									.filter((o) => selected.includes(o.value))
									.map((o) => o.label)
									.join(", ")
							: placeholder || "Select..."}
					</div> */}
					<ul className="max-h-fit overflow-y-auto space-y-1">
						{options.map((option) => (
							<li key={option.value}>
								<label className="flex items-center gap-2 text-sm cursor-pointer text-[#71717A] font-medium leading-[22px] p-2">
									<input
										type="checkbox"
										checked={selected.includes(option.value)}
										onChange={() => {
											if (selected.includes(option.value)) {
												onChange(selected.filter((v) => v !== option.value));
											} else {
												onChange([...selected, option.value]);
											}
										}}
										className="accent-blue-500"
									/>
									{option.label}
								</label>
							</li>
						))}
					</ul>
				</div>
			</div>
		</div>
	);
}
