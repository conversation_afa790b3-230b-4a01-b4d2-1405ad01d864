"use client"

import { X } from "lucide-react"
import type React from "react"

interface ModalProps {
  open: boolean
  onClose: () => void
  children: React.ReactNode
  className?: string
}

const Modal: React.FC<ModalProps> = ({ open, onClose, children, className }) => {
  if (!open) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
      <div className={`bg-white rounded-2xl shadow-xl relative w-full max-w-[596px] mx-4 ${className || ""}`}>
        <button
          className="absolute top-4 right-4 text-2xl text-gray-400 hover:text-gray-600 focus:outline-none w-fit"
          onClick={onClose}
          aria-label="Close modal"
        >
          <X className="text-[#52525B] w-5 h-5"/>
        </button>
        {children}
      </div>
    </div>
  )
}

export default Modal
