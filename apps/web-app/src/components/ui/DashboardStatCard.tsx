import { CaretIcon } from "@ui/icons";
import { But<PERSON> } from "@ui/ui";
import React, { ReactNode } from "react";
import { PlusIcon } from "../common/icons";

interface DashboardStatCardProps {
	title: string;
	value: number;
	subtext: string;
}
interface JobPostCardProps {
	title: string;
	icon: ReactNode;
}

const DashboardStatCard: React.FC<DashboardStatCardProps> = ({ title, value, subtext }) => (
	<div className="rounded-xl  p-5 flex flex-col gap-2 border border-[#E4E4E7] bg-white text-gray-300 w-[282px] relative">
		<div className="flex justify-between items-start">
			<p className="text-sm font-medium leading-[22px] text-[#71717A]">{title}</p>
			<button className="border border-[#E4E4E7] rounded-[8px] p-1">
				<CaretIcon className="w-4 h-4" color="#71717A" />
			</button>
		</div>

		<div className="text-[24px] leading-[32px] text-[#27272A] font-bold">{value}</div>
		<div className="text-sm leading-[22px] text-[#71717A] ">{subtext}</div>
	</div>
);



const JobPostCard: React.FC<JobPostCardProps> = ({title, icon}) => {
	return (
		<div className="p-5 rounded-[16px] border border-[#E4E4E7] w-[381px]">
			<div className="flex items-center gap-2">
				<span className="w-5 h-5">{icon}</span>
				<p className="font-semibold text-base leading-[26px] text-[#27272A]">{title}</p>
			</div>

			<div className="flex flex-col gap-4 items-center justify-center p-5 h-[263px] w-full">
				<p className="font-medium text-sm leading-[22px] text-[#71717A] text-center">
					Metrics for {title} will appear. <br /> Create a job to get started.
				</p>

                <Button size="sm" variant="outline" className="w-full" rightIcon={<PlusIcon color="#335CFF" className="w-5 h-5"/>}>Post a job</Button>
			</div>
		</div>
	);
};

export {JobPostCard, DashboardStatCard }
