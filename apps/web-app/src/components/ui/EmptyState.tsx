import { FC } from "react";

interface EmptyStateProps {
	icon?: React.ReactNode;
	title: string;
	subtitle: string;
	onClear: () => void;
	buttonText?: string;
}

const EmptyState: FC<EmptyStateProps> = ({
	icon,
	title,
	subtitle,
	onClear,
	buttonText = "Clear all filters",
}) => (
	<div className="flex flex-col items-center justify-center py-16 px-4 text-center">
		{icon && <div className="mb-6 p-6 rounded-[32px] border border-[#E4E4E7]  text-5xl text-blue-400">{icon}</div>}
		<h2 className="text-[18px] leading-[24px] text-[#27272A] font-semibold ">{title}</h2>
		<p className=" font-normal text-sm leading-[22px] text-[#71717A] mb-4">{subtitle}</p>
		<button
			onClick={onClear}
			className="px-4 py-2 rounded-md cursor-pointer bg-brand-500 text-white font-medium hover:bg-brand-700 transition">
			{buttonText}
		</button>
	</div>
);

export default EmptyState;
