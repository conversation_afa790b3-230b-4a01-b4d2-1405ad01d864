"use client"
import Image from "next/image";
import Button from "./Button";
import { ArrowAngleIcon, CalendarIcon } from "../common/icons";

interface ArticleCardProps {
	id: string;
	title: string;
	description: string;
	image: string;
	category: string;
	publishedDate: string;
	onReadMore?: (id: string) => void;
}

export default function ArticleCard({
	id,
	title,
	description,
	image,
	category,
	publishedDate,
	onReadMore,
}: ArticleCardProps) {
	const handleReadMore = () => {
		onReadMore?.(id);
	};

	return (
		<div
			className="bg-white rounded-[20px] overflow-hidden transition-shadow duration-300 cursor-pointer group px-3 py-3 sm:px-5 sm:py-5 w-full"
			style={{ boxShadow: "0px 0px 16px 4px #DFDFDF40" }}
			onClick={handleReadMore}>
			{/* Article Image with Category Badge */}
			<div className="relative h-[140px] sm:h-[200px] overflow-hidden rounded-[10px] mb-4 sm:mb-6">
				<Image
					src={image}
					alt={title}
					className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
					fill
					priority
				/>

				{/* Category Badge - positioned at bottom left with 10px margin */}
				<div className="absolute bottom-[10px] left-[10px] sm:bottom-[16px] sm:left-[16px]">
					<span className="bg-neutral-100 backdrop-blur-sm text-neutral-600 px-2 sm:px-3 py-0.5 sm:py-1 rounded-full text-xs sm:text-base leading-[18px] sm:leading-[26px] font-normal">
						{category}
					</span>
				</div>
			</div>

			{/* Article Content */}
			<div>
				{/* Title */}
				<h3 className="text-base sm:text-[20px] sm:leading-[32px] font-semibold text-neutral-600 mb-1.5 sm:mb-2.5 group-hover:text-[#335CFF] transition-colors duration-200">
					{title}
				</h3>

				{/* Description */}
				<p className="text-neutral-400 text-xs sm:text-[16px] sm:leading-[26px] mb-1.5 sm:mb-2.5 line-clamp-2">
					{description}
				</p>

				{/* Meta Information */}
				<div className="flex flex-col items-start justify-between gap-3 sm:gap-5">
					{/* Published Date */}
					<div className="flex items-center gap-2 text-sm sm:text-lg leading-[18px] sm:leading-[24px] text-neutral-600">
						<CalendarIcon className="w-4 h-4 sm:w-6 sm:h-6" />
						<span>{publishedDate}</span>
					</div>

					{/* Read More Button */}
					<Button
						variant="primary"
						size="sm"
						rightIcon={
							<ArrowAngleIcon color="#fff" className="w-4 h-4 sm:w-5 sm:h-5" />
						}
						onClick={(e) => {
							e.stopPropagation();
							handleReadMore();
						}}
						className="w-full sm:w-auto hidden md:flex ">
						Read article
					</Button>
					<Button
						variant="primary"
						size="xs"
						rightIcon={
							<ArrowAngleIcon color="#fff" className="w-4 h-4 sm:w-5 sm:h-5" />
						}
						onClick={(e) => {
							e.stopPropagation();
							handleReadMore();
						}}
						className="md:w-full w-fit sm:w-auto md:hidden">
						Read article
					</Button>
				</div>
			</div>
		</div>
	);
}
