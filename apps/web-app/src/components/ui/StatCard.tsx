import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
    label?: string;
  };
  color?: 'blue' | 'purple' | 'orange' | 'green' | 'red' | 'gray';
  className?: string;
}

const StatCard = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  color = 'blue',
  className
}: StatCardProps) => {
  const colorStyles = {
    blue: {
      bg: "bg-gradient-to-br from-blue-50 to-blue-100",
      icon: "text-blue-600",
      value: "text-blue-900",
      trend: "text-blue-600"
    },
    purple: {
      bg: "bg-gradient-to-br from-purple-50 to-purple-100",
      icon: "text-purple-600",
      value: "text-purple-900",
      trend: "text-purple-600"
    },
    orange: {
      bg: "bg-gradient-to-br from-orange-50 to-orange-100",
      icon: "text-orange-600",
      value: "text-orange-900",
      trend: "text-orange-600"
    },
    green: {
      bg: "bg-gradient-to-br from-green-50 to-green-100",
      icon: "text-green-600",
      value: "text-green-900",
      trend: "text-green-600"
    },
    red: {
      bg: "bg-gradient-to-br from-red-50 to-red-100",
      icon: "text-red-600",
      value: "text-red-900",
      trend: "text-red-600"
    },
    gray: {
      bg: "bg-gradient-to-br from-brandGray-50 to-brandGray-100",
      icon: "text-brandGray-600",
      value: "text-brandGray-900",
      trend: "text-brandGray-600"
    }
  };

  const styles = colorStyles[color];

  return (
    <div className={cn(
      "rounded-xl p-6 border border-white/20 shadow-sm hover:shadow-md transition-all duration-200",
      styles.bg,
      className
    )}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-brandGray-600 mb-1">
            {title}
          </p>
          <p className={cn("text-3xl font-bold mb-1", styles.value)}>
            {typeof value === 'number' ? value.toLocaleString() : value}
          </p>
          {subtitle && (
            <p className="text-sm text-brandGray-500">
              {subtitle}
            </p>
          )}
          {trend && (
            <div className="flex items-center mt-2">
              <span className={cn(
                "inline-flex items-center text-xs font-medium",
                trend.isPositive ? "text-green-600" : "text-red-600"
              )}>
                {trend.isPositive ? (
                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
                {trend.value}%
              </span>
              {trend.label && (
                <span className="text-xs text-brandGray-500 ml-1">
                  {trend.label}
                </span>
              )}
            </div>
          )}
        </div>
        {icon && (
          <div className={cn("p-3 rounded-lg bg-white/50", styles.icon)}>
            {icon}
          </div>
        )}
      </div>
    </div>
  );
};

export default StatCard;
