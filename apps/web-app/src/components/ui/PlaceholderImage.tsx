interface PlaceholderImageProps {
  width: number
  height: number
  className?: string
  alt?: string
  text?: string
}

export default function PlaceholderImage({ 
  width, 
  height, 
  className = '', 
  text 
}: PlaceholderImageProps) {
  return (
    <div 
      className={`bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center text-gray-500 ${className}`}
      style={{ width, height }}
    >
      <div className="text-center">
        <div className="text-2xl mb-2">📷</div>
        {text && <div className="text-sm font-medium">{text}</div>}
        <div className="text-xs opacity-75">{width} × {height}</div>
      </div>
    </div>
  )
}
