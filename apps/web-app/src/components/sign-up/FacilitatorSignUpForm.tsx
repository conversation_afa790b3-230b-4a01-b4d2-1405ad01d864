"use client";

import Button from "@ui/ui/Button";
import {
	AfricaSkillzLogoColoredIcon,
	AfricaSkillzLogoIcon,
	CaretIcon,
	RoundCheckIcon,
} from "@ui/icons";
import Image from "next/image";
import { useState } from "react";
import { Eye, EyeOff } from "lucide-react";
import Modal from "../ui/Modal";
import { EditIcon } from "../common/icons";
import { SendFeedback } from "@/lib/feedback";
import PricingSection from "../PricingSection";
import PaymentSuccessPage from "./PaymentSuccessPage";

function StepOne({ onContinue }: { onContinue: () => void }) {
	return (
		<main className="w-full grid grid-cols-[40%_60%]">
			<div className="p-[62px] bg-gradient-to-b from-[#335CFF] to-[#2441B5] h-screen">
				<div className="w-[455px]">
					<div>
						<AfricaSkillzLogoIcon className="w-[116px] h-[42px]" />
					</div>

					<div className="flex flex-col gap-8 mt-[77px]">
						<h1 className="font-bold text-[24px] leading-[32px] text-[#FAFAFA]">
							Join AfricaSkillz as a Recruiter
						</h1>

						<div className="flex flex-col gap-4 ">
							<div className="flex items-start gap-5">
								<RoundCheckIcon />
								<p className="font-regular text-base text-[#FAFAFA]">
									Access to a diverse pool of qualified African talent across
									various industries
								</p>
							</div>
							<div className="flex items-start gap-5">
								<RoundCheckIcon />
								<p className="font-regular text-base text-[#FAFAFA]">
									Advanced search and filtering tools to find the perfect
									candidates
								</p>
							</div>
							<div className="flex items-start gap-5">
								<RoundCheckIcon />
								<p className="font-regular text-base text-[#FAFAFA]">
									Streamlined communication with potential candidates
								</p>
							</div>
							<div className="flex items-start gap-5">
								<RoundCheckIcon />
								<p className="font-regular text-base text-[#FAFAFA]">
									Comprehensive dashboard to manage all your recruitment
									activities
								</p>
							</div>
							<div className="flex items-start gap-5">
								<RoundCheckIcon />
								<p className="font-regular text-base text-[#FAFAFA]">
									Analytics and reporting to track your recruitment performance
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div className="w-full h-screen bg-white relative">
				<div className="relative w-[352px] h-[409px] rounded-[20px] mt-[150px] m-auto">
					<Image src="/facilitator-signup.png" fill alt="sign up avatar" />
				</div>

				<div className="mt-10 max-w-[354px] m-auto text-center z-30 relative">
					<p className="font-regular text-[14px] leading-[26px] text-[#27272A]">
						Share role openings, corporate notices, and procurements
						informations, all in one place.
					</p>

					<Button
						variant="primary"
						className="mt-10"
						size="md"
						rightIcon={<CaretIcon />}
						onClick={onContinue}>
						Continue as Facilitator
					</Button>
				</div>

				<div className="p-4 rounded-2xl w-fit shadow-lg absolute top-[35%] left-[17%] bg-white">
					<svg
						width="40"
						height="40"
						viewBox="0 0 40 40"
						fill="none"
						xmlns="http://www.w3.org/2000/svg">
						<path
							opacity="0.2"
							d="M36.2444 12.6453L34.0179 31.3953C33.9801 31.7002 33.8322 31.9808 33.602 32.1843C33.3718 32.3878 33.0751 32.5 32.7679 32.5H7.24285C6.93561 32.5 6.63895 32.3878 6.40872 32.1843C6.1785 31.9808 6.03059 31.7002 5.99285 31.3953L3.76629 12.6453C3.7457 12.4694 3.76273 12.2911 3.81625 12.1222C3.86978 11.9534 3.95859 11.7978 4.07678 11.6659C4.19497 11.534 4.33986 11.4287 4.50183 11.357C4.6638 11.2852 4.83916 11.2488 5.01629 11.25H34.9913C35.1687 11.2483 35.3444 11.2845 35.5067 11.356C35.6691 11.4275 35.8144 11.5327 35.9329 11.6647C36.0514 11.7967 36.1405 11.9524 36.1942 12.1215C36.2479 12.2905 36.265 12.4691 36.2444 12.6453Z"
							fill="#335CFF"
						/>
						<path
							d="M36.8748 10.8438C36.6378 10.5777 36.347 10.3649 36.0216 10.2194C35.6962 10.074 35.3437 9.99919 34.9873 10H27.4998C27.4998 8.01088 26.7097 6.10322 25.3031 4.6967C23.8966 3.29018 21.989 2.5 19.9998 2.5C18.0107 2.5 16.1031 3.29018 14.6965 4.6967C13.29 6.10322 12.4998 8.01088 12.4998 10H5.01234C4.65806 10.001 4.30796 10.0766 3.98489 10.222C3.66181 10.3674 3.37301 10.5792 3.13734 10.8438C2.90363 11.1073 2.72826 11.4172 2.6227 11.7533C2.51715 12.0893 2.4838 12.4439 2.52484 12.7937L4.75297 31.5438C4.82523 32.1547 5.1202 32.7175 5.58145 33.1245C6.04269 33.5316 6.63781 33.7543 7.25296 33.75H32.7608C33.3759 33.7543 33.9711 33.5316 34.4323 33.1245C34.8935 32.7175 35.1885 32.1547 35.2608 31.5438L37.4889 12.7937C37.5297 12.4438 37.4961 12.0892 37.3903 11.7531C37.2845 11.4171 37.1088 11.1072 36.8748 10.8438ZM19.9998 5C21.3259 5 22.5977 5.52678 23.5354 6.46447C24.4731 7.40215 24.9998 8.67392 24.9998 10H14.9998C14.9998 8.67392 15.5266 7.40215 16.4643 6.46447C17.402 5.52678 18.6738 5 19.9998 5ZM32.7748 31.25C32.7703 31.2517 32.7653 31.2517 32.7608 31.25H7.2264L5.01234 12.5H34.9998L32.7748 31.25Z"
							fill="#335CFF"
						/>
					</svg>

					<p className="font-bold text-[18px] leading-[24px] text-[#18181B] mt-2">
						Manage Procurements
					</p>
				</div>
				<div className="p-4 rounded-2xl w-fit shadow-lg absolute top-[47%] right-[20%] bg-white">
					<span className="px-3 py-1 rounded-[24px] bg-[#D25625] font-medium text-[12px] leading-[16px] text-[#EBEFFF]">
						Announce
					</span>
					<p className="font-bold text-[18px] leading-[24px] text-[#18181B] mt-2">
						Corporate Notices
					</p>
				</div>
				<div className="p-4 rounded-2xl shadow-lg absolute top-[25%] right-[20%] bg-white w-fit max-w-[185px]">
					<div className="py-3 px-3.5 rounded-[6px] bg-[#335CFF]">
						<p className="font-bold text-[18px] leading-[24px] text-[#EBEFFF] mt-2">
							Connect Talents with Opportunities
						</p>
					</div>
				</div>
				<div className="w-full h-[300px] bg-gradient-to-t from-[#A1B4FF] via-transparent to-transparent  absolute bottom-0 z-10"></div>
			</div>
		</main>
	);
}

function StepTwo({
	onContinue,
	formData,
	setFormData,
	logo,
	setLogo,
}: {
	onContinue: () => void;
	formData: {
		companyName: string;
		representativeName: string;
		email: string;
		password: string;
		confirmPassword: string;
	};
	setFormData: React.Dispatch<
		React.SetStateAction<{
			companyName: string;
			representativeName: string;
			email: string;
			password: string;
			confirmPassword: string;
		}>
	>;
	logo: string | null;
	setLogo: (logo: string | null) => void;
}) {
	const [showPassword, setShowPassword] = useState(false);
	const [showConfirmPassword, setShowConfirmPassword] = useState(false);
	const [modalOpen, setModalOpen] = useState(false);
	const [tempLogo, setTempLogo] = useState<string | null>(null);

	// const handleInputChange = (field: string, value: string) => {
	// 	setFormData((prev) => ({ ...prev, [field]: value }));
	// };

	// const handleSignup = (e: React.FormEvent) => {
	// 	e.preventDefault();
	// 	// Handle signup logic here
	// 	console.log("Form submitted:", formData);
	// };

	const handleImageClick = () => {
		setTempLogo(logo);
		setModalOpen(true);
	};

	const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (file) {
			const reader = new FileReader();
			reader.onload = (ev) => {
				setTempLogo(ev.target?.result as string);
			};
			reader.readAsDataURL(file);
		}
	};

	const handleSaveLogo = () => {
		setLogo(tempLogo);
		setModalOpen(false);
		SendFeedback("Logo uploaded successfully", "success");
	};

	const handleCancelLogo = () => {
		setTempLogo(logo);
		setModalOpen(false);
	};

	return (
		<main className="w-full grid grid-cols-[40%_60%]">
			<div className="p-[62px] bg-gradient-to-b from-[#335CFF] to-[#2441B5] h-screen">
				<div className="w-[455px]">
					<div>
						<AfricaSkillzLogoIcon className="w-[116px] h-[42px]" />
					</div>

					<div className="flex flex-col gap-8 mt-[77px]">
						<h1 className="font-bold text-[24px] leading-[32px] text-[#FAFAFA]">
							Join AfricaSkillz as a Recruiter
						</h1>

						<div className="flex flex-col gap-4 ">
							<div className="flex items-start gap-5">
								<RoundCheckIcon />
								<p className="font-regular text-base text-[#FAFAFA]">
									Access to a diverse pool of qualified African talent across
									various industries
								</p>
							</div>
							<div className="flex items-start gap-5">
								<RoundCheckIcon />
								<p className="font-regular text-base text-[#FAFAFA]">
									Advanced search and filtering tools to find the perfect
									candidates
								</p>
							</div>
							<div className="flex items-start gap-5">
								<RoundCheckIcon />
								<p className="font-regular text-base text-[#FAFAFA]">
									Streamlined communication with potential candidates
								</p>
							</div>
							<div className="flex items-start gap-5">
								<RoundCheckIcon />
								<p className="font-regular text-base text-[#FAFAFA]">
									Comprehensive dashboard to manage all your recruitment
									activities
								</p>
							</div>
							<div className="flex items-start gap-5">
								<RoundCheckIcon />
								<p className="font-regular text-base text-[#FAFAFA]">
									Analytics and reporting to track your recruitment performance
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div className="w-full h-screen bg-white flex flex-col items-center justify-center relative">
				<form className="w-full max-w-[400px] bg-transparent rounded-lg p-8 shadow-none flex flex-col gap-5 z-30">
					<p className="font-bold text-[24px] leading-[32px] text-[#18181B]">
						Register as a Recruiter
					</p>
					<div className="flex flex-col gap-4 ">
						<div className="flex flex-col gap-1">
							<label
								className="font-medium text-[14px] leading-[22px] text-[#3F3F46]"
								htmlFor="">
								Company&apos;s Logo
							</label>
							<div className="relative w-[100px] h-[100px]">
								<button
									type="button"
									onClick={handleImageClick}
									className="focus:outline-none w-full h-full">
									{logo ? (
										<>
											<Image
												src={logo}
												alt="Company Logo"
												width={100}
												height={100}
												priority
												className="w-[100px] h-[100px] object-cover rounded-lg"
											/>
											{/* Overlay with edit icon */}
											<span className="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 hover:opacity-100 transition-opacity rounded-lg cursor-pointer">
												<svg
													width="32"
													height="32"
													viewBox="0 0 24 24"
													fill="none"
													xmlns="http://www.w3.org/2000/svg">
													<path
														d="M21.375 14.25V19.5C21.375 19.9973 21.1775 20.4742 20.8258 20.8258C20.4742 21.1775 19.9973 21.375 19.5 21.375H4.5C4.00272 21.375 3.52581 21.1775 3.17417 20.8258C2.82254 20.4742 2.625 19.9973 2.625 19.5V14.25C2.625 13.9516 2.74353 13.6655 2.9545 13.4545C3.16548 13.2435 3.45163 13.125 3.75 13.125C4.04837 13.125 4.33452 13.2435 4.5455 13.4545C4.75647 13.6655 4.875 13.9516 4.875 14.25V19.125H19.125V14.25C19.125 13.9516 19.2435 13.6655 19.4545 13.4545C19.6655 13.2435 19.9516 13.125 20.25 13.125C20.5484 13.125 20.8345 13.2435 21.0455 13.4545C21.2565 13.6655 21.375 13.9516 21.375 14.25ZM9.04594 8.29594L10.875 6.46875V14.25C10.875 14.5484 10.9935 14.8345 11.2045 15.0455C11.4155 15.2565 11.7016 15.375 12 15.375C12.2984 15.375 12.5845 15.2565 12.7955 15.0455C13.0065 14.8345 13.125 14.5484 13.125 14.25V6.46875L14.9541 8.29875C15.1654 8.5101 15.4521 8.62883 15.7509 8.62883C16.0498 8.62883 16.3365 8.5101 16.5478 8.29875C16.7592 8.08741 16.8779 7.80076 16.8779 7.50188C16.8779 7.20299 16.7592 6.91635 16.5478 6.705L12.7978 2.955C12.6933 2.85012 12.5691 2.76691 12.4324 2.71012C12.2956 2.65334 12.149 2.62411 12.0009 2.62411C11.8529 2.62411 11.7063 2.65334 11.5695 2.71012C11.4328 2.76691 11.3086 2.85012 11.2041 2.955L7.45406 6.705C7.34942 6.80965 7.2664 6.93388 7.20977 7.07061C7.15314 7.20734 7.12399 7.35388 7.12399 7.50188C7.12399 7.80076 7.24272 8.08741 7.45406 8.29875C7.66541 8.5101 7.95205 8.62883 8.25094 8.62883C8.54982 8.62883 8.83647 8.5101 9.04781 8.29875L9.04594 8.29594Z"
														fill="#FAFAFA"
													/>
												</svg>
											</span>
										</>
									) : (
										<Image
											src="/image-upload-img.png"
											width={100}
											height={100}
											alt="image selector"
											priority
											className="cursor-pointer"
										/>
									)}
								</button>
							</div>
						</div>
						<div className="flex flex-col gap-1">
							<label
								className="font-medium text-[14px] leading-[22px] text-[#3F3F46]"
								htmlFor="companyName">
								Company&apos;s Name
							</label>
							<div>
								<input
									id="companyName"
									type="text"
									value={formData.companyName}
									onChange={(e) =>
										setFormData((prev) => ({
											...prev,
											companyName: e.target.value,
										}))
									}
									placeholder="e.g Alaska Nig. Enterprise"
									className="w-full px-3 pr-4 py-2.5 border border-[#E4E4E7] rounded-lg bg-[#FAFAFA] text-base text-[#18181B] focus:outline-none focus:ring-2 focus:ring-[#3F3F46] placeholder:text-[#A1A1AA] placeholder:font-regular placeholder:text-[14px] placeholder:leading-[22px] placeholder:opacity-100"
								/>
							</div>
						</div>
						<div className="flex flex-col gap-1">
							<label
								className="font-medium text-[14px] leading-[22px] text-[#3F3F46]"
								htmlFor="representativeName">
								Representative Name
							</label>
							<div>
								<input
									id="representativeName"
									type="text"
									value={formData.representativeName}
									onChange={(e) =>
										setFormData((prev) => ({
											...prev,
											representativeName: e.target.value,
										}))
									}
									placeholder="e.g Johnson Sandbox"
									className="w-full px-3 pr-4 py-2.5 border border-[#E4E4E7] rounded-lg bg-[#FAFAFA] text-base text-[#18181B] focus:outline-none focus:ring-2 focus:ring-[#3F3F46] placeholder:text-[#A1A1AA] placeholder:font-regular placeholder:text-[14px] placeholder:leading-[22px] placeholder:opacity-100"
								/>
							</div>
						</div>
						<div className="flex flex-col gap-1">
							<label
								className="font-medium text-[14px] leading-[22px] text-[#3F3F46]"
								htmlFor="email">
								Email Address
							</label>
							<div>
								<input
									id="email"
									type="email"
									value={formData.email}
									onChange={(e) =>
										setFormData((prev) => ({ ...prev, email: e.target.value }))
									}
									placeholder="e.g <EMAIL>"
									className="w-full px-3 pr-4 py-2.5 border border-[#E4E4E7] rounded-lg bg-[#FAFAFA] text-base text-[#18181B] focus:outline-none focus:ring-2 focus:ring-[#3F3F46] placeholder:text-[#A1A1AA] placeholder:font-regular placeholder:text-[14px] placeholder:leading-[22px] placeholder:opacity-100"
								/>
							</div>
						</div>
						<div className="flex flex-col gap-1">
							<label
								className="font-medium text-[14px] leading-[22px] text-[#3F3F46]"
								htmlFor="password">
								Create Password
							</label>
							<div className="relative">
								<input
									id="password"
									type={showPassword ? "text" : "password"}
									autoComplete="new-password"
									required
									value={formData.password}
									onChange={(e) =>
										setFormData((prev) => ({
											...prev,
											password: e.target.value,
										}))
									}
									placeholder="Enter password"
									className="w-full px-3 pr-4 py-2.5 border border-[#E4E4E7] rounded-lg bg-[#FAFAFA] text-base text-[#18181B] focus:outline-none focus:ring-2 focus:ring-[#3F3F46] placeholder:text-[#A1A1AA] placeholder:font-regular placeholder:text-[14px] placeholder:leading-[22px] placeholder:opacity-100"
								/>
								<button
									type="button"
									className="absolute right-3 top-1/2 -translate-y-1/2 text-[#A1A1AA]"
									onClick={() => setShowPassword((v) => !v)}
									tabIndex={-1}>
									{showPassword ? (
										<EyeOff className="w-5 h-5" />
									) : (
										<Eye className="w-5 h-5" />
									)}
								</button>
							</div>
						</div>
						<div className="flex flex-col gap-1">
							<label
								className="font-medium text-[14px] leading-[22px] text-[#3F3F46]"
								htmlFor="confirm-password">
								Confirm Password
							</label>
							<div className="relative">
								<input
									id="confirm-password"
									type={showConfirmPassword ? "text" : "password"}
									autoComplete="new-password"
									required
									value={formData.confirmPassword}
									onChange={(e) =>
										setFormData((prev) => ({
											...prev,
											confirmPassword: e.target.value,
										}))
									}
									placeholder="Enter password"
									className="w-full px-3 pr-4 py-2.5 border border-[#E4E4E7] rounded-lg bg-[#FAFAFA] text-base text-[#18181B] focus:outline-none focus:ring-2 focus:ring-[#3F3F46] placeholder:text-[#A1A1AA] placeholder:font-regular placeholder:text-[14px] placeholder:leading-[22px] placeholder:opacity-100"
								/>
								<button
									type="button"
									className="absolute right-3 top-1/2 -translate-y-1/2 text-[#A1A1AA]"
									onClick={() => setShowConfirmPassword((v) => !v)}
									tabIndex={-1}>
									{showConfirmPassword ? (
										<EyeOff className="w-5 h-5" />
									) : (
										<Eye className="w-5 h-5" />
									)}
								</button>
							</div>
						</div>
					</div>

					<Button
						variant="primary"
						size="md"
						onClick={onContinue}
						type="button"
						className="z-30">
						Continue
					</Button>
				</form>
				<div className="w-full h-[300px] bg-gradient-to-t from-[#A1B4FF] via-transparent to-transparent  absolute bottom-0 z-20"></div>

				<Modal
					open={modalOpen}
					onClose={handleCancelLogo}
					className="w-[596px]">
					<div className="p-5">
						<div className="mb-6">
							<h2 className="text-[18px] leading-[24px] font-semibold text-[#27272A]">
								Company&apos;s Logo
							</h2>
						</div>

						<div className="flex flex-col items-center">
							<div className="w-[150px] h-[150px] p-2 bg-gray-100 rounded-xl flex items-center justify-center mb-6 border-2 border-dashed border-gray-200 relative">
								{tempLogo ? (
									<Image
										src={tempLogo || "/placeholder-logo.png"}
										alt="Preview"
										fill
										priority
										className="object-cover rounded-lg"
									/>
								) : (
									<div className="flex flex-col items-center text-gray-400 relative w-[146px] h-[146px]">
										{/* <ImageIcon className="w-16 h-16 mb-2" />
										<span className="text-sm">No image selected</span> */}
										<Image
											src="/placeholder-logo.png"
											alt="Preview"
											fill
											priority
											className=" object-cover rounded-lg"
										/>
									</div>
								)}
							</div>

							<label className="flex items-center gap-1 px-2 py-1 rounded-[8px] border border-gray-300 bg-white font-normal text-[12px] leading-[16px] text-[#71717A] cursor-pointer hover:bg-gray-50 transition-colors mb-8">
								Change Image
								<EditIcon className="w-4 h-4" />
								<input
									type="file"
									accept="image/*"
									className="hidden"
									onChange={handleImageChange}
								/>
							</label>
						</div>

						<div className="flex justify-between items-center pt-5 border-t border-gray-200">
							<button
								type="button"
								className="text-[#71717A] text-sm font-medium hover:text-gray-700 transition-colors cursor-pointer"
								onClick={handleCancelLogo}>
								Cancel
							</button>
							<Button
								variant="outline"
								size="xs"
								onClick={handleSaveLogo}
								// className="bg-[#335CFF] hover:bg-[#2441B5] text-white px-6 py-2 rounded-lg"
							>
								Save
							</Button>
						</div>
					</div>
				</Modal>
			</div>
		</main>
	);
}

function StepThree({
	email,
	onContinue,
}: {
	email: string;
	onContinue: () => void;
}) {
	return (
		<main className="w-full grid grid-cols-1 relative">
			<div className="w-full h-screen bg-white  flex flex-col items-center justify-center relative">
				<div>
					<AfricaSkillzLogoColoredIcon className="w-[116px] h-[42px] absolute inset-0 top-8 left-8" />
				</div>
				<div className="w-[494px] bg-white border border-[#E4E4E7] rounded-2xl p-10 text-white relative">
					<div className="flex items-center gap-2">
						<div className="max-w-[336px]">
							<h2 className="font-semibold text-[18px] leading-[24px] text-[#27272A]">
								Verify organization&apos;s mail
							</h2>
							<p className="text-[#71717A] font-medium leading-[22px] ">
								We sent a verification code to the organization&apos;s email,{" "}
								<span className=" text-[#18181B]">{email}</span>
							</p>
						</div>

						<div className="">
							<svg
								width="70"
								height="52"
								viewBox="0 0 70 52"
								fill="none"
								xmlns="http://www.w3.org/2000/svg"
								xmlnsXlink="http://www.w3.org/1999/xlink">
								<rect
									x="0.953125"
									y="5.35803"
									width="66"
									height="46"
									transform="rotate(-4 0.953125 5.35803)"
									fill="url(#pattern0_683_49138)"
								/>
								<defs>
									<pattern
										id="pattern0_683_49138"
										patternContentUnits="objectBoundingBox"
										width="1"
										height="1">
										<use
											xlinkHref="#image0_683_49138"
											transform="matrix(0.00133775 0 0 0.00191939 0.00770662 0)"
										/>
									</pattern>
									<image
										id="image0_683_49138"
										width="736"
										height="521"
										preserveAspectRatio="none"
										xlinkHref="data:image/png;base64,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"
									/>
								</defs>
							</svg>
						</div>
					</div>

					<div className="flex justify-between items-center mt-8">
						<div className="flex gap-2">
							{Array(3)
								.fill(0)
								.map((_, i) => (
									<input
										key={i}
										type="text"
										maxLength={1}
										className="w-[56px] h-[72px] bg-[#F4F4F5] rounded-[8px] text-center text-2xl font-normal text-[#71717A] focus:outline-[#3F3F46]"
									/>
								))}
						</div>
						<div className="text-[#F4F4F5] w-6 h-1 bg-[#F4F4F5]"></div>
						<div className="flex gap-2">
							{Array(3)
								.fill(0)
								.map((_, i) => (
									<input
										key={i + 3}
										type="text"
										maxLength={1}
										className="w-[56px] h-[72px] bg-[#F4F4F5] rounded-[8px] text-center text-2xl font-normal text-[#71717A] focus:outline-[#3F3F46]"
									/>
								))}
						</div>
					</div>

					<button className="flex items-center gap-2 text-[#4C82F7] mt-6">
						<svg
							width="20"
							height="20"
							viewBox="0 0 20 20"
							fill="none"
							xmlns="http://www.w3.org/2000/svg">
							<path
								d="M17.8125 10C17.8127 12.054 17.004 14.0254 15.5614 15.4876C14.1188 16.9498 12.1585 17.785 10.1047 17.8125H10C8.00552 17.8167 6.08591 17.0533 4.63906 15.6805C4.45828 15.5097 4.35273 15.2742 4.34562 15.0256C4.33851 14.7771 4.43044 14.5359 4.60117 14.3551C4.7719 14.1743 5.00746 14.0688 5.25602 14.0616C5.50458 14.0545 5.74578 14.1465 5.92656 14.3172C6.77519 15.1184 7.84111 15.6517 8.99112 15.8505C10.1411 16.0493 11.3242 15.9048 12.3926 15.435C13.4609 14.9653 14.3671 14.191 14.998 13.2092C15.6288 12.2273 15.9563 11.0812 15.9395 9.91428C15.9227 8.74733 15.5623 7.61121 14.9035 6.64789C14.2446 5.68458 13.3165 4.93679 12.2351 4.49798C11.1536 4.05916 9.96684 3.94879 8.82303 4.18064C7.67922 4.41249 6.62911 4.97629 5.80391 5.80157C5.79375 5.81173 5.78438 5.82111 5.77344 5.83048L4.28828 7.18751H5.625C5.87364 7.18751 6.1121 7.28628 6.28791 7.4621C6.46373 7.63791 6.5625 7.87637 6.5625 8.12501C6.5625 8.37365 6.46373 8.61211 6.28791 8.78792C6.1121 8.96374 5.87364 9.06251 5.625 9.06251H1.875C1.62636 9.06251 1.3879 8.96374 1.21209 8.78792C1.03627 8.61211 0.9375 8.37365 0.9375 8.12501V4.37501C0.9375 4.12637 1.03627 3.88791 1.21209 3.7121C1.3879 3.53628 1.62636 3.43751 1.875 3.43751C2.12364 3.43751 2.3621 3.53628 2.53791 3.7121C2.71373 3.88791 2.8125 4.12637 2.8125 4.37501V5.99376L4.49062 4.45782C5.58508 3.36925 6.97748 2.6293 8.49209 2.33136C10.0067 2.03341 11.5756 2.19084 13.0008 2.78376C14.4261 3.37668 15.6437 4.37853 16.5001 5.66284C17.3564 6.94716 17.8131 8.45638 17.8125 10Z"
								fill="#335CFF"
							/>
						</svg>

						<span className="font-medium">Resend code</span>
					</button>

					<Button
						variant="primary"
						className="w-full mt-10 "
						size="md"
						onClick={onContinue}>
						Continue
					</Button>
				</div>
				<div className="w-full h-[300px] bg-gradient-to-t from-[#A1B4FF] via-transparent to-transparent  absolute bottom-0 z-10"></div>
			</div>
		</main>
	);
}

function StepFour({ onContinue }: { onContinue: () => void }) {
	return (
		<main className="w-full h-screen relative bg-white">
			<div className="p-[32px] h-screen z-40">
				<AfricaSkillzLogoColoredIcon className="w-[116px] h-[42px]" />

				<div className="mt-[64px] mx-auto px-[200px] z-40">
					<div className="py-5 ">
						<div className="max-w-[408px]">
							<h2 className="font-semibold text-[18px] leading-[24px] text-[#27272A]">
								Flexible Plans for Every Need
							</h2>
							<p className="text-[#71717A] font-medium leading-[22px] ">
								Choose the plan that works for you, whether you&apos;re a job
								seeker, employer, or educational institution.
							</p>
						</div>
					</div>
					<PricingSection onContinue={onContinue} />
				</div>
			</div>
			{/* <div className="w-full h-[300px] bg-gradient-to-t from-[#A1B4FF] via-transparent to-transparent  absolute bottom-0 "></div> */}
		</main>
	);
}

export default function FacilitatorSignUpForm() {
	const [step, setStep] = useState(1);
	const [formData, setFormData] = useState({
		companyName: "",
		representativeName: "",
		email: "",
		password: "",
		confirmPassword: "",
	});
	const [logo, setLogo] = useState<string | null>(null);
	console.log(step);
	if (step === 1) {
		return <StepOne onContinue={() => setStep(2)} />;
	}

	if (step === 2) {
		return (
			<StepTwo
				onContinue={() => setStep(3)}
				formData={formData}
				setFormData={setFormData}
				logo={logo}
				setLogo={setLogo}
			/>
		);
	}

	if (step === 3) {
		return <StepThree email={formData.email} onContinue={() => setStep(4)} />;
	}
	if (step === 4) {
		return <StepFour onContinue={() => setStep(5)} />;
	}
	if (step === 5) {
		return <PaymentSuccessPage userRole="facilitator" />;
	}

	return <StepOne onContinue={() => setStep(2)} />;
}
