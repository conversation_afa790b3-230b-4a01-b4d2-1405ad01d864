import React from "react";
type IIconProps = {
	baseColor?: string;
} & React.SVGProps<SVGSVGElement>;

export const GlobeIcon = (props: IIconProps) => {
	return (
		<svg
			width="19"
			height="19"
			viewBox="0 0 19 19"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M9.78272 2.1875C8.33644 2.1875 6.92265 2.61637 5.72011 3.41988C4.51758 4.22339 3.58031 5.36544 3.02685 6.70163C2.47338 8.03781 2.32857 9.50811 2.61073 10.9266C2.89288 12.3451 3.58933 13.648 4.612 14.6707C5.63467 15.6934 6.93763 16.3898 8.35612 16.672C9.77461 16.9541 11.2449 16.8093 12.5811 16.2559C13.9173 15.7024 15.0593 14.7651 15.8628 13.5626C16.6663 12.3601 17.0952 10.9463 17.0952 9.5C17.0932 7.56123 16.3221 5.70246 14.9512 4.33154C13.5803 2.96063 11.7215 2.18955 9.78272 2.1875ZM7.92858 12.3125H11.6369C11.2593 13.602 10.6265 14.7643 9.78272 15.6798C8.93897 14.7643 8.30616 13.602 7.92858 12.3125ZM7.67334 11.1875C7.48678 10.0702 7.48678 8.92977 7.67334 7.8125H11.8921C12.0787 8.92977 12.0787 10.0702 11.8921 11.1875H7.67334ZM3.59522 9.5C3.59473 8.92937 3.67353 8.36144 3.82936 7.8125H6.53358C6.36577 8.93124 6.36577 10.0688 6.53358 11.1875H3.82936C3.67353 10.6386 3.59473 10.0706 3.59522 9.5ZM11.6369 6.6875H7.92858C8.30616 5.39797 8.93897 4.2357 9.78272 3.32023C10.6265 4.2357 11.2593 5.39797 11.6369 6.6875ZM13.0319 7.8125H15.7361C16.0483 8.91584 16.0483 10.0842 15.7361 11.1875H13.0319C13.1997 10.0688 13.1997 8.93124 13.0319 7.8125ZM15.2931 6.6875H12.8019C12.5149 5.55794 12.0328 4.48717 11.3774 3.52344C12.2147 3.74845 12.9957 4.14621 13.6701 4.69114C14.3444 5.23606 14.8973 5.9161 15.2931 6.6875ZM8.18803 3.52344C7.53264 4.48717 7.05056 5.55794 6.7635 6.6875H4.27233C4.66814 5.9161 5.221 5.23606 5.89538 4.69114C6.56976 4.14621 7.35071 3.74845 8.18803 3.52344ZM4.27233 12.3125H6.7635C7.05056 13.4421 7.53264 14.5128 8.18803 15.4766C7.35071 15.2515 6.56976 14.8538 5.89538 14.3089C5.221 13.7639 4.66814 13.0839 4.27233 12.3125ZM11.3774 15.4766C12.0328 14.5128 12.5149 13.4421 12.8019 12.3125H15.2931C14.8973 13.0839 14.3444 13.7639 13.6701 14.3089C12.9957 14.8538 12.2147 15.2515 11.3774 15.4766Z"
				fill="#52525B"
			/>
		</svg>
	);
};
export const EditIcon = (props: IIconProps) => {
	return (
		<svg
			width="17"
			height="17"
			viewBox="0 0 17 17"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M14.7069 5.08561L11.9144 2.29249C11.8215 2.1996 11.7113 2.12592 11.5899 2.07565C11.4686 2.02539 11.3385 1.99951 11.2072 1.99951C11.0759 1.99951 10.9458 2.02539 10.8245 2.07565C10.7031 2.12592 10.5929 2.1996 10.5 2.29249L2.79313 9.99999C2.69987 10.0925 2.62593 10.2026 2.5756 10.324C2.52528 10.4453 2.49959 10.5755 2.50001 10.7069V13.5C2.50001 13.7652 2.60536 14.0196 2.7929 14.2071C2.98043 14.3946 3.23479 14.5 3.50001 14.5H6.29313C6.4245 14.5004 6.55464 14.4747 6.67599 14.4244C6.79735 14.3741 6.90748 14.3001 7.00001 14.2069L14.7069 6.49999C14.7998 6.40712 14.8734 6.29687 14.9237 6.17553C14.974 6.05419 14.9999 5.92414 14.9999 5.7928C14.9999 5.66146 14.974 5.5314 14.9237 5.41006C14.8734 5.28872 14.7998 5.17847 14.7069 5.08561ZM6.29313 13.5H3.50001V10.7069L9.00001 5.20686L11.7931 7.99999L6.29313 13.5ZM12.5 7.29249L9.70688 4.49999L11.2069 2.99999L14 5.79249L12.5 7.29249Z"
				fill={props.color ?? "#71717A"}
			/>
		</svg>
	);
};
export const ChartIcon = (props: IIconProps) => {
	return (
		<svg
			width="20"
			height="20"
			viewBox="0 0 20 20"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M18.125 16.25C18.125 16.4158 18.0592 16.5747 17.9419 16.6919C17.8247 16.8092 17.6658 16.875 17.5 16.875H2.5C2.33424 16.875 2.17527 16.8092 2.05806 16.6919C1.94085 16.5747 1.875 16.4158 1.875 16.25V3.75C1.875 3.58424 1.94085 3.42527 2.05806 3.30806C2.17527 3.19085 2.33424 3.125 2.5 3.125C2.66576 3.125 2.82473 3.19085 2.94194 3.30806C3.05915 3.42527 3.125 3.58424 3.125 3.75V11.1227L7.08828 7.65625C7.19613 7.56184 7.33315 7.50737 7.47638 7.50197C7.61961 7.49656 7.76034 7.54055 7.875 7.62656L12.4695 11.0727L17.0883 7.03125C17.1488 6.9713 17.221 6.92437 17.3004 6.89334C17.3797 6.86231 17.4646 6.84784 17.5498 6.85081C17.6349 6.85378 17.7186 6.87414 17.7956 6.91062C17.8726 6.94711 17.9413 6.99896 17.9976 7.06298C18.0538 7.127 18.0963 7.20185 18.1226 7.28292C18.1488 7.36399 18.1582 7.44956 18.1502 7.5344C18.1421 7.61923 18.1168 7.70152 18.0758 7.77622C18.0348 7.85091 17.979 7.91643 17.9117 7.96875L12.9117 12.3438C12.8039 12.4382 12.6669 12.4926 12.5236 12.498C12.3804 12.5034 12.2397 12.4594 12.125 12.3734L7.53047 8.92891L3.125 12.7836V15.625H17.5C17.6658 15.625 17.8247 15.6908 17.9419 15.8081C18.0592 15.9253 18.125 16.0842 18.125 16.25Z"
				fill={props.color ?? "#71717A"}
			/>
		</svg>
	);
};
export const CalculatorIcon = (props: IIconProps) => {
	return (
		<svg
			width="27"
			height="27"
			viewBox="0 0 27 27"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M9.47719 15.1091V13.5001C9.47719 13.2867 9.56195 13.0821 9.71282 12.9312C9.8637 12.7803 10.0683 12.6956 10.2817 12.6956C10.4951 12.6956 10.6997 12.7803 10.8506 12.9312C11.0015 13.0821 11.0862 13.2867 11.0862 13.5001V15.1091C11.0862 15.3225 11.0015 15.5271 10.8506 15.678C10.6997 15.8289 10.4951 15.9136 10.2817 15.9136C10.0683 15.9136 9.8637 15.8289 9.71282 15.678C9.56195 15.5271 9.47719 15.3225 9.47719 15.1091ZM13.4998 15.9136C13.7132 15.9136 13.9178 15.8289 14.0687 15.678C14.2196 15.5271 14.3043 15.3225 14.3043 15.1091V12.6956C14.3043 12.4822 14.2196 12.2776 14.0687 12.1267C13.9178 11.9758 13.7132 11.891 13.4998 11.891C13.2864 11.891 13.0818 11.9758 12.9309 12.1267C12.78 12.2776 12.6953 12.4822 12.6953 12.6956V15.1091C12.6953 15.3225 12.78 15.5271 12.9309 15.678C13.0818 15.8289 13.2864 15.9136 13.4998 15.9136ZM16.7179 15.9136C16.9312 15.9136 17.1359 15.8289 17.2868 15.678C17.4376 15.5271 17.5224 15.3225 17.5224 15.1091V11.891C17.5224 11.6777 17.4376 11.473 17.2868 11.3222C17.1359 11.1713 16.9312 11.0865 16.7179 11.0865C16.5045 11.0865 16.2999 11.1713 16.149 11.3222C15.9981 11.473 15.9134 11.6777 15.9134 11.891V15.1091C15.9134 15.3225 15.9981 15.5271 16.149 15.678C16.2999 15.8289 16.5045 15.9136 16.7179 15.9136ZM22.3495 8.67295V18.3272H23.154C23.3674 18.3272 23.5721 18.412 23.7229 18.5628C23.8738 18.7137 23.9586 18.9184 23.9586 19.1317C23.9586 19.3451 23.8738 19.5497 23.7229 19.7006C23.5721 19.8515 23.3674 19.9363 23.154 19.9363H14.3043V21.6841C14.8411 21.8739 15.2935 22.2473 15.5816 22.7384C15.8697 23.2295 15.9749 23.8066 15.8786 24.3677C15.7823 24.9289 15.4908 25.4379 15.0555 25.8049C14.6202 26.1719 14.0691 26.3732 13.4998 26.3732C12.9304 26.3732 12.3794 26.1719 11.9441 25.8049C11.5088 25.4379 11.2173 24.9289 11.121 24.3677C11.0247 23.8066 11.1299 23.2295 11.418 22.7384C11.7061 22.2473 12.1585 21.8739 12.6953 21.6841V19.9363H3.84554C3.63216 19.9363 3.42753 19.8515 3.27665 19.7006C3.12578 19.5497 3.04102 19.3451 3.04102 19.1317C3.04102 18.9184 3.12578 18.7137 3.27665 18.5628C3.42753 18.412 3.63216 18.3272 3.84554 18.3272H4.65006V8.67295C4.22331 8.67295 3.81405 8.50343 3.51229 8.20167C3.21054 7.89992 3.04102 7.49065 3.04102 7.06391V5.45487C3.04102 5.02812 3.21054 4.61886 3.51229 4.3171C3.81405 4.01535 4.22331 3.84583 4.65006 3.84583H22.3495C22.7763 3.84583 23.1855 4.01535 23.4873 4.3171C23.789 4.61886 23.9586 5.02812 23.9586 5.45487V7.06391C23.9586 7.49065 23.789 7.89992 23.4873 8.20167C23.1855 8.50343 22.7763 8.67295 22.3495 8.67295ZM14.3043 23.9589C14.3043 23.7997 14.2571 23.6442 14.1687 23.5119C14.0803 23.3796 13.9547 23.2765 13.8077 23.2156C13.6607 23.1547 13.4989 23.1388 13.3428 23.1698C13.1868 23.2008 13.0434 23.2775 12.9309 23.39C12.8184 23.5025 12.7418 23.6458 12.7107 23.8019C12.6797 23.958 12.6956 24.1197 12.7565 24.2667C12.8174 24.4137 12.9205 24.5394 13.0528 24.6278C13.1851 24.7162 13.3407 24.7634 13.4998 24.7634C13.7132 24.7634 13.9178 24.6786 14.0687 24.5277C14.2196 24.3769 14.3043 24.1722 14.3043 23.9589ZM4.65006 7.06391H22.3495V5.45487H4.65006V7.06391ZM20.7405 8.67295H6.2591V18.3272H20.7405V8.67295Z"
				fill={props.color ?? "#5C7DFF"}
			/>
		</svg>
	);
};
export const MediaIcon = (props: IIconProps) => {
	return (
		<svg
			width="27"
			height="27"
			viewBox="0 0 27 27"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M22.3487 11.0864H10.8933L21.7453 8.2213C21.848 8.19423 21.9444 8.14709 22.0288 8.0826C22.1132 8.0181 22.184 7.93753 22.2371 7.84553C22.2902 7.75353 22.3246 7.65193 22.3382 7.54658C22.3518 7.44123 22.3445 7.33422 22.3165 7.23174L21.4959 4.21478C21.383 3.80889 21.1147 3.46383 20.7491 3.25433C20.3836 3.04483 19.9502 2.98777 19.5429 3.09549L4.22787 7.13821C4.02395 7.19114 3.83264 7.28423 3.66514 7.41201C3.49764 7.53979 3.35732 7.69971 3.25238 7.88239C3.14686 8.06284 3.07869 8.26265 3.05191 8.46996C3.02514 8.67727 3.0403 8.88784 3.09651 9.08917L3.84471 11.8467C3.84471 11.8608 3.84471 11.8758 3.84471 11.8909V20.7407C3.84471 21.1674 4.01424 21.5767 4.31599 21.8784C4.61774 22.1802 5.02701 22.3497 5.45376 22.3497H21.5442C21.9709 22.3497 22.3802 22.1802 22.6819 21.8784C22.9837 21.5767 23.1532 21.1674 23.1532 20.7407V11.8909C23.1532 11.6775 23.0685 11.4729 22.9176 11.322C22.7667 11.1712 22.5621 11.0864 22.3487 11.0864ZM19.9512 4.65023L20.5546 6.8697L18.2798 7.47309L15.4519 5.83991L19.9512 4.65023ZM13.2445 6.42018L16.0724 8.05335L12.3576 9.03386L9.52966 7.4027L13.2445 6.42018ZM5.25966 10.9074L4.65627 8.68691L7.32125 7.98296L10.1491 9.61815L5.25966 10.9074ZM21.5442 20.7407H5.45376V12.6954H21.5442V20.7407Z"
				fill={props.color ?? "#5C7DFF"}
			/>
		</svg>
	);
};
export const PiggyIcon = (props: IIconProps) => {
	return (
		<svg
			width="27"
			height="27"
			viewBox="0 0 27 27"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M19.9355 12.2933C19.9355 12.532 19.8647 12.7653 19.7321 12.9637C19.5995 13.1622 19.411 13.3169 19.1905 13.4082C18.97 13.4996 18.7273 13.5235 18.4932 13.4769C18.2592 13.4303 18.0441 13.3154 17.8754 13.1466C17.7066 12.9779 17.5917 12.7628 17.5451 12.5287C17.4985 12.2946 17.5224 12.052 17.6138 11.8315C17.7051 11.611 17.8598 11.4225 18.0582 11.2899C18.2567 11.1573 18.49 11.0865 18.7287 11.0865C19.0487 11.0865 19.3557 11.2137 19.582 11.44C19.8083 11.6663 19.9355 11.9732 19.9355 12.2933ZM15.9129 7.06391H11.8903C11.6769 7.06391 11.4722 7.14867 11.3214 7.29955C11.1705 7.45043 11.0857 7.65506 11.0857 7.86843C11.0857 8.0818 11.1705 8.28644 11.3214 8.43731C11.4722 8.58819 11.6769 8.67295 11.8903 8.67295H15.9129C16.1262 8.67295 16.3309 8.58819 16.4817 8.43731C16.6326 8.28644 16.7174 8.0818 16.7174 7.86843C16.7174 7.65506 16.6326 7.45043 16.4817 7.29955C16.3309 7.14867 16.1262 7.06391 15.9129 7.06391ZM25.5671 11.891V15.1091C25.5671 15.7492 25.3128 16.3631 24.8602 16.8158C24.4076 17.2684 23.7937 17.5227 23.1535 17.5227H22.9162L21.2861 22.0863C21.1744 22.3989 20.9689 22.6694 20.6976 22.8606C20.4263 23.0518 20.1025 23.1544 19.7705 23.1543H18.4913C18.1594 23.1544 17.8356 23.0518 17.5643 22.8606C17.293 22.6694 17.0874 22.3989 16.9758 22.0863L16.7827 21.5453H11.0204L10.8273 22.0863C10.7157 22.3989 10.5101 22.6694 10.2388 22.8606C9.96749 23.0518 9.64368 23.1544 9.31176 23.1543H8.03257C7.70065 23.1544 7.37684 23.0518 7.10553 22.8606C6.83421 22.6694 6.62866 22.3989 6.51705 22.0863L5.25295 18.5505C4.05037 17.1894 3.29737 15.4902 3.09683 13.6851C2.8372 13.8215 2.61977 14.0262 2.46805 14.2772C2.31633 14.5282 2.23609 14.8159 2.236 15.1091C2.236 15.3225 2.15123 15.5271 2.00036 15.678C1.84948 15.8289 1.64485 15.9136 1.43147 15.9136C1.2181 15.9136 1.01347 15.8289 0.862592 15.678C0.711715 15.5271 0.626953 15.3225 0.626953 15.1091C0.628182 14.3917 0.869137 13.6952 1.31153 13.1303C1.75393 12.5655 2.3724 12.1647 3.06868 11.9916C3.2484 9.77447 4.25537 7.70601 5.88961 6.19696C7.52385 4.68791 9.66585 3.84865 11.8903 3.84583H22.349C22.5624 3.84583 22.767 3.93059 22.9179 4.08146C23.0688 4.23234 23.1535 4.43697 23.1535 4.65035C23.1535 4.86372 23.0688 5.06835 22.9179 5.21923C22.767 5.37011 22.5624 5.45487 22.349 5.45487H20.1979C21.5331 6.39163 22.5831 7.6794 23.232 9.17578C23.2752 9.27634 23.3175 9.37691 23.3577 9.47747C23.9618 9.52875 24.5245 9.8055 24.9338 10.2527C25.3432 10.6999 25.5693 11.2848 25.5671 11.891ZM23.9581 11.891C23.9581 11.6777 23.8733 11.473 23.7224 11.3222C23.5716 11.1713 23.3669 11.0865 23.1535 11.0865H22.7855C22.6141 11.0867 22.4472 11.0322 22.309 10.9309C22.1708 10.8296 22.0686 10.6868 22.0172 10.5234C21.5562 9.05242 20.6373 7.76729 19.3944 6.85549C18.1515 5.94369 16.6498 5.45294 15.1083 5.45487H11.8903C10.4851 5.45479 9.11023 5.8636 7.93338 6.63142C6.75653 7.39925 5.82853 8.49292 5.26253 9.77907C4.69654 11.0652 4.51701 12.4883 4.74584 13.8747C4.97467 15.2611 5.60197 16.551 6.55125 17.587C6.62384 17.666 6.67991 17.7587 6.71617 17.8596L8.03257 21.5453H9.31176L9.69592 20.4703C9.75169 20.314 9.85439 20.1789 9.98995 20.0833C10.1255 19.9877 10.2873 19.9363 10.4532 19.9363H17.3499C17.5158 19.9363 17.6776 19.9877 17.8132 20.0833C17.9487 20.1789 18.0514 20.314 18.1072 20.4703L18.4913 21.5453H19.7705L21.5918 16.4476C21.6475 16.2914 21.7502 16.1563 21.8858 16.0607C22.0214 15.9651 22.1832 15.9137 22.349 15.9136H23.1535C23.3669 15.9136 23.5716 15.8289 23.7224 15.678C23.8733 15.5271 23.9581 15.3225 23.9581 15.1091V11.891Z"
				fill={props.color ?? "#5C7DFF"}
			/>
		</svg>
	);
};
export const HandshakeIcon = (props: IIconProps) => {
	return (
		<svg
			width="27"
			height="26"
			viewBox="0 0 27 26"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M12.6711 22.0448C12.6276 22.2189 12.5271 22.3734 12.3857 22.4838C12.2443 22.5942 12.0701 22.6542 11.8907 22.6542C11.8229 22.6546 11.7553 22.6464 11.6895 22.6301L8.47146 21.8256C8.38187 21.8031 8.29682 21.7653 8.22004 21.7139L5.80648 20.1049C5.62898 19.9865 5.50579 19.8024 5.46402 19.5932C5.42225 19.3839 5.46531 19.1666 5.58373 18.9891C5.70215 18.8116 5.88623 18.6884 6.09548 18.6467C6.30473 18.6049 6.522 18.648 6.6995 18.7664L8.99641 20.298L12.0797 21.0693C12.1828 21.0943 12.2798 21.1394 12.3654 21.202C12.4509 21.2646 12.5232 21.3435 12.5782 21.4341C12.6331 21.5248 12.6696 21.6254 12.6856 21.7302C12.7015 21.835 12.6966 21.9419 12.6711 22.0448ZM26.015 12.3443C25.949 12.5448 25.844 12.7302 25.7059 12.8899C25.5678 13.0496 25.3994 13.1803 25.2105 13.2745L22.8291 14.4652L17.29 20.0053C17.1917 20.1036 17.0696 20.1746 16.9357 20.2116C16.8018 20.2486 16.6605 20.2503 16.5257 20.2165L10.0896 18.6075C9.99167 18.5829 9.89925 18.5403 9.81702 18.4818L4.23364 14.4954L1.79192 13.2745C1.41031 13.0838 1.12008 12.7492 0.985058 12.3445C0.850031 11.9398 0.881258 11.4981 1.07187 11.1164L3.57092 6.11931C3.76168 5.7377 4.09619 5.44748 4.5009 5.31245C4.9056 5.17742 5.34736 5.20865 5.72905 5.39926L7.94751 6.50548L13.2775 4.98292C13.4221 4.94156 13.5754 4.94156 13.72 4.98292L19.0499 6.50548L21.2684 5.39926C21.6501 5.20865 22.0918 5.17742 22.4965 5.31245C22.9012 5.44748 23.2357 5.7377 23.4265 6.11931L25.9255 11.1164C26.0209 11.305 26.0779 11.5106 26.0932 11.7214C26.1086 11.9321 26.082 12.1439 26.015 12.3443ZM21.371 13.6456L18.6346 8.17285H15.4346L11.0862 12.3966C12.3593 13.2102 14.3555 13.4344 16.1466 11.6293C16.2865 11.4883 16.4736 11.404 16.6718 11.3927C16.8701 11.3814 17.0656 11.4438 17.2206 11.5679L20.6811 14.3405L21.371 13.6456ZM2.51096 11.8354L4.28996 12.7254L6.789 7.72835L5.01 6.83835L2.51096 11.8354ZM19.5336 15.4799L16.747 13.2484C14.7819 14.8574 12.2869 15.0696 10.2193 13.7512C10.0159 13.6218 9.84417 13.4484 9.71681 13.2438C9.58944 13.0391 9.50968 12.8085 9.48342 12.5688C9.45716 12.3292 9.48507 12.0868 9.5651 11.8594C9.64512 11.632 9.7752 11.4255 9.94574 11.2552C9.94816 11.2522 9.95086 11.2495 9.95379 11.2471L14.4651 6.87154L13.4997 6.59599L8.42922 8.04513L5.67675 13.5491L10.6246 17.0839L16.4714 18.5451L19.5336 15.4799ZM24.4855 11.8354L21.9894 6.83835L20.2104 7.72835L22.7095 12.7254L24.4855 11.8354Z"
				fill={props.color ?? "#5C7DFF"}
			/>
		</svg>
	);
};
export const BuildingIcon = (props: IIconProps) => {
	return (
		<svg
			width="27"
			height="26"
			viewBox="0 0 27 26"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M3.04068 10.5865H5.45425V17.0226H3.8452C3.63183 17.0226 3.4272 17.1074 3.27632 17.2583C3.12544 17.4091 3.04068 17.6138 3.04068 17.8272C3.04068 18.0405 3.12544 18.2452 3.27632 18.396C3.4272 18.5469 3.63183 18.6317 3.8452 18.6317H23.1537C23.3671 18.6317 23.5717 18.5469 23.7226 18.396C23.8735 18.2452 23.9582 18.0405 23.9582 17.8272C23.9582 17.6138 23.8735 17.4091 23.7226 17.2583C23.5717 17.1074 23.3671 17.0226 23.1537 17.0226H21.5447V10.5865H23.9582C24.1333 10.5863 24.3035 10.529 24.4431 10.4234C24.5826 10.3177 24.6839 10.1694 24.7316 10.001C24.7792 9.83252 24.7706 9.65314 24.7071 9.49002C24.6436 9.3269 24.5286 9.18895 24.3796 9.09709L13.9208 2.66092C13.7941 2.583 13.6482 2.54175 13.4995 2.54175C13.3507 2.54175 13.2048 2.583 13.0781 2.66092L2.61932 9.09709C2.47031 9.18895 2.35532 9.3269 2.2918 9.49002C2.22829 9.65314 2.21971 9.83252 2.26736 10.001C2.31501 10.1694 2.4163 10.3177 2.55586 10.4234C2.69542 10.529 2.86564 10.5863 3.04068 10.5865ZM7.06329 10.5865H10.2814V17.0226H7.06329V10.5865ZM15.1085 10.5865V17.0226H11.8904V10.5865H15.1085ZM19.9356 17.0226H16.7175V10.5865H19.9356V17.0226ZM13.4995 4.29008L21.1163 8.97742H5.88265L13.4995 4.29008ZM25.5673 21.0452C25.5673 21.2586 25.4825 21.4632 25.3316 21.6141C25.1808 21.765 24.9761 21.8498 24.7628 21.8498H2.23616C2.02279 21.8498 1.81816 21.765 1.66728 21.6141C1.5164 21.4632 1.43164 21.2586 1.43164 21.0452C1.43164 20.8319 1.5164 20.6272 1.66728 20.4764C1.81816 20.3255 2.02279 20.2407 2.23616 20.2407H24.7628C24.9761 20.2407 25.1808 20.3255 25.3316 20.4764C25.4825 20.6272 25.5673 20.8319 25.5673 21.0452Z"
				fill={props.color ?? "#5C7DFF"}
			/>
		</svg>
	);
};
export const ChevronDown = (props: IIconProps) => {
	return (
		<svg
			width="19"
			height="19"
			viewBox="0 0 19 19"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M15.8055 7.64801L10.1805 13.273C10.1283 13.3253 10.0662 13.3668 9.99794 13.3951C9.92966 13.4234 9.85646 13.438 9.78254 13.438C9.70862 13.438 9.63542 13.4234 9.56714 13.3951C9.49885 13.3668 9.43681 13.3253 9.38457 13.273L3.75957 7.64801C3.65402 7.54246 3.59473 7.39931 3.59473 7.25004C3.59473 7.10077 3.65402 6.95762 3.75957 6.85207C3.86512 6.74652 4.00827 6.68723 4.15754 6.68723C4.30681 6.68723 4.44996 6.74652 4.55551 6.85207L9.78254 12.0798L15.0096 6.85207C15.0618 6.79981 15.1239 6.75835 15.1922 6.73007C15.2604 6.70178 15.3336 6.68723 15.4075 6.68723C15.4814 6.68723 15.5546 6.70178 15.6229 6.73007C15.6912 6.75835 15.7532 6.79981 15.8055 6.85207C15.8578 6.90433 15.8992 6.96638 15.9275 7.03466C15.9558 7.10294 15.9704 7.17613 15.9704 7.25004C15.9704 7.32395 15.9558 7.39713 15.9275 7.46542C15.8992 7.5337 15.8578 7.59575 15.8055 7.64801Z"
				fill="#52525B"
			/>
		</svg>
	);
};
export const SuitcaseIcon = (props: IIconProps) => {
	return (
		<svg
			width="24"
			height="24"
			viewBox="0 0 24 24"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M20.25 5.25H16.5V4.5C16.5 3.90326 16.2629 3.33097 15.841 2.90901C15.419 2.48705 14.8467 2.25 14.25 2.25H9.75C9.15326 2.25 8.58097 2.48705 8.15901 2.90901C7.73705 3.33097 7.5 3.90326 7.5 4.5V5.25H3.75C3.35218 5.25 2.97064 5.40804 2.68934 5.68934C2.40804 5.97064 2.25 6.35218 2.25 6.75V18.75C2.25 19.1478 2.40804 19.5294 2.68934 19.8107C2.97064 20.092 3.35218 20.25 3.75 20.25H20.25C20.6478 20.25 21.0294 20.092 21.3107 19.8107C21.592 19.5294 21.75 19.1478 21.75 18.75V6.75C21.75 6.35218 21.592 5.97064 21.3107 5.68934C21.0294 5.40804 20.6478 5.25 20.25 5.25ZM3.75 10.5H20.25V15H3.75V10.5ZM9 4.5C9 4.30109 9.07902 4.11032 9.21967 3.96967C9.36032 3.82902 9.55109 3.75 9.75 3.75H14.25C14.4489 3.75 14.6397 3.82902 14.7803 3.96967C14.921 4.11032 15 4.30109 15 4.5V5.25H9V4.5ZM20.25 6.75V9H3.75V6.75H20.25ZM20.25 18.75H3.75V16.5H20.25V18.75Z"
				fill={props.color ?? "#FAFAFA"}
			/>
		</svg>
	);
};
export const SuitcaseIcon1 = (props: IIconProps) => {
	return (
		<svg
			width="24"
			height="24"
			viewBox="0 0 24 24"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M20.25 5.25H16.5V4.5C16.5 3.90326 16.2629 3.33097 15.841 2.90901C15.419 2.48705 14.8467 2.25 14.25 2.25H9.75C9.15326 2.25 8.58097 2.48705 8.15901 2.90901C7.73705 3.33097 7.5 3.90326 7.5 4.5V5.25H3.75C3.35218 5.25 2.97064 5.40804 2.68934 5.68934C2.40804 5.97064 2.25 6.35218 2.25 6.75V18.75C2.25 19.1478 2.40804 19.5294 2.68934 19.8107C2.97064 20.092 3.35218 20.25 3.75 20.25H20.25C20.6478 20.25 21.0294 20.092 21.3107 19.8107C21.592 19.5294 21.75 19.1478 21.75 18.75V6.75C21.75 6.35218 21.592 5.97064 21.3107 5.68934C21.0294 5.40804 20.6478 5.25 20.25 5.25ZM3.75 10.5H20.25V15H3.75V10.5ZM9 4.5C9 4.30109 9.07902 4.11032 9.21967 3.96967C9.36032 3.82902 9.55109 3.75 9.75 3.75H14.25C14.4489 3.75 14.6397 3.82902 14.7803 3.96967C14.921 4.11032 15 4.30109 15 4.5V5.25H9V4.5ZM20.25 6.75V9H3.75V6.75H20.25ZM20.25 18.75H3.75V16.5H20.25V18.75Z"
				fill={props.color ?? "#FAFAFA"}
			/>
		</svg>
	);
};
export const ProfileIcon1 = (props: IIconProps) => {
	return (
		<svg
			width="20"
			height="20"
			viewBox="0 0 20 20"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M18.0407 16.5624C16.8508 14.5054 15.0172 13.0304 12.8774 12.3312C13.9358 11.7011 14.7582 10.7409 15.2182 9.59821C15.6781 8.45548 15.7503 7.19337 15.4235 6.00568C15.0968 4.81798 14.3892 3.77039 13.4094 3.02378C12.4296 2.27716 11.2318 1.8728 10 1.8728C8.76821 1.8728 7.57044 2.27716 6.59067 3.02378C5.6109 3.77039 4.90331 4.81798 4.57654 6.00568C4.24978 7.19337 4.32193 8.45548 4.78189 9.59821C5.24186 10.7409 6.06422 11.7011 7.12268 12.3312C4.98284 13.0296 3.14925 14.5046 1.9594 16.5624C1.91577 16.6336 1.88683 16.7127 1.87429 16.7953C1.86174 16.8778 1.86585 16.962 1.88638 17.0429C1.9069 17.1238 1.94341 17.1997 1.99377 17.2663C2.04413 17.3328 2.10731 17.3886 2.17958 17.4304C2.25185 17.4721 2.33175 17.499 2.41457 17.5093C2.49738 17.5197 2.58143 17.5134 2.66176 17.4907C2.74209 17.4681 2.81708 17.4296 2.88228 17.3775C2.94749 17.3254 3.00161 17.2608 3.04143 17.1874C4.51331 14.6437 7.11487 13.1249 10 13.1249C12.8852 13.1249 15.4867 14.6437 16.9586 17.1874C16.9985 17.2608 17.0526 17.3254 17.1178 17.3775C17.183 17.4296 17.258 17.4681 17.3383 17.4907C17.4186 17.5134 17.5027 17.5197 17.5855 17.5093C17.6683 17.499 17.7482 17.4721 17.8205 17.4304C17.8927 17.3886 17.9559 17.3328 18.0063 17.2663C18.0566 17.1997 18.0932 17.1238 18.1137 17.0429C18.1342 16.962 18.1383 16.8778 18.1258 16.7953C18.1132 16.7127 18.0843 16.6336 18.0407 16.5624ZM5.62503 7.49993C5.62503 6.63463 5.88162 5.78877 6.36235 5.06931C6.84308 4.34984 7.52636 3.78909 8.32579 3.45796C9.12522 3.12682 10.0049 3.04018 10.8535 3.20899C11.7022 3.3778 12.4818 3.79448 13.0936 4.40634C13.7055 5.01819 14.1222 5.79774 14.291 6.64641C14.4598 7.49508 14.3731 8.37474 14.042 9.17417C13.7109 9.9736 13.1501 10.6569 12.4306 11.1376C11.7112 11.6183 10.8653 11.8749 10 11.8749C8.84009 11.8737 7.72801 11.4124 6.90781 10.5922C6.0876 9.77195 5.62627 8.65987 5.62503 7.49993Z"
				fill={props.color ?? "#71717A"}
			/>
		</svg>
	);
};
export const DollarIcon = (props: IIconProps) => {
	return (
		<svg
			width="20"
			height="20"
			viewBox="0 0 20 20"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M15.625 13.125C15.624 14.1192 15.2285 15.0725 14.5255 15.7755C13.8225 16.4785 12.8692 16.874 11.875 16.875H10.625V18.125C10.625 18.2908 10.5592 18.4497 10.4419 18.5669C10.3247 18.6842 10.1658 18.75 10 18.75C9.83424 18.75 9.67527 18.6842 9.55806 18.5669C9.44085 18.4497 9.375 18.2908 9.375 18.125V16.875H8.125C7.13076 16.874 6.17753 16.4785 5.47449 15.7755C4.77145 15.0725 4.37603 14.1192 4.375 13.125C4.375 12.9592 4.44085 12.8003 4.55806 12.6831C4.67527 12.5658 4.83424 12.5 5 12.5C5.16576 12.5 5.32473 12.5658 5.44194 12.6831C5.55915 12.8003 5.625 12.9592 5.625 13.125C5.625 13.788 5.88839 14.4239 6.35723 14.8928C6.82607 15.3616 7.46196 15.625 8.125 15.625H11.875C12.538 15.625 13.1739 15.3616 13.6428 14.8928C14.1116 14.4239 14.375 13.788 14.375 13.125C14.375 12.462 14.1116 11.8261 13.6428 11.3572C13.1739 10.8884 12.538 10.625 11.875 10.625H8.75C7.75544 10.625 6.80161 10.2299 6.09835 9.52665C5.39509 8.82339 5 7.86956 5 6.875C5 5.88044 5.39509 4.92661 6.09835 4.22335C6.80161 3.52009 7.75544 3.125 8.75 3.125H9.375V1.875C9.375 1.70924 9.44085 1.55027 9.55806 1.43306C9.67527 1.31585 9.83424 1.25 10 1.25C10.1658 1.25 10.3247 1.31585 10.4419 1.43306C10.5592 1.55027 10.625 1.70924 10.625 1.875V3.125H11.25C12.2442 3.12603 13.1975 3.52145 13.9005 4.22449C14.6035 4.92753 14.999 5.88076 15 6.875C15 7.04076 14.9342 7.19973 14.8169 7.31694C14.6997 7.43415 14.5408 7.5 14.375 7.5C14.2092 7.5 14.0503 7.43415 13.9331 7.31694C13.8158 7.19973 13.75 7.04076 13.75 6.875C13.75 6.21196 13.4866 5.57607 13.0178 5.10723C12.5489 4.63839 11.913 4.375 11.25 4.375H8.75C8.08696 4.375 7.45107 4.63839 6.98223 5.10723C6.51339 5.57607 6.25 6.21196 6.25 6.875C6.25 7.53804 6.51339 8.17393 6.98223 8.64277C7.45107 9.11161 8.08696 9.375 8.75 9.375H11.875C12.8692 9.37603 13.8225 9.77145 14.5255 10.4745C15.2285 11.1775 15.624 12.1308 15.625 13.125Z"
				fill={props.color ?? "#71717A"}
			/>
		</svg>
	);
};
export const HomeIcon = (props: IIconProps) => {
	return (
		<svg
			width="20"
			height="20"
			viewBox="0 0 20 20"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M17.0961 8.10692L10.8461 2.21005C10.843 2.20738 10.8402 2.20451 10.8375 2.20145C10.6074 1.99218 10.3075 1.87622 9.99648 1.87622C9.68545 1.87622 9.38558 1.99218 9.15547 2.20145L9.14688 2.21005L2.90391 8.10692C2.77656 8.22402 2.67491 8.36628 2.60538 8.5247C2.53586 8.68311 2.49997 8.85424 2.5 9.02724V16.2499C2.5 16.5814 2.6317 16.8994 2.86612 17.1338C3.10054 17.3682 3.41848 17.4999 3.75 17.4999H7.5C7.83152 17.4999 8.14946 17.3682 8.38388 17.1338C8.6183 16.8994 8.75 16.5814 8.75 16.2499V12.4999H11.25V16.2499C11.25 16.5814 11.3817 16.8994 11.6161 17.1338C11.8505 17.3682 12.1685 17.4999 12.5 17.4999H16.25C16.5815 17.4999 16.8995 17.3682 17.1339 17.1338C17.3683 16.8994 17.5 16.5814 17.5 16.2499V9.02724C17.5 8.85424 17.4641 8.68311 17.3946 8.5247C17.3251 8.36628 17.2234 8.22402 17.0961 8.10692ZM16.25 16.2499H12.5V12.4999C12.5 12.1684 12.3683 11.8504 12.1339 11.616C11.8995 11.3816 11.5815 11.2499 11.25 11.2499H8.75C8.41848 11.2499 8.10054 11.3816 7.86612 11.616C7.6317 11.8504 7.5 12.1684 7.5 12.4999V16.2499H3.75V9.02724L3.75859 9.01942L10 3.12489L16.2422 9.01786L16.2508 9.02567L16.25 16.2499Z"
				fill={props.color ?? "#335CFF"}
			/>
		</svg>
	);
};
export const PlusIcon = (props: IIconProps) => {
	return (
		<svg
			width="20"
			height="20"
			viewBox="0 0 20 20"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M17.5 10C17.5 10.1658 17.4342 10.3247 17.3169 10.4419C17.1997 10.5592 17.0408 10.625 16.875 10.625H10.625V16.875C10.625 17.0408 10.5592 17.1997 10.4419 17.3169C10.3247 17.4342 10.1658 17.5 10 17.5C9.83424 17.5 9.67527 17.4342 9.55806 17.3169C9.44085 17.1997 9.375 17.0408 9.375 16.875V10.625H3.125C2.95924 10.625 2.80027 10.5592 2.68306 10.4419C2.56585 10.3247 2.5 10.1658 2.5 10C2.5 9.83424 2.56585 9.67527 2.68306 9.55806C2.80027 9.44085 2.95924 9.375 3.125 9.375H9.375V3.125C9.375 2.95924 9.44085 2.80027 9.55806 2.68306C9.67527 2.56585 9.83424 2.5 10 2.5C10.1658 2.5 10.3247 2.56585 10.4419 2.68306C10.5592 2.80027 10.625 2.95924 10.625 3.125V9.375H16.875C17.0408 9.375 17.1997 9.44085 17.3169 9.55806C17.4342 9.67527 17.5 9.83424 17.5 10Z"
				fill={props.color ?? "white"}
			/>
		</svg>
	);
};
export const BellIcon = (props: IIconProps) => {
	return (
		<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
			<path
				d="M17.3272 13.7453C16.8936 12.9984 16.2491 10.8852 16.2491 8.125C16.2491 6.4674 15.5906 4.87769 14.4185 3.70558C13.2464 2.53348 11.6567 1.875 9.99909 1.875C8.34149 1.875 6.75178 2.53348 5.57968 3.70558C4.40757 4.87769 3.74909 6.4674 3.74909 8.125C3.74909 10.8859 3.10378 12.9984 2.67019 13.7453C2.55946 13.9352 2.50076 14.1509 2.50001 14.3707C2.49925 14.5905 2.55647 14.8066 2.66589 14.9973C2.77531 15.1879 2.93306 15.3463 3.12324 15.4565C3.31342 15.5667 3.52929 15.6248 3.74909 15.625H6.93738C7.08157 16.3306 7.46505 16.9647 8.02295 17.4201C8.58085 17.8756 9.27892 18.1243 9.99909 18.1243C10.7193 18.1243 11.4173 17.8756 11.9752 17.4201C12.5331 16.9647 12.9166 16.3306 13.0608 15.625H16.2491C16.4688 15.6247 16.6846 15.5665 16.8747 15.4562C17.0647 15.346 17.2224 15.1875 17.3317 14.9969C17.441 14.8063 17.4982 14.5903 17.4974 14.3705C17.4966 14.1508 17.4379 13.9351 17.3272 13.7453ZM9.99909 16.875C9.61145 16.8749 9.23338 16.7546 8.91691 16.5308C8.60043 16.3069 8.36112 15.9905 8.23191 15.625H11.7663C11.6371 15.9905 11.3978 16.3069 11.0813 16.5308C10.7648 16.7546 10.3867 16.8749 9.99909 16.875ZM3.74909 14.375C4.35066 13.3406 4.99909 10.9438 4.99909 8.125C4.99909 6.79892 5.52588 5.52715 6.46356 4.58947C7.40124 3.65178 8.67301 3.125 9.99909 3.125C11.3252 3.125 12.5969 3.65178 13.5346 4.58947C14.4723 5.52715 14.9991 6.79892 14.9991 8.125C14.9991 10.9414 15.646 13.3383 16.2491 14.375H3.74909Z"
				fill={props.color ?? "#27272A"}
			/>
		</svg>
	);
};
export const CrownIcon = (props: IIconProps) => {
	return (
		<svg
			width="20"
			height="20"
			viewBox="0 0 20 20"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M19.0482 5.95226C18.9072 5.82933 18.7328 5.75107 18.5473 5.72742C18.3617 5.70377 18.1733 5.7358 18.006 5.81945L14.0833 7.77257L10.8083 2.33273C10.7241 2.19314 10.6053 2.07767 10.4634 1.99752C10.3214 1.91736 10.1612 1.87524 9.99817 1.87524C9.83516 1.87524 9.67491 1.91736 9.53297 1.99752C9.39102 2.07767 9.2722 2.19314 9.18801 2.33273L5.91301 7.77491L1.99192 5.82179C1.82499 5.73889 1.63731 5.70714 1.45241 5.73052C1.26751 5.7539 1.09363 5.83137 0.952598 5.95321C0.811565 6.07505 0.70966 6.23583 0.659669 6.41537C0.609679 6.59491 0.61383 6.78522 0.671603 6.96241L3.56223 15.8187C3.59129 15.9076 3.63997 15.9889 3.70466 16.0566C3.76935 16.1242 3.84841 16.1765 3.936 16.2095C4.02359 16.2425 4.11748 16.2554 4.21072 16.2472C4.30396 16.2391 4.39419 16.2101 4.47473 16.1624C4.49426 16.1507 6.49192 14.9999 9.99817 14.9999C13.5044 14.9999 15.5021 16.1507 15.52 16.1616C15.6006 16.2098 15.691 16.2392 15.7845 16.2476C15.8779 16.256 15.9721 16.2433 16.06 16.2103C16.1479 16.1774 16.2272 16.1251 16.2921 16.0573C16.357 15.9895 16.4058 15.9079 16.4349 15.8187L19.3255 6.96476C19.3849 6.78752 19.3903 6.5966 19.3409 6.4163C19.2915 6.236 19.1896 6.07447 19.0482 5.95226ZM15.4669 14.7523C14.5294 14.3546 12.6607 13.7499 9.99817 13.7499C7.33567 13.7499 5.46692 14.3546 4.52942 14.7523L2.08488 7.26554L5.60598 9.02101C5.81911 9.12611 6.06387 9.1476 6.29205 9.08122C6.52023 9.01485 6.71529 8.86544 6.83879 8.66241L9.99817 3.41085L13.1575 8.66085C13.281 8.86368 13.4759 9.01296 13.7039 9.07932C13.9319 9.14568 14.1765 9.12432 14.3896 9.01945L17.9114 7.26554L15.4669 14.7523Z"
				fill={props.color ?? "#335CFF"}
			/>
		</svg>
	);
};
export const GraduationHatIcon = (props: IIconProps) => {
	return (
		<svg
			width="22"
			height="23"
			viewBox="0 0 22 23"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M21.6354 8.14317L11.3229 2.64317C11.2234 2.5902 11.1125 2.5625 10.9998 2.5625C10.8871 2.5625 10.7761 2.5902 10.6767 2.64317L0.36416 8.14317C0.254164 8.20179 0.162173 8.28921 0.0980289 8.39608C0.0338846 8.50295 0 8.62524 0 8.74989C0 8.87453 0.0338846 8.99682 0.0980289 9.10369C0.162173 9.21056 0.254164 9.29798 0.36416 9.3566L2.74979 10.6293V14.7904C2.74907 15.1281 2.87335 15.4541 3.09869 15.7057C4.22447 16.9595 6.74674 19.0624 10.9998 19.0624C12.41 19.074 13.8096 18.8186 15.1248 18.3096V21.1249C15.1248 21.3072 15.1972 21.4821 15.3261 21.611C15.4551 21.74 15.6299 21.8124 15.8123 21.8124C15.9946 21.8124 16.1695 21.74 16.2984 21.611C16.4274 21.4821 16.4998 21.3072 16.4998 21.1249V17.6453C17.3963 17.1278 18.2064 16.4733 18.9009 15.7057C19.1262 15.4541 19.2505 15.1281 19.2498 14.7904V10.6293L21.6354 9.3566C21.7454 9.29798 21.8374 9.21056 21.9015 9.10369C21.9657 8.99682 21.9996 8.87453 21.9996 8.74989C21.9996 8.62524 21.9657 8.50295 21.9015 8.39608C21.8374 8.28921 21.7454 8.20179 21.6354 8.14317ZM10.9998 17.6874C7.28127 17.6874 5.09416 15.8707 4.12478 14.7904V11.3624L10.6767 14.8566C10.7761 14.9096 10.8871 14.9373 10.9998 14.9373C11.1125 14.9373 11.2234 14.9096 11.3229 14.8566L15.1248 12.8293V16.8117C14.042 17.317 12.6773 17.6874 10.9998 17.6874ZM17.8748 14.787C17.4627 15.2443 17.0016 15.655 16.4998 16.0116V12.0954L17.8748 11.3624V14.787ZM16.156 10.7213L16.1371 10.7101L11.3246 8.14317C11.1641 8.06116 10.9777 8.0455 10.8057 8.09957C10.6337 8.15364 10.4898 8.27312 10.4051 8.43225C10.3203 8.59139 10.3015 8.77744 10.3526 8.95034C10.4037 9.12324 10.5207 9.26914 10.6784 9.3566L14.6951 11.4999L10.9998 13.4704L2.14822 8.74989L10.9998 4.02934L19.8513 8.74989L16.156 10.7213Z"
				fill={props.color ?? "#A55DC3"}
			/>
		</svg>
	);
};
export const FilterModernIcon = (props: IIconProps) => {
	return (
		<svg
			width="20"
			height="21"
			viewBox="0 0 20 21"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M15.625 10.5502C15.625 10.7159 15.5592 10.8749 15.4419 10.9921C15.3247 11.1093 15.1658 11.1752 15 11.1752H5C4.83424 11.1752 4.67527 11.1093 4.55806 10.9921C4.44085 10.8749 4.375 10.7159 4.375 10.5502C4.375 10.3844 4.44085 10.2254 4.55806 10.1082C4.67527 9.99102 4.83424 9.92517 5 9.92517H15C15.1658 9.92517 15.3247 9.99102 15.4419 10.1082C15.5592 10.2254 15.625 10.3844 15.625 10.5502ZM18.125 6.17517H1.875C1.70924 6.17517 1.55027 6.24102 1.43306 6.35823C1.31585 6.47544 1.25 6.63441 1.25 6.80017C1.25 6.96593 1.31585 7.1249 1.43306 7.24211C1.55027 7.35932 1.70924 7.42517 1.875 7.42517H18.125C18.2908 7.42517 18.4497 7.35932 18.5669 7.24211C18.6842 7.1249 18.75 6.96593 18.75 6.80017C18.75 6.63441 18.6842 6.47544 18.5669 6.35823C18.4497 6.24102 18.2908 6.17517 18.125 6.17517ZM11.875 13.6752H8.125C7.95924 13.6752 7.80027 13.741 7.68306 13.8582C7.56585 13.9754 7.5 14.1344 7.5 14.3002C7.5 14.4659 7.56585 14.6249 7.68306 14.7421C7.80027 14.8593 7.95924 14.9252 8.125 14.9252H11.875C12.0408 14.9252 12.1997 14.8593 12.3169 14.7421C12.4342 14.6249 12.5 14.4659 12.5 14.3002C12.5 14.1344 12.4342 13.9754 12.3169 13.8582C12.1997 13.741 12.0408 13.6752 11.875 13.6752Z"
				fill={props.color ?? "#27272A"}
			/>
		</svg>
	);
};
export const CheckMarkIcon = (props: IIconProps) => {
	return (
		<svg
			width="24"
			height="24"
			viewBox="0 0 24 24"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M21.5306 7.28061L9.53055 19.2806C9.4609 19.3503 9.37818 19.4057 9.28713 19.4434C9.19609 19.4812 9.09849 19.5006 8.99993 19.5006C8.90137 19.5006 8.80377 19.4812 8.71272 19.4434C8.62168 19.4057 8.53896 19.3503 8.4693 19.2806L3.2193 14.0306C3.07857 13.8899 2.99951 13.699 2.99951 13.5C2.99951 13.301 3.07857 13.1101 3.2193 12.9694C3.36003 12.8286 3.55091 12.7496 3.74993 12.7496C3.94895 12.7496 4.13982 12.8286 4.28055 12.9694L8.99993 17.6897L20.4693 6.21936C20.61 6.07863 20.8009 5.99957 20.9999 5.99957C21.199 5.99957 21.3898 6.07863 21.5306 6.21936C21.6713 6.36009 21.7503 6.55097 21.7503 6.74999C21.7503 6.94901 21.6713 7.13988 21.5306 7.28061Z"
				fill="#FAFAFA"
			/>
		</svg>
	);
};
export const LightningIcon = (props: IIconProps) => {
	return (
		<svg
			width="32"
			height="32"
			viewBox="0 0 32 32"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M26.9738 14.7712C26.9359 14.611 26.8591 14.4625 26.75 14.3391C26.641 14.2157 26.5032 14.1211 26.3488 14.0637L19.1475 11.3625L20.98 2.19625C21.0215 1.98327 20.9926 1.76258 20.8977 1.56747C20.8028 1.37237 20.6469 1.21343 20.4538 1.11465C20.2606 1.01587 20.0405 0.982599 19.8267 1.01987C19.613 1.05713 19.4172 1.16292 19.2688 1.32125L5.26879 16.3212C5.15508 16.4411 5.07283 16.5872 5.02937 16.7465C4.98591 16.9059 4.98261 17.0735 5.01975 17.2345C5.05689 17.3954 5.13332 17.5447 5.24222 17.6689C5.35111 17.7931 5.48908 17.8884 5.64379 17.9462L12.8475 20.6475L11.02 29.8037C10.9785 30.0167 11.0074 30.2374 11.1024 30.4325C11.1973 30.6276 11.3531 30.7866 11.5463 30.8853C11.7395 30.9841 11.9596 31.0174 12.1733 30.9801C12.3871 30.9429 12.5829 30.8371 12.7313 30.6787L26.7313 15.6787C26.8429 15.5589 26.9234 15.4135 26.9657 15.2552C27.008 15.097 27.0108 14.9308 26.9738 14.7712ZM13.6713 26.75L14.98 20.2025C15.0269 19.9703 14.9898 19.729 14.8753 19.5216C14.7608 19.3142 14.5765 19.1542 14.355 19.07L7.75004 16.5887L18.3275 5.25625L17.02 11.8037C16.9732 12.036 17.0103 12.2773 17.1248 12.4847C17.2392 12.6921 17.4236 12.8521 17.645 12.9362L24.245 15.4112L13.6713 26.75Z"
				fill="#A55DC3"
			/>
		</svg>
	);
};

export const SearchIcon = (props: IIconProps) => {
	return (
		<svg
			width="20"
			height="20"
			viewBox="0 0 20 20"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M18.1634 16.8367L14.4533 13.125C15.5657 11.6754 16.085 9.85688 15.906 8.03841C15.7269 6.21993 14.8629 4.53765 13.4891 3.33282C12.1153 2.12799 10.3346 1.49084 8.50834 1.55061C6.68205 1.61038 4.94687 2.3626 3.65479 3.65467C2.36272 4.94675 1.6105 6.68193 1.55073 8.50822C1.49096 10.3345 2.12811 12.1152 3.33294 13.489C4.53777 14.8628 6.22005 15.7268 8.03853 15.9059C9.85701 16.0849 11.6755 15.5656 13.1251 14.4531L16.8384 18.1672C16.9256 18.2544 17.0292 18.3236 17.1431 18.3708C17.257 18.418 17.3792 18.4423 17.5025 18.4423C17.6258 18.4423 17.7479 18.418 17.8619 18.3708C17.9758 18.3236 18.0793 18.2544 18.1665 18.1672C18.2537 18.08 18.3229 17.9765 18.3701 17.8625C18.4173 17.7486 18.4416 17.6265 18.4416 17.5031C18.4416 17.3798 18.4173 17.2577 18.3701 17.1438C18.3229 17.0298 18.2537 16.9263 18.1665 16.8391L18.1634 16.8367ZM3.43764 8.75002C3.43764 7.6993 3.74921 6.67218 4.33295 5.79855C4.9167 4.92491 5.7464 4.244 6.71713 3.84191C7.68786 3.43981 8.75603 3.33461 9.78656 3.53959C10.8171 3.74458 11.7637 4.25054 12.5066 4.99351C13.2496 5.73648 13.7556 6.68307 13.9606 7.7136C14.1655 8.74412 14.0603 9.81229 13.6582 10.783C13.2562 11.7538 12.5752 12.5835 11.7016 13.1672C10.828 13.7509 9.80085 14.0625 8.75014 14.0625C7.34162 14.0611 5.9912 13.5009 4.99523 12.5049C3.99926 11.5089 3.43908 10.1585 3.43764 8.75002Z"
				fill="#A1A1AA"
			/>
		</svg>
	);
};

export const ShareIcon = (props: IIconProps) => {
	return (
		<svg
			width="24"
			height="24"
			viewBox="0 0 24 24"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M16.5002 15.0001C15.9999 14.9999 15.5047 15.1001 15.0438 15.2948C14.5829 15.4895 14.1658 15.7747 13.8171 16.1335L9.49522 13.3557C9.83519 12.4839 9.83519 11.5162 9.49522 10.6444L13.8171 7.86664C14.4663 8.53162 15.3392 8.93187 16.2668 8.98979C17.1943 9.0477 18.1103 8.75915 18.8371 8.18009C19.564 7.60102 20.0499 6.77269 20.2007 5.85569C20.3516 4.93869 20.1565 3.99835 19.6534 3.21698C19.1503 2.43562 18.375 1.86889 17.4778 1.62668C16.5806 1.38447 15.6254 1.48403 14.7975 1.90606C13.9695 2.32809 13.3277 3.04252 12.9966 3.91085C12.6655 4.77917 12.6685 5.73951 13.0052 6.6057L8.68335 9.38351C8.16291 8.84921 7.49496 8.48224 6.76489 8.32952C6.03481 8.1768 5.27578 8.24527 4.58481 8.52617C3.89385 8.80707 3.30235 9.28764 2.88592 9.90645C2.4695 10.5253 2.24707 11.2542 2.24707 12.0001C2.24707 12.746 2.4695 13.4749 2.88592 14.0937C3.30235 14.7125 3.89385 15.1931 4.58481 15.474C5.27578 15.7549 6.03481 15.8233 6.76489 15.6706C7.49496 15.5179 8.16291 15.1509 8.68335 14.6166L13.0052 17.3944C12.7157 18.1412 12.6729 18.961 12.883 19.7339C13.0931 20.5067 13.5451 21.192 14.1729 21.6894C14.8006 22.1867 15.5712 22.47 16.3716 22.4978C17.172 22.5256 17.9603 22.2964 18.621 21.8437C19.2818 21.3911 19.7802 20.7388 20.0434 19.9823C20.3066 19.2259 20.3206 18.4051 20.0836 17.64C19.8465 16.875 19.3707 16.206 18.7259 15.731C18.0811 15.2559 17.3011 14.9998 16.5002 15.0001ZM16.5002 3.00007C16.9452 3.00007 17.3802 3.13203 17.7503 3.37927C18.1203 3.6265 18.4087 3.9779 18.579 4.38904C18.7493 4.80017 18.7938 5.25257 18.707 5.68903C18.6202 6.12548 18.4059 6.5264 18.0912 6.84106C17.7765 7.15573 17.3756 7.37002 16.9392 7.45684C16.5027 7.54366 16.0503 7.4991 15.6392 7.3288C15.2281 7.15851 14.8767 6.87012 14.6294 6.50011C14.3822 6.1301 14.2502 5.69508 14.2502 5.25007C14.2502 4.65334 14.4873 4.08104 14.9092 3.65908C15.3312 3.23713 15.9035 3.00007 16.5002 3.00007ZM6.00023 14.2501C5.55522 14.2501 5.1202 14.1181 4.75019 13.8709C4.38018 13.6236 4.09179 13.2722 3.9215 12.8611C3.7512 12.45 3.70664 11.9976 3.79346 11.5611C3.88028 11.1247 4.09457 10.7238 4.40924 10.4091C4.7239 10.0944 5.12481 9.88012 5.56127 9.79331C5.99773 9.70649 6.45013 9.75105 6.86126 9.92134C7.2724 10.0916 7.6238 10.38 7.87103 10.75C8.11827 11.1201 8.25023 11.5551 8.25023 12.0001C8.25023 12.5968 8.01317 13.1691 7.59122 13.5911C7.16926 14.013 6.59696 14.2501 6.00023 14.2501ZM16.5002 21.0001C16.0552 21.0001 15.6202 20.8681 15.2502 20.6209C14.8802 20.3736 14.5918 20.0222 14.4215 19.6111C14.2512 19.2 14.2066 18.7476 14.2935 18.3111C14.3803 17.8747 14.5946 17.4738 14.9092 17.1591C15.2239 16.8444 15.6248 16.6301 16.0613 16.5433C16.4977 16.4565 16.9501 16.501 17.3613 16.6713C17.7724 16.8416 18.1238 17.13 18.371 17.5C18.6183 17.8701 18.7502 18.3051 18.7502 18.7501C18.7502 19.3468 18.5132 19.9191 18.0912 20.3411C17.6693 20.763 17.097 21.0001 16.5002 21.0001Z"
				fill={props.color ?? "#A1A1AA"}
			/>
		</svg>
	);
};

export const SuitcaseIconNew = (props: IIconProps) => {
	return (
		<svg
			width="20"
			height="19"
			viewBox="0 0 20 19"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M18.25 3.25H14.5V2.5C14.5 1.90326 14.2629 1.33097 13.841 0.90901C13.419 0.487053 12.8467 0.25 12.25 0.25H7.75C7.15326 0.25 6.58097 0.487053 6.15901 0.90901C5.73705 1.33097 5.5 1.90326 5.5 2.5V3.25H1.75C1.35218 3.25 0.970644 3.40804 0.68934 3.68934C0.408035 3.97064 0.25 4.35218 0.25 4.75V16.75C0.25 17.1478 0.408035 17.5294 0.68934 17.8107C0.970644 18.092 1.35218 18.25 1.75 18.25H18.25C18.6478 18.25 19.0294 18.092 19.3107 17.8107C19.592 17.5294 19.75 17.1478 19.75 16.75V4.75C19.75 4.35218 19.592 3.97064 19.3107 3.68934C19.0294 3.40804 18.6478 3.25 18.25 3.25ZM7 2.5C7 2.30109 7.07902 2.11032 7.21967 1.96967C7.36032 1.82902 7.55109 1.75 7.75 1.75H12.25C12.4489 1.75 12.6397 1.82902 12.7803 1.96967C12.921 2.11032 13 2.30109 13 2.5V3.25H7V2.5ZM18.25 4.75V8.65094C15.7185 10.0289 12.8822 10.7505 10 10.75C7.11794 10.7505 4.28165 10.0292 1.75 8.65188V4.75H18.25ZM18.25 16.75H1.75V10.3412C4.31868 11.5977 7.1405 12.2506 10 12.25C12.8596 12.2501 15.6813 11.5969 18.25 10.3403V16.75ZM7.75 8.5C7.75 8.30109 7.82902 8.11032 7.96967 7.96967C8.11032 7.82902 8.30109 7.75 8.5 7.75H11.5C11.6989 7.75 11.8897 7.82902 12.0303 7.96967C12.171 8.11032 12.25 8.30109 12.25 8.5C12.25 8.69891 12.171 8.88968 12.0303 9.03033C11.8897 9.17098 11.6989 9.25 11.5 9.25H8.5C8.30109 9.25 8.11032 9.17098 7.96967 9.03033C7.82902 8.88968 7.75 8.69891 7.75 8.5Z"
				fill="#A1A1AA"
			/>
		</svg>
	);
};

export const ClockIcon = (props: IIconProps) => {
	return (
		<svg
			width="24"
			height="24"
			viewBox="0 0 24 24"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M12 2.25C10.0716 2.25 8.18657 2.82183 6.58319 3.89317C4.97982 4.96451 3.73013 6.48726 2.99218 8.26884C2.25422 10.0504 2.06114 12.0108 2.43735 13.9021C2.81355 15.7934 3.74215 17.5307 5.10571 18.8943C6.46928 20.2579 8.20656 21.1865 10.0979 21.5627C11.9892 21.9389 13.9496 21.7458 15.7312 21.0078C17.5127 20.2699 19.0355 19.0202 20.1068 17.4168C21.1782 15.8134 21.75 13.9284 21.75 12C21.7473 9.41498 20.7192 6.93661 18.8913 5.10872C17.0634 3.28084 14.585 2.25273 12 2.25ZM12 20.25C10.3683 20.25 8.77326 19.7661 7.41655 18.8596C6.05984 17.9531 5.00242 16.6646 4.378 15.1571C3.75358 13.6496 3.5902 11.9908 3.90853 10.3905C4.22685 8.79016 5.01259 7.32015 6.16637 6.16637C7.32016 5.01259 8.79017 4.22685 10.3905 3.90852C11.9909 3.59019 13.6497 3.75357 15.1571 4.37799C16.6646 5.00242 17.9531 6.05984 18.8596 7.41655C19.7661 8.77325 20.25 10.3683 20.25 12C20.2475 14.1873 19.3775 16.2843 17.8309 17.8309C16.2843 19.3775 14.1873 20.2475 12 20.25ZM18 12C18 12.1989 17.921 12.3897 17.7803 12.5303C17.6397 12.671 17.4489 12.75 17.25 12.75H12C11.8011 12.75 11.6103 12.671 11.4697 12.5303C11.329 12.3897 11.25 12.1989 11.25 12V6.75C11.25 6.55109 11.329 6.36032 11.4697 6.21967C11.6103 6.07902 11.8011 6 12 6C12.1989 6 12.3897 6.07902 12.5303 6.21967C12.671 6.36032 12.75 6.55109 12.75 6.75V11.25H17.25C17.4489 11.25 17.6397 11.329 17.7803 11.4697C17.921 11.6103 18 11.8011 18 12Z"
				fill={props.color ?? "#A1A1AA"}
			/>
		</svg>
	);
};
export const BookIcon = (props: IIconProps) => {
	return (
		<svg
			width="20"
			height="20"
			viewBox="0 0 20 20"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M17.5 3.75H12.5C12.0149 3.75 11.5364 3.86295 11.1025 4.07991C10.6685 4.29688 10.2911 4.61189 10 5C9.70892 4.61189 9.33147 4.29688 8.89754 4.07991C8.46362 3.86295 7.98514 3.75 7.5 3.75H2.5C2.16848 3.75 1.85054 3.8817 1.61612 4.11612C1.3817 4.35054 1.25 4.66848 1.25 5V15C1.25 15.3315 1.3817 15.6495 1.61612 15.8839C1.85054 16.1183 2.16848 16.25 2.5 16.25H7.5C7.99728 16.25 8.47419 16.4475 8.82583 16.7992C9.17746 17.1508 9.375 17.6277 9.375 18.125C9.375 18.2908 9.44085 18.4497 9.55806 18.5669C9.67527 18.6842 9.83424 18.75 10 18.75C10.1658 18.75 10.3247 18.6842 10.4419 18.5669C10.5592 18.4497 10.625 18.2908 10.625 18.125C10.625 17.6277 10.8225 17.1508 11.1742 16.7992C11.5258 16.4475 12.0027 16.25 12.5 16.25H17.5C17.8315 16.25 18.1495 16.1183 18.3839 15.8839C18.6183 15.6495 18.75 15.3315 18.75 15V5C18.75 4.66848 18.6183 4.35054 18.3839 4.11612C18.1495 3.8817 17.8315 3.75 17.5 3.75ZM7.5 15H2.5V5H7.5C7.99728 5 8.47419 5.19754 8.82583 5.54917C9.17746 5.90081 9.375 6.37772 9.375 6.875V15.625C8.83458 15.2183 8.17633 14.9989 7.5 15ZM17.5 15H12.5C11.8237 14.9989 11.1654 15.2183 10.625 15.625V6.875C10.625 6.37772 10.8225 5.90081 11.1742 5.54917C11.5258 5.19754 12.0027 5 12.5 5H17.5V15Z"
				fill="#8F34B4"
			/>
		</svg>
	);
};
export const ProfileIcon = (props: IIconProps) => {
	return (
		<svg
			width="20"
			height="20"
			viewBox="0 0 20 20"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M16.875 3.125H3.125C2.79348 3.125 2.47554 3.2567 2.24112 3.49112C2.0067 3.72554 1.875 4.04348 1.875 4.375V15.625C1.875 15.9565 2.0067 16.2745 2.24112 16.5089C2.47554 16.7433 2.79348 16.875 3.125 16.875H16.875C17.2065 16.875 17.5245 16.7433 17.7589 16.5089C17.9933 16.2745 18.125 15.9565 18.125 15.625V4.375C18.125 4.04348 17.9933 3.72554 17.7589 3.49112C17.5245 3.2567 17.2065 3.125 16.875 3.125ZM7.5 9.375C7.5 8.88055 7.64662 8.3972 7.92133 7.98607C8.19603 7.57495 8.58648 7.25452 9.04329 7.0653C9.50011 6.87608 10.0028 6.82657 10.4877 6.92304C10.9727 7.0195 11.4181 7.2576 11.7678 7.60723C12.1174 7.95686 12.3555 8.40232 12.452 8.88727C12.5484 9.37223 12.4989 9.87489 12.3097 10.3317C12.1205 10.7885 11.8 11.179 11.3889 11.4537C10.9778 11.7284 10.4945 11.875 10 11.875C9.33696 11.875 8.70107 11.6116 8.23223 11.1428C7.76339 10.6739 7.5 10.038 7.5 9.375ZM5.66953 15.625C6.10833 14.8647 6.73954 14.2334 7.4997 13.7945C8.25987 13.3555 9.1222 13.1244 10 13.1244C10.8778 13.1244 11.7401 13.3555 12.5003 13.7945C13.2605 14.2334 13.8917 14.8647 14.3305 15.625H5.66953ZM16.875 15.625H15.7289C15.0692 14.1184 13.8431 12.9318 12.3156 12.3219C12.9291 11.8402 13.3772 11.1793 13.5974 10.431C13.8177 9.68273 13.7991 8.88442 13.5443 8.14721C13.2895 7.41001 12.8111 6.77061 12.1759 6.31803C11.5406 5.86545 10.78 5.62223 10 5.62223C9.22 5.62223 8.45942 5.86545 7.82415 6.31803C7.18888 6.77061 6.71054 7.41001 6.45573 8.14721C6.20093 8.88442 6.18234 9.68273 6.40257 10.431C6.62279 11.1793 7.07085 11.8402 7.68437 12.3219C6.15692 12.9318 4.93082 14.1184 4.27109 15.625H3.125V4.375H16.875V15.625Z"
				fill="#8F34B4"
			/>
		</svg>
	);
};
export const ArrowRightIcon = (props: IIconProps) => {
	return (
		<svg
			width="34"
			height="35"
			viewBox="0 0 34 35"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M29.4392 18.2518L19.8767 27.8143C19.6774 28.0137 19.4069 28.1257 19.125 28.1257C18.8431 28.1257 18.5726 28.0137 18.3733 27.8143C18.1739 27.615 18.0619 27.3446 18.0619 27.0626C18.0619 26.7807 18.1739 26.5103 18.3733 26.3109L26.1229 18.5626H5.3125C5.03071 18.5626 4.76046 18.4507 4.5612 18.2514C4.36194 18.0521 4.25 17.7819 4.25 17.5001C4.25 17.2183 4.36194 16.9481 4.5612 16.7488C4.76046 16.5495 5.03071 16.4376 5.3125 16.4376H26.1229L18.3733 8.68932C18.1739 8.48995 18.0619 8.21955 18.0619 7.9376C18.0619 7.65565 18.1739 7.38525 18.3733 7.18588C18.5726 6.98652 18.8431 6.87451 19.125 6.87451C19.4069 6.87451 19.6774 6.98652 19.8767 7.18588L29.4392 16.7484C29.538 16.8471 29.6164 16.9642 29.6698 17.0932C29.7233 17.2222 29.7508 17.3605 29.7508 17.5001C29.7508 17.6397 29.7233 17.778 29.6698 17.907C29.6164 18.036 29.538 18.1531 29.4392 18.2518Z"
				fill={props.color ?? "#52525B"}
			/>
		</svg>
	);
};
export const CaretIcon = (props: IIconProps) => {
	return (
		<svg
			width="21"
			height="20"
			viewBox="0 0 21 20"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M14.6925 10.4425L8.44254 16.6925C8.38447 16.7506 8.31553 16.7967 8.23966 16.8281C8.16379 16.8595 8.08247 16.8757 8.00035 16.8757C7.91823 16.8757 7.83691 16.8595 7.76104 16.8281C7.68517 16.7967 7.61623 16.7506 7.55816 16.6925C7.50009 16.6345 7.45403 16.5655 7.4226 16.4897C7.39117 16.4138 7.375 16.3325 7.375 16.2503C7.375 16.1682 7.39117 16.0869 7.4226 16.011C7.45403 15.9352 7.50009 15.8662 7.55816 15.8082L13.3668 10.0003L7.55816 4.19253C7.44088 4.07526 7.375 3.9162 7.375 3.75035C7.375 3.5845 7.44088 3.42544 7.55816 3.30816C7.67544 3.19088 7.8345 3.125 8.00035 3.125C8.1662 3.125 8.32526 3.19088 8.44254 3.30816L14.6925 9.55816C14.7506 9.61621 14.7967 9.68514 14.8282 9.76101C14.8597 9.83688 14.8758 9.91821 14.8758 10.0003C14.8758 10.0825 14.8597 10.1638 14.8282 10.2397C14.7967 10.3156 14.7506 10.3845 14.6925 10.4425Z"
				fill={props.color ?? "white"}
			/>
		</svg>
	);
};

export const ZoomIcon = (props: IIconProps) => {
	return (
		<svg
			width="32"
			height="32"
			viewBox="0 0 32 32"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M31.4713 9.125C31.3118 9.03953 31.1321 8.99892 30.9514 9.0075C30.7707 9.01609 30.5956 9.07354 30.445 9.17375L26 12.1313V9C26 8.46957 25.7893 7.96086 25.4142 7.58579C25.0391 7.21071 24.5304 7 24 7H4C3.46957 7 2.96086 7.21071 2.58579 7.58579C2.21071 7.96086 2 8.46957 2 9V23C2 23.5304 2.21071 24.0391 2.58579 24.4142C2.96086 24.7893 3.46957 25 4 25H24C24.5304 25 25.0391 24.7893 25.4142 24.4142C25.7893 24.0391 26 23.5304 26 23V19.875L30.445 22.8388C30.6101 22.946 30.8032 23.002 31 23C31.2652 23 31.5196 22.8946 31.7071 22.7071C31.8946 22.5196 32 22.2652 32 22V10C31.9987 9.82007 31.949 9.64382 31.8559 9.48982C31.7628 9.33582 31.63 9.20979 31.4713 9.125ZM24 23H4V9H24V23ZM30 20.1313L26 17.465V14.535L30 11.875V20.1313Z"
				fill={props.color ?? "#A1A1AA"}
			/>
		</svg>
	);
};
export const ArrowAngleIcon = (props: IIconProps) => {
	return (
		<svg
			width="21"
			height="20"
			viewBox="0 0 21 20"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M16.1249 5V13.125C16.1249 13.2908 16.059 13.4497 15.9418 13.5669C15.8246 13.6842 15.6656 13.75 15.4999 13.75C15.3341 13.75 15.1751 13.6842 15.0579 13.5669C14.9407 13.4497 14.8749 13.2908 14.8749 13.125V6.50859L5.94205 15.4422C5.82477 15.5595 5.66571 15.6253 5.49986 15.6253C5.33401 15.6253 5.17495 15.5595 5.05767 15.4422C4.9404 15.3249 4.87451 15.1659 4.87451 15C4.87451 14.8341 4.9404 14.6751 5.05767 14.5578L13.9913 5.625H7.37486C7.2091 5.625 7.05013 5.55915 6.93292 5.44194C6.81571 5.32473 6.74986 5.16576 6.74986 5C6.74986 4.83424 6.81571 4.67527 6.93292 4.55806C7.05013 4.44085 7.2091 4.375 7.37486 4.375H15.4999C15.6656 4.375 15.8246 4.44085 15.9418 4.55806C16.059 4.67527 16.1249 4.83424 16.1249 5Z"
				fill={props.color ?? "#335CFF"}
			/>
		</svg>
	);
};
export const LocationIcon = (props: IIconProps) => {
	return (
		<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M9 4.5C8.44374 4.5 7.89997 4.66495 7.43746 4.97399C6.97495 5.28303 6.61446 5.72229 6.40159 6.2362C6.18872 6.75012 6.13302 7.31562 6.24154 7.86119C6.35006 8.40676 6.61793 8.9079 7.01126 9.30124C7.4046 9.69457 7.90574 9.96244 8.45131 10.071C8.99688 10.1795 9.56238 10.1238 10.0763 9.91091C10.5902 9.69804 11.0295 9.33755 11.3385 8.87504C11.6475 8.41253 11.8125 7.86876 11.8125 7.3125C11.8125 6.56658 11.5162 5.85121 10.9887 5.32376C10.4613 4.79632 9.74592 4.5 9 4.5ZM9 9C8.66624 9 8.33998 8.90103 8.06248 8.7156C7.78497 8.53018 7.56868 8.26663 7.44095 7.95828C7.31323 7.64993 7.27981 7.31063 7.34492 6.98328C7.41004 6.65594 7.57076 6.35526 7.80676 6.11926C8.04276 5.88326 8.34344 5.72254 8.67078 5.65742C8.99813 5.59231 9.33743 5.62573 9.64578 5.75345C9.95413 5.88118 10.2177 6.09747 10.4031 6.37498C10.5885 6.65248 10.6875 6.97874 10.6875 7.3125C10.6875 7.76005 10.5097 8.18928 10.1932 8.50574C9.87678 8.82221 9.44755 9 9 9ZM9 1.125C7.35954 1.12686 5.78681 1.77935 4.62683 2.93933C3.46685 4.09931 2.81436 5.67204 2.8125 7.3125C2.8125 9.52031 3.83273 11.8603 5.76562 14.0801C6.63414 15.0831 7.61165 15.9864 8.68008 16.773C8.77466 16.8393 8.88734 16.8748 9.00281 16.8748C9.11829 16.8748 9.23097 16.8393 9.32555 16.773C10.392 15.986 11.3676 15.0828 12.2344 14.0801C14.1645 11.8603 15.1875 9.52031 15.1875 7.3125C15.1856 5.67204 14.5331 4.09931 13.3732 2.93933C12.2132 1.77935 10.6405 1.12686 9 1.125ZM9 15.6094C7.83773 14.6953 3.9375 11.3379 3.9375 7.3125C3.9375 5.96984 4.47087 4.68217 5.42027 3.73277C6.36967 2.78337 7.65734 2.25 9 2.25C10.3427 2.25 11.6303 2.78337 12.5797 3.73277C13.5291 4.68217 14.0625 5.96984 14.0625 7.3125C14.0625 11.3365 10.1623 14.6953 9 15.6094Z"
				fill={props.color ?? "#52525B"}
			/>
		</svg>
	);
};
export const MailIcon = (props: IIconProps) => {
	return (
		<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M16.417 3.375H2.91699C2.76781 3.375 2.62473 3.43426 2.51924 3.53975C2.41376 3.64524 2.35449 3.78832 2.35449 3.9375V13.5C2.35449 13.7984 2.47302 14.0845 2.684 14.2955C2.89498 14.5065 3.18112 14.625 3.47949 14.625H15.8545C16.1529 14.625 16.439 14.5065 16.65 14.2955C16.861 14.0845 16.9795 13.7984 16.9795 13.5V3.9375C16.9795 3.78832 16.9202 3.64524 16.8147 3.53975C16.7093 3.43426 16.5662 3.375 16.417 3.375ZM9.66699 9.36211L4.36332 4.5H14.9707L9.66699 9.36211ZM7.60754 9L3.47949 12.7835V5.21648L7.60754 9ZM8.44004 9.76289L9.28379 10.5398C9.38757 10.6351 9.52331 10.688 9.66418 10.688C9.80505 10.688 9.94079 10.6351 10.0446 10.5398L10.8883 9.76289L14.9664 13.5H4.36332L8.44004 9.76289ZM11.7264 9L15.8545 5.21578V12.7842L11.7264 9Z"
				fill={props.color ?? "#52525B"}
			/>
		</svg>
	);
};
export const PhoneIcon = (props: IIconProps) => {
	return (
		<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M11.0395 2.66681C11.0586 2.59542 11.0916 2.5285 11.1365 2.46986C11.1815 2.41122 11.2376 2.36202 11.3016 2.32506C11.3656 2.2881 11.4362 2.26411 11.5095 2.25447C11.5827 2.24482 11.6572 2.24971 11.7286 2.26884C12.7713 2.54092 13.7227 3.08604 14.4848 3.84809C15.2468 4.61013 15.792 5.56153 16.064 6.60431C16.0832 6.67569 16.088 6.75013 16.0784 6.8234C16.0688 6.89666 16.0448 6.96731 16.0078 7.0313C15.9709 7.09529 15.9217 7.15137 15.863 7.19634C15.8044 7.24131 15.7374 7.27428 15.6661 7.29337C15.6185 7.30585 15.5696 7.31223 15.5205 7.31236C15.3966 7.31235 15.2761 7.27141 15.1778 7.19588C15.0795 7.12035 15.0089 7.01448 14.977 6.8947C14.7551 6.04344 14.3102 5.26676 13.6882 4.64472C13.0661 4.02267 12.2894 3.57777 11.4382 3.35587C11.3667 3.33685 11.2997 3.30394 11.241 3.259C11.1823 3.21407 11.133 3.15799 11.096 3.094C11.0589 3.03 11.0349 2.95933 11.0252 2.88603C11.0155 2.81273 11.0204 2.73823 11.0395 2.66681ZM10.8757 5.60587C11.8453 5.86462 12.4682 6.48759 12.727 7.4572C12.7589 7.57698 12.8295 7.68285 12.9278 7.75838C13.0261 7.83391 13.1466 7.87485 13.2705 7.87486C13.3196 7.87473 13.3685 7.86835 13.4161 7.85587C13.4874 7.83678 13.5544 7.80381 13.613 7.75884C13.6717 7.71387 13.7209 7.65779 13.7578 7.5938C13.7948 7.52981 13.8188 7.45916 13.8284 7.3859C13.838 7.31263 13.8332 7.23819 13.814 7.16681C13.454 5.81962 12.5132 4.87884 11.1661 4.51884C11.0947 4.49977 11.0203 4.49495 10.947 4.50465C10.8738 4.51435 10.8032 4.53838 10.7392 4.57537C10.6753 4.61235 10.6192 4.66157 10.5743 4.72022C10.5294 4.77887 10.4964 4.84579 10.4773 4.91716C10.4583 4.98854 10.4535 5.06297 10.4632 5.13621C10.4729 5.20945 10.4969 5.28006 10.5339 5.34401C10.5709 5.40797 10.6201 5.46401 10.6787 5.50894C10.7374 5.55387 10.8043 5.58681 10.8757 5.60587ZM16.6371 12.3102C16.5117 13.2629 16.0438 14.1375 15.3208 14.7705C14.5977 15.4034 13.669 15.7516 12.708 15.7499C7.1252 15.7499 2.58301 11.2077 2.58301 5.62486C2.58129 4.66389 2.92943 3.73515 3.56242 3.01211C4.19541 2.28907 5.06995 1.82117 6.0227 1.6958C6.26363 1.66638 6.50761 1.71567 6.71822 1.83631C6.92884 1.95695 7.09478 2.14247 7.1913 2.36517L8.6763 5.6804V5.68884C8.75018 5.85932 8.7807 6.04544 8.76512 6.23058C8.74954 6.41573 8.68834 6.59413 8.587 6.74986C8.57434 6.76884 8.56098 6.78642 8.54692 6.804L7.08301 8.53931C7.60965 9.60947 8.72903 10.719 9.81325 11.247L11.5247 9.79087C11.5415 9.77675 11.5591 9.7636 11.5774 9.7515C11.733 9.64772 11.912 9.58437 12.0982 9.56718C12.2845 9.54999 12.4721 9.57951 12.644 9.65306L12.6532 9.65728L15.9656 11.1416C16.1887 11.2377 16.3747 11.4035 16.4957 11.6142C16.6167 11.8248 16.6663 12.069 16.6371 12.3102ZM15.5205 12.1695C15.5205 12.1695 15.5156 12.1695 15.5128 12.1695L12.2081 10.6895L10.496 12.1456C10.4794 12.1597 10.462 12.1729 10.444 12.185C10.2821 12.293 10.0949 12.3572 9.90082 12.3712C9.70671 12.3853 9.51229 12.3487 9.33653 12.2652C8.01958 11.6288 6.70684 10.326 6.06981 9.02306C5.98546 8.84858 5.9476 8.65528 5.95992 8.46187C5.97224 8.26846 6.03431 8.08152 6.14012 7.91915C6.15205 7.90009 6.16545 7.88199 6.1802 7.86501L7.64551 6.12759L6.16895 2.8229C6.16867 2.8201 6.16867 2.81727 6.16895 2.81447C5.4871 2.90341 4.86105 3.23786 4.40805 3.75519C3.95505 4.27251 3.70617 4.93723 3.70801 5.62486C3.71062 8.01101 4.65967 10.2987 6.34693 11.9859C8.03419 13.6732 10.3219 14.6223 12.708 14.6249C13.3952 14.6272 14.0598 14.3792 14.5774 13.9272C15.095 13.4752 15.4303 12.8501 15.5205 12.1688V12.1695Z"
				fill={props.color ?? "#52525B"}
			/>
		</svg>
	);
};
export const DeadlineIcon = (props: IIconProps) => {
	return (
		<svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M15.292 2.25H13.6045V1.6875C13.6045 1.53832 13.5452 1.39524 13.4397 1.28975C13.3343 1.18426 13.1912 1.125 13.042 1.125C12.8928 1.125 12.7497 1.18426 12.6442 1.28975C12.5388 1.39524 12.4795 1.53832 12.4795 1.6875V2.25H6.85449V1.6875C6.85449 1.53832 6.79523 1.39524 6.68974 1.28975C6.58425 1.18426 6.44118 1.125 6.29199 1.125C6.14281 1.125 5.99973 1.18426 5.89424 1.28975C5.78876 1.39524 5.72949 1.53832 5.72949 1.6875V2.25H4.04199C3.74362 2.25 3.45748 2.36853 3.2465 2.5795C3.03552 2.79048 2.91699 3.07663 2.91699 3.375V14.625C2.91699 14.9234 3.03552 15.2095 3.2465 15.4205C3.45748 15.6315 3.74362 15.75 4.04199 15.75H15.292C15.5904 15.75 15.8765 15.6315 16.0875 15.4205C16.2985 15.2095 16.417 14.9234 16.417 14.625V3.375C16.417 3.07663 16.2985 2.79048 16.0875 2.5795C15.8765 2.36853 15.5904 2.25 15.292 2.25ZM5.72949 3.375V3.9375C5.72949 4.08668 5.78876 4.22976 5.89424 4.33525C5.99973 4.44074 6.14281 4.5 6.29199 4.5C6.44118 4.5 6.58425 4.44074 6.68974 4.33525C6.79523 4.22976 6.85449 4.08668 6.85449 3.9375V3.375H12.4795V3.9375C12.4795 4.08668 12.5388 4.22976 12.6442 4.33525C12.7497 4.44074 12.8928 4.5 13.042 4.5C13.1912 4.5 13.3343 4.44074 13.4397 4.33525C13.5452 4.22976 13.6045 4.08668 13.6045 3.9375V3.375H15.292V5.625H4.04199V3.375H5.72949ZM15.292 14.625H4.04199V6.75H15.292V14.625ZM11.7525 9.39797L10.4622 10.6875L11.7525 11.977C11.8047 12.0293 11.8462 12.0913 11.8745 12.1596C11.9027 12.2279 11.9173 12.3011 11.9173 12.375C11.9173 12.4489 11.9027 12.5221 11.8745 12.5904C11.8462 12.6587 11.8047 12.7207 11.7525 12.773C11.7002 12.8252 11.6382 12.8667 11.5699 12.895C11.5016 12.9233 11.4284 12.9378 11.3545 12.9378C11.2806 12.9378 11.2074 12.9233 11.1391 12.895C11.0708 12.8667 11.0088 12.8252 10.9565 12.773L9.66699 11.4827L8.37746 12.773C8.3252 12.8252 8.26315 12.8667 8.19487 12.895C8.12659 12.9233 8.0534 12.9378 7.97949 12.9378C7.90558 12.9378 7.8324 12.9233 7.76411 12.895C7.69583 12.8667 7.63379 12.8252 7.58152 12.773C7.52926 12.7207 7.4878 12.6587 7.45952 12.5904C7.43124 12.5221 7.41668 12.4489 7.41668 12.375C7.41668 12.3011 7.43124 12.2279 7.45952 12.1596C7.4878 12.0913 7.52926 12.0293 7.58152 11.977L8.87176 10.6875L7.58152 9.39797C7.47598 9.29242 7.41668 9.14927 7.41668 9C7.41668 8.85073 7.47598 8.70758 7.58152 8.60203C7.68707 8.49648 7.83022 8.43719 7.97949 8.43719C8.12876 8.43719 8.27191 8.49648 8.37746 8.60203L9.66699 9.89227L10.9565 8.60203C11.0088 8.54977 11.0708 8.50831 11.1391 8.48003C11.2074 8.45174 11.2806 8.43719 11.3545 8.43719C11.4284 8.43719 11.5016 8.45174 11.5699 8.48003C11.6382 8.50831 11.7002 8.54977 11.7525 8.60203C11.8047 8.65429 11.8462 8.71634 11.8745 8.78462C11.9027 8.8529 11.9173 8.92609 11.9173 9C11.9173 9.07391 11.9027 9.1471 11.8745 9.21538C11.8462 9.28366 11.8047 9.34571 11.7525 9.39797Z"
				fill={props.color ?? "#52525B"}
			/>
		</svg>
	);
};
export const CalendarCheckIcon = (props: IIconProps) => {
	return (
		<svg
			width="18"
			height="18"
			viewBox="0 0 18 18"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M14.625 2.25H12.9375V1.6875C12.9375 1.53832 12.8782 1.39524 12.7727 1.28975C12.6673 1.18426 12.5242 1.125 12.375 1.125C12.2258 1.125 12.0827 1.18426 11.9773 1.28975C11.8718 1.39524 11.8125 1.53832 11.8125 1.6875V2.25H6.1875V1.6875C6.1875 1.53832 6.12824 1.39524 6.02275 1.28975C5.91726 1.18426 5.77418 1.125 5.625 1.125C5.47582 1.125 5.33274 1.18426 5.22725 1.28975C5.12176 1.39524 5.0625 1.53832 5.0625 1.6875V2.25H3.375C3.07663 2.25 2.79048 2.36853 2.5795 2.5795C2.36853 2.79048 2.25 3.07663 2.25 3.375V14.625C2.25 14.9234 2.36853 15.2095 2.5795 15.4205C2.79048 15.6315 3.07663 15.75 3.375 15.75H14.625C14.9234 15.75 15.2095 15.6315 15.4205 15.4205C15.6315 15.2095 15.75 14.9234 15.75 14.625V3.375C15.75 3.07663 15.6315 2.79048 15.4205 2.5795C15.2095 2.36853 14.9234 2.25 14.625 2.25ZM5.0625 3.375V3.9375C5.0625 4.08668 5.12176 4.22976 5.22725 4.33525C5.33274 4.44074 5.47582 4.5 5.625 4.5C5.77418 4.5 5.91726 4.44074 6.02275 4.33525C6.12824 4.22976 6.1875 4.08668 6.1875 3.9375V3.375H11.8125V3.9375C11.8125 4.08668 11.8718 4.22976 11.9773 4.33525C12.0827 4.44074 12.2258 4.5 12.375 4.5C12.5242 4.5 12.6673 4.44074 12.7727 4.33525C12.8782 4.22976 12.9375 4.08668 12.9375 3.9375V3.375H14.625V5.625H3.375V3.375H5.0625ZM14.625 14.625H3.375V6.75H14.625V14.625ZM11.9292 8.60203C11.9815 8.65427 12.023 8.71631 12.0513 8.7846C12.0796 8.85288 12.0942 8.92608 12.0942 9C12.0942 9.07392 12.0796 9.14712 12.0513 9.2154C12.023 9.28369 11.9815 9.34573 11.9292 9.39797L8.55422 12.773C8.50198 12.8253 8.43994 12.8668 8.37165 12.8951C8.30337 12.9234 8.23017 12.9379 8.15625 12.9379C8.08233 12.9379 8.00913 12.9234 7.94085 12.8951C7.87256 12.8668 7.81052 12.8253 7.75828 12.773L6.07078 11.0855C5.96523 10.9799 5.90594 10.8368 5.90594 10.6875C5.90594 10.5382 5.96523 10.3951 6.07078 10.2895C6.17633 10.184 6.31948 10.1247 6.46875 10.1247C6.61802 10.1247 6.76117 10.184 6.86672 10.2895L8.15625 11.5798L11.1333 8.60203C11.1855 8.54973 11.2476 8.50824 11.3158 8.47994C11.3841 8.45163 11.4573 8.43706 11.5312 8.43706C11.6052 8.43706 11.6784 8.45163 11.7467 8.47994C11.8149 8.50824 11.877 8.54973 11.9292 8.60203Z"
				fill={props.color ?? "#52525B"}
			/>
		</svg>
	);
};
export const PeopleIcon = (props: IIconProps) => {
	return (
		<svg
			width="18"
			height="18"
			viewBox="0 0 18 18"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M8.24408 11.1035C8.99798 10.6016 9.57034 9.87043 9.87653 9.01808C10.1827 8.16573 10.2065 7.23747 9.94432 6.37056C9.68214 5.50366 9.14795 4.74414 8.42073 4.20433C7.69351 3.66451 6.81187 3.37305 5.90619 3.37305C5.00051 3.37305 4.11888 3.66451 3.39166 4.20433C2.66444 4.74414 2.13024 5.50366 1.86807 6.37056C1.6059 7.23747 1.62966 8.16573 1.93586 9.01808C2.24205 9.87043 2.81441 10.6016 3.5683 11.1035C2.20459 11.6061 1.03994 12.5362 0.248146 13.755C0.206533 13.8168 0.177627 13.8863 0.163111 13.9595C0.148595 14.0326 0.148758 14.1079 0.16359 14.1809C0.178421 14.254 0.207626 14.3233 0.249507 14.385C0.291387 14.4467 0.345108 14.4994 0.407547 14.5401C0.469985 14.5809 0.539896 14.6088 0.613215 14.6222C0.686534 14.6357 0.761799 14.6345 0.834634 14.6186C0.90747 14.6027 0.976424 14.5725 1.03749 14.5298C1.09855 14.487 1.15051 14.4325 1.19033 14.3695C1.70108 13.584 2.39996 12.9385 3.22352 12.4916C4.04707 12.0447 4.96921 11.8107 5.90619 11.8107C6.84317 11.8107 7.76531 12.0447 8.58887 12.4916C9.41243 12.9385 10.1113 13.584 10.6221 14.3695C10.7046 14.4921 10.832 14.5774 10.9768 14.6068C11.1217 14.6362 11.2723 14.6074 11.396 14.5267C11.5198 14.4459 11.6069 14.3197 11.6383 14.1753C11.6698 14.0309 11.6432 13.8799 11.5642 13.755C10.7724 12.5362 9.6078 11.6061 8.24408 11.1035ZM2.81244 7.59351C2.81244 6.98162 2.99389 6.38348 3.33383 5.87471C3.67378 5.36595 4.15696 4.96942 4.72227 4.73526C5.28758 4.5011 5.90963 4.43983 6.50975 4.5592C7.10988 4.67858 7.66114 4.97323 8.09381 5.4059C8.52647 5.83857 8.82112 6.38982 8.9405 6.98995C9.05987 7.59008 8.9986 8.21213 8.76445 8.77744C8.53029 9.34274 8.13375 9.82592 7.62499 10.1659C7.11622 10.5058 6.51808 10.6873 5.90619 10.6873C5.08597 10.6863 4.2996 10.3601 3.71961 9.78009C3.13962 9.2001 2.81337 8.41374 2.81244 7.59351ZM17.5879 14.5334C17.463 14.6148 17.3108 14.6433 17.1648 14.6126C17.0188 14.5819 16.8911 14.4945 16.8096 14.3695C16.2994 13.5835 15.6006 12.9377 14.7769 12.491C13.9532 12.0444 13.0307 11.811 12.0937 11.8123C11.9445 11.8123 11.8014 11.753 11.6959 11.6475C11.5905 11.542 11.5312 11.3989 11.5312 11.2498C11.5312 11.1006 11.5905 10.9575 11.6959 10.852C11.8014 10.7465 11.9445 10.6873 12.0937 10.6873C12.5493 10.6868 12.9992 10.5858 13.4112 10.3913C13.8232 10.1969 14.1872 9.91381 14.4772 9.56238C14.7671 9.21095 14.9759 8.79981 15.0885 8.35835C15.2011 7.91689 15.2149 7.456 15.1287 7.00861C15.0426 6.56123 14.8587 6.13839 14.5902 5.7703C14.3217 5.40222 13.9752 5.09798 13.5755 4.87933C13.1758 4.66067 12.7328 4.533 12.278 4.50543C11.8232 4.47786 11.368 4.55107 10.9448 4.71984C10.8758 4.74966 10.8015 4.76536 10.7263 4.766C10.6512 4.76663 10.5766 4.75219 10.5071 4.72354C10.4376 4.69488 10.3746 4.65258 10.3217 4.59915C10.2689 4.54571 10.2272 4.48222 10.1993 4.41242C10.1714 4.34263 10.1578 4.26795 10.1592 4.19279C10.1606 4.11763 10.1771 4.04352 10.2077 3.97484C10.2382 3.90616 10.2822 3.8443 10.3371 3.79292C10.392 3.74154 10.4566 3.70168 10.5271 3.6757C11.4957 3.28944 12.5729 3.27554 13.5511 3.63668C14.5293 3.99782 15.339 4.70842 15.8242 5.63138C16.3094 6.55434 16.4355 7.62428 16.1784 8.63478C15.9212 9.64528 15.2989 10.5247 14.4316 11.1035C15.7953 11.6061 16.9599 12.5362 17.7517 13.755C17.8332 13.8799 17.8617 14.0321 17.831 14.1781C17.8003 14.3241 17.7128 14.4518 17.5879 14.5334Z"
				fill="#52525B"
			/>
		</svg>
	);
};
export const AwardIcon = (props: IIconProps) => {
	return (
		<svg
			width="19"
			height="18"
			viewBox="0 0 19 18"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M15.5215 6.74977C15.5221 5.70703 15.2592 4.68106 14.7572 3.76713C14.2551 2.8532 13.5303 2.08097 12.6499 1.52215C11.7696 0.963339 10.7623 0.636072 9.72161 0.570749C8.68092 0.505425 7.64061 0.704165 6.69729 1.14851C5.75397 1.59285 4.93824 2.26838 4.32588 3.11236C3.71351 3.95634 3.32437 4.94139 3.19459 5.97602C3.06482 7.01064 3.19862 8.06128 3.58357 9.03036C3.96852 9.99943 4.59213 10.8555 5.39649 11.5191V16.8748C5.39642 16.9707 5.42088 17.0651 5.46756 17.1489C5.51424 17.2327 5.58157 17.3032 5.66317 17.3536C5.74476 17.4041 5.8379 17.4328 5.93374 17.4371C6.02957 17.4414 6.12491 17.4211 6.2107 17.3782L9.33398 15.8201L12.458 17.3817C12.5363 17.4192 12.6222 17.4382 12.709 17.4373C12.8582 17.4373 13.0012 17.378 13.1067 17.2725C13.2122 17.167 13.2715 17.024 13.2715 16.8748V11.5191C13.9753 10.9394 14.542 10.2112 14.931 9.38659C15.32 8.562 15.5217 7.66151 15.5215 6.74977ZM4.27149 6.74977C4.27149 5.7485 4.5684 4.76972 5.12467 3.93719C5.68094 3.10467 6.4716 2.4558 7.39665 2.07263C8.3217 1.68946 9.3396 1.5892 10.3216 1.78454C11.3037 1.97988 12.2057 2.46204 12.9137 3.17004C13.6217 3.87804 14.1039 4.78009 14.2992 5.76212C14.4945 6.74415 14.3943 7.76205 14.0111 8.6871C13.628 9.61215 12.9791 10.4028 12.1466 10.9591C11.314 11.5154 10.3353 11.8123 9.33398 11.8123C7.99178 11.8108 6.70498 11.2769 5.7559 10.3279C4.80682 9.37877 4.27297 8.09197 4.27149 6.74977ZM12.1465 15.9649L9.585 14.6845C9.50685 14.6454 9.42067 14.6251 9.33328 14.6251C9.2459 14.6251 9.15971 14.6454 9.08156 14.6845L6.52149 15.9649V12.2602C7.39221 12.7052 8.35612 12.9373 9.33398 12.9373C10.3119 12.9373 11.2758 12.7052 12.1465 12.2602V15.9649ZM9.33398 10.6873C10.1127 10.6873 10.874 10.4563 11.5215 10.0237C12.1691 9.59102 12.6737 8.97607 12.9718 8.25658C13.2698 7.5371 13.3478 6.7454 13.1958 5.9816C13.0439 5.2178 12.6689 4.5162 12.1182 3.96553C11.5675 3.41487 10.866 3.03986 10.1022 2.88793C9.33835 2.736 8.54665 2.81397 7.82717 3.11199C7.10768 3.41001 6.49273 3.91469 6.06007 4.56221C5.62742 5.20973 5.39649 5.971 5.39649 6.74977C5.3976 7.79371 5.8128 8.79458 6.55099 9.53277C7.28917 10.2709 8.29004 10.6862 9.33398 10.6873ZM9.33398 3.93727C9.89024 3.93727 10.434 4.10222 10.8965 4.41126C11.359 4.7203 11.7195 5.15955 11.9324 5.67347C12.1453 6.18739 12.201 6.75289 12.0924 7.29846C11.9839 7.84403 11.7161 8.34517 11.3227 8.7385C10.9294 9.13184 10.4282 9.3997 9.88268 9.50823C9.33711 9.61675 8.77161 9.56105 8.25769 9.34818C7.74377 9.13531 7.30452 8.77482 6.99548 8.31231C6.68644 7.84979 6.52149 7.30603 6.52149 6.74977C6.52149 6.00385 6.8178 5.28848 7.34525 4.76103C7.87269 4.23358 8.58806 3.93727 9.33398 3.93727Z"
				fill="#52525B"
			/>
		</svg>
	);
};
export const VideoIcon = (props: IIconProps) => {
	return (
		<svg
			width="20"
			height="21"
			viewBox="0 0 20 21"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M15 6.03711V14.7871C15 15.1186 14.8683 15.4366 14.6339 15.671C14.3995 15.9054 14.0815 16.0371 13.75 16.0371H2.5C2.16848 16.0371 1.85054 15.9054 1.61612 15.671C1.3817 15.4366 1.25 15.1186 1.25 14.7871V6.03711C1.25 5.70559 1.3817 5.38765 1.61612 5.15323C1.85054 4.91881 2.16848 4.78711 2.5 4.78711H13.75C14.0815 4.78711 14.3995 4.91881 14.6339 5.15323C14.8683 5.38765 15 5.70559 15 6.03711ZM19.5312 6.05664C19.4431 6.03506 19.3513 6.0324 19.2621 6.04883C19.1728 6.06526 19.088 6.1004 19.0133 6.15195L16.3891 7.90117C16.3463 7.92973 16.3112 7.96842 16.2869 8.01381C16.2626 8.05919 16.25 8.10987 16.25 8.16133V12.6629C16.25 12.7144 16.2626 12.765 16.2869 12.8104C16.3112 12.8558 16.3463 12.8945 16.3891 12.923L19.0281 14.6824C19.1269 14.7483 19.2424 14.7847 19.3611 14.7873C19.4798 14.79 19.5968 14.7587 19.6984 14.6973C19.7924 14.6375 19.8695 14.5546 19.9223 14.4565C19.9751 14.3585 20.0019 14.2485 20 14.1371V6.66211C20.0001 6.5235 19.9541 6.38879 19.8692 6.27919C19.7843 6.16958 19.6655 6.09129 19.5312 6.05664Z"
				fill={props.color ?? "#335CFF"}
			/>
		</svg>
	);
};
export const AudioIcon = (props: IIconProps) => {
	return (
		<svg
			width="20"
			height="21"
			viewBox="0 0 20 21"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M18.125 11.0369V15.4119C18.125 15.9091 17.9275 16.386 17.5758 16.7377C17.2242 17.0893 16.7473 17.2869 16.25 17.2869H15C14.5027 17.2869 14.0258 17.0893 13.6742 16.7377C13.3225 16.386 13.125 15.9091 13.125 15.4119V12.2869C13.125 11.7896 13.3225 11.3127 13.6742 10.961C14.0258 10.6094 14.5027 10.4119 15 10.4119H16.8477C16.6975 8.71036 15.9173 7.12629 14.66 5.97013C13.4027 4.81396 11.7589 4.16907 10.0508 4.16185H10C8.28477 4.16177 6.63147 4.80284 5.36466 5.95921C4.09785 7.11558 3.30905 8.70373 3.15313 10.4119H5C5.49728 10.4119 5.97419 10.6094 6.32583 10.961C6.67746 11.3127 6.875 11.7896 6.875 12.2869V15.4119C6.875 15.9091 6.67746 16.386 6.32583 16.7377C5.97419 17.0893 5.49728 17.2869 5 17.2869H3.75C3.25272 17.2869 2.77581 17.0893 2.42417 16.7377C2.07254 16.386 1.875 15.9091 1.875 15.4119V11.0369C1.87675 9.4274 2.35596 7.85464 3.25196 6.51765C4.14796 5.18067 5.42047 4.13956 6.90843 3.52611C8.39639 2.91266 10.0329 2.75444 11.6108 3.07149C13.1887 3.38853 14.6371 4.16658 15.7727 5.30716C16.5224 6.06053 17.1161 6.95442 17.5197 7.93762C17.9234 8.92083 18.1291 9.97402 18.125 11.0369Z"
				fill={props.color ?? "#335CFF"}
			/>
		</svg>
	);
};
export const ArticleIcon = (props: IIconProps) => {
	return (
		<svg
			width="20"
			height="21"
			viewBox="0 0 20 21"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M16.875 4.16211H4.375C4.04348 4.16211 3.72554 4.29381 3.49112 4.52823C3.2567 4.76265 3.125 5.08059 3.125 5.41211V14.7871C3.125 14.9529 3.05915 15.1118 2.94194 15.2291C2.82473 15.3463 2.66576 15.4121 2.5 15.4121C2.33424 15.4121 2.17527 15.3463 2.05806 15.2291C1.94085 15.1118 1.875 14.9529 1.875 14.7871V7.28711C1.875 7.12135 1.80915 6.96238 1.69194 6.84517C1.57473 6.72796 1.41576 6.66211 1.25 6.66211C1.08424 6.66211 0.925268 6.72796 0.808058 6.84517C0.690848 6.96238 0.625 7.12135 0.625 7.28711V14.7957C0.627272 15.2915 0.825819 15.7662 1.1772 16.116C1.52859 16.4657 2.0042 16.6621 2.5 16.6621H16.25C16.7473 16.6621 17.2242 16.4646 17.5758 16.1129C17.9275 15.7613 18.125 15.2844 18.125 14.7871V5.41211C18.125 5.08059 17.9933 4.76265 17.7589 4.52823C17.5245 4.29381 17.2065 4.16211 16.875 4.16211ZM13.75 12.2871H7.5C7.33424 12.2871 7.17527 12.2213 7.05806 12.1041C6.94085 11.9868 6.875 11.8279 6.875 11.6621C6.875 11.4963 6.94085 11.3374 7.05806 11.2202C7.17527 11.103 7.33424 11.0371 7.5 11.0371H13.75C13.9158 11.0371 14.0747 11.103 14.1919 11.2202C14.3092 11.3374 14.375 11.4963 14.375 11.6621C14.375 11.8279 14.3092 11.9868 14.1919 12.1041C14.0747 12.2213 13.9158 12.2871 13.75 12.2871ZM13.75 9.78711H7.5C7.33424 9.78711 7.17527 9.72126 7.05806 9.60405C6.94085 9.48684 6.875 9.32787 6.875 9.16211C6.875 8.99635 6.94085 8.83738 7.05806 8.72017C7.17527 8.60296 7.33424 8.53711 7.5 8.53711H13.75C13.9158 8.53711 14.0747 8.60296 14.1919 8.72017C14.3092 8.83738 14.375 8.99635 14.375 9.16211C14.375 9.32787 14.3092 9.48684 14.1919 9.60405C14.0747 9.72126 13.9158 9.78711 13.75 9.78711Z"
				fill={props.color ?? "#335CFF"}
			/>
		</svg>
	);
};
export const FilterIcon = (props: IIconProps) => {
	return (
		<svg
			width="20"
			height="21"
			viewBox="0 0 20 21"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M15.625 10.4121C15.625 10.5779 15.5592 10.7368 15.4419 10.8541C15.3247 10.9713 15.1658 11.0371 15 11.0371H5C4.83424 11.0371 4.67527 10.9713 4.55806 10.8541C4.44085 10.7368 4.375 10.5779 4.375 10.4121C4.375 10.2463 4.44085 10.0874 4.55806 9.97017C4.67527 9.85296 4.83424 9.78711 5 9.78711H15C15.1658 9.78711 15.3247 9.85296 15.4419 9.97017C15.5592 10.0874 15.625 10.2463 15.625 10.4121ZM18.125 6.03711H1.875C1.70924 6.03711 1.55027 6.10296 1.43306 6.22017C1.31585 6.33738 1.25 6.49635 1.25 6.66211C1.25 6.82787 1.31585 6.98684 1.43306 7.10405C1.55027 7.22126 1.70924 7.28711 1.875 7.28711H18.125C18.2908 7.28711 18.4497 7.22126 18.5669 7.10405C18.6842 6.98684 18.75 6.82787 18.75 6.66211C18.75 6.49635 18.6842 6.33738 18.5669 6.22017C18.4497 6.10296 18.2908 6.03711 18.125 6.03711ZM11.875 13.5371H8.125C7.95924 13.5371 7.80027 13.603 7.68306 13.7202C7.56585 13.8374 7.5 13.9963 7.5 14.1621C7.5 14.3279 7.56585 14.4868 7.68306 14.6041C7.80027 14.7213 7.95924 14.7871 8.125 14.7871H11.875C12.0408 14.7871 12.1997 14.7213 12.3169 14.6041C12.4342 14.4868 12.5 14.3279 12.5 14.1621C12.5 13.9963 12.4342 13.8374 12.3169 13.7202C12.1997 13.603 12.0408 13.5371 11.875 13.5371Z"
				fill={props.color ?? "#27272A"}
			/>
		</svg>
	);
};

export const DoubleCheckIcon = (props: IIconProps) => {
	return (
		<svg
			width="32"
			height="32"
			viewBox="0 0 32 32"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M18.7014 10.7136L7.5014 21.7136C7.31436 21.8976 7.06251 22.0007 6.80015 22.0007C6.53779 22.0007 6.28594 21.8976 6.0989 21.7136L1.2989 16.9999C1.20525 16.9078 1.13066 16.7981 1.07938 16.6772C1.0281 16.5563 1.00114 16.4265 1.00004 16.2951C0.998933 16.1638 1.02371 16.0335 1.07295 15.9118C1.12219 15.79 1.19493 15.6791 1.28702 15.5855C1.37911 15.4918 1.48875 15.4172 1.60966 15.366C1.73058 15.3147 1.86042 15.2877 1.99175 15.2866C2.12309 15.2855 2.25336 15.3103 2.37512 15.3595C2.49688 15.4088 2.60775 15.4815 2.7014 15.5736L6.80015 19.5986L17.3001 9.28612C17.4894 9.10013 17.7449 8.99697 18.0102 8.99931C18.1416 9.00047 18.2715 9.0275 18.3925 9.07885C18.5134 9.13021 18.6231 9.20488 18.7151 9.29862C18.8072 9.39235 18.88 9.5033 18.9292 9.62514C18.9784 9.74697 19.0031 9.87731 19.002 10.0087C19.0008 10.1401 18.9738 10.27 18.9224 10.3909C18.8711 10.5119 18.7964 10.6215 18.7026 10.7136H18.7014ZM30.7139 9.29862C30.6219 9.20481 30.5122 9.13008 30.3913 9.07868C30.2703 9.02728 30.1404 9.00022 30.009 8.99906C29.8776 8.9979 29.7472 9.02266 29.6254 9.07191C29.5035 9.12117 29.3926 9.19395 29.2989 9.28612L18.7989 19.5986L16.4451 17.2861C16.2559 17.1003 16.0005 16.9973 15.7352 16.9998C15.47 17.0022 15.2166 17.1099 15.0308 17.2992C14.845 17.4885 14.7419 17.7439 14.7444 18.0091C14.7469 18.2744 14.8546 18.5278 15.0439 18.7136L18.0976 21.7136C18.2847 21.8976 18.5365 22.0007 18.7989 22.0007C19.0613 22.0007 19.3131 21.8976 19.5001 21.7136L30.7001 10.7136C30.794 10.6216 30.8689 10.5121 30.9204 10.3912C30.9719 10.2703 30.9991 10.1404 31.0003 10.009C31.0016 9.87755 30.977 9.74717 30.9278 9.62528C30.8787 9.50339 30.806 9.39239 30.7139 9.29862Z"
				fill="#49A05F"
			/>
		</svg>
	);
};
export const FileIcon = (props: IIconProps) => {
	return (
		<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M26.7075 10.2925L19.7075 3.2925C19.6146 3.19967 19.5042 3.12605 19.3829 3.07586C19.2615 3.02568 19.1314 2.9999 19 3H7C6.46957 3 5.96086 3.21071 5.58579 3.58579C5.21071 3.96086 5 4.46957 5 5V27C5 27.5304 5.21071 28.0391 5.58579 28.4142C5.96086 28.7893 6.46957 29 7 29H25C25.5304 29 26.0391 28.7893 26.4142 28.4142C26.7893 28.0391 27 27.5304 27 27V11C27.0001 10.8686 26.9743 10.7385 26.9241 10.6172C26.8739 10.4958 26.8003 10.3854 26.7075 10.2925ZM20 6.41375L23.5863 10H20V6.41375ZM25 27H7V5H18V11C18 11.2652 18.1054 11.5196 18.2929 11.7071C18.4804 11.8946 18.7348 12 19 12H25V27ZM21 17C21 17.2652 20.8946 17.5196 20.7071 17.7071C20.5196 17.8946 20.2652 18 20 18H12C11.7348 18 11.4804 17.8946 11.2929 17.7071C11.1054 17.5196 11 17.2652 11 17C11 16.7348 11.1054 16.4804 11.2929 16.2929C11.4804 16.1054 11.7348 16 12 16H20C20.2652 16 20.5196 16.1054 20.7071 16.2929C20.8946 16.4804 21 16.7348 21 17ZM21 21C21 21.2652 20.8946 21.5196 20.7071 21.7071C20.5196 21.8946 20.2652 22 20 22H12C11.7348 22 11.4804 21.8946 11.2929 21.7071C11.1054 21.5196 11 21.2652 11 21C11 20.7348 11.1054 20.4804 11.2929 20.2929C11.4804 20.1054 11.7348 20 12 20H20C20.2652 20 20.5196 20.1054 20.7071 20.2929C20.8946 20.4804 21 20.7348 21 21Z"
				fill={props.color ?? "#A1A1AA"}
			/>
		</svg>
	);
};
export const DownloadIcon = (props: IIconProps) => {
	return (
		<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M17.5 11.875V16.25C17.5 16.5815 17.3683 16.8995 17.1339 17.1339C16.8995 17.3683 16.5815 17.5 16.25 17.5H3.75C3.41848 17.5 3.10054 17.3683 2.86612 17.1339C2.6317 16.8995 2.5 16.5815 2.5 16.25V11.875C2.5 11.7092 2.56585 11.5503 2.68306 11.4331C2.80027 11.3158 2.95924 11.25 3.125 11.25C3.29076 11.25 3.44973 11.3158 3.56694 11.4331C3.68415 11.5503 3.75 11.7092 3.75 11.875V16.25H16.25V11.875C16.25 11.7092 16.3158 11.5503 16.4331 11.4331C16.5503 11.3158 16.7092 11.25 16.875 11.25C17.0408 11.25 17.1997 11.3158 17.3169 11.4331C17.4342 11.5503 17.5 11.7092 17.5 11.875ZM9.55781 12.3172C9.61586 12.3753 9.68479 12.4214 9.76066 12.4529C9.83654 12.4843 9.91787 12.5005 10 12.5005C10.0821 12.5005 10.1635 12.4843 10.2393 12.4529C10.3152 12.4214 10.3841 12.3753 10.4422 12.3172L13.5672 9.19219C13.6253 9.13412 13.6713 9.06518 13.7027 8.98931C13.7342 8.91344 13.7503 8.83212 13.7503 8.75C13.7503 8.66788 13.7342 8.58656 13.7027 8.51069C13.6713 8.43482 13.6253 8.36588 13.5672 8.30781C13.5091 8.24974 13.4402 8.20368 13.3643 8.17225C13.2884 8.14083 13.2071 8.12465 13.125 8.12465C13.0429 8.12465 12.9616 8.14083 12.8857 8.17225C12.8098 8.20368 12.7409 8.24974 12.6828 8.30781L10.625 10.3664V3.125C10.625 2.95924 10.5592 2.80027 10.4419 2.68306C10.3247 2.56585 10.1658 2.5 10 2.5C9.83424 2.5 9.67527 2.56585 9.55806 2.68306C9.44085 2.80027 9.375 2.95924 9.375 3.125V10.3664L7.31719 8.30781C7.19991 8.19054 7.04085 8.12465 6.875 8.12465C6.70915 8.12465 6.55009 8.19054 6.43281 8.30781C6.31554 8.42509 6.24965 8.58415 6.24965 8.75C6.24965 8.91585 6.31554 9.07491 6.43281 9.19219L9.55781 12.3172Z"
				fill={props.color ?? "#52525B"}
			/>
		</svg>
	);
};
export const OfficeIcon = (props: IIconProps) => {
	return (
		<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M22.5 19.5H21V9C21 8.60217 20.842 8.22064 20.5607 7.93934C20.2794 7.65803 19.8978 7.5 19.5 7.5H13.5V3C13.5002 2.72837 13.4266 2.46179 13.2871 2.22872C13.1476 1.99564 12.9474 1.80482 12.708 1.67661C12.4685 1.54841 12.1987 1.48764 11.9274 1.50078C11.6561 1.51393 11.3935 1.60049 11.1675 1.75125L3.6675 6.75C3.46176 6.88727 3.29315 7.07328 3.17669 7.29148C3.06023 7.50968 2.99953 7.75329 3 8.00062V19.5H1.5C1.30109 19.5 1.11032 19.579 0.96967 19.7197C0.829018 19.8603 0.75 20.0511 0.75 20.25C0.75 20.4489 0.829018 20.6397 0.96967 20.7803C1.11032 20.921 1.30109 21 1.5 21H22.5C22.6989 21 22.8897 20.921 23.0303 20.7803C23.171 20.6397 23.25 20.4489 23.25 20.25C23.25 20.0511 23.171 19.8603 23.0303 19.7197C22.8897 19.579 22.6989 19.5 22.5 19.5ZM19.5 9V19.5H13.5V9H19.5ZM4.5 8.00062L12 3V19.5H4.5V8.00062ZM10.5 10.5V12C10.5 12.1989 10.421 12.3897 10.2803 12.5303C10.1397 12.671 9.94891 12.75 9.75 12.75C9.55109 12.75 9.36032 12.671 9.21967 12.5303C9.07902 12.3897 9 12.1989 9 12V10.5C9 10.3011 9.07902 10.1103 9.21967 9.96967C9.36032 9.82902 9.55109 9.75 9.75 9.75C9.94891 9.75 10.1397 9.82902 10.2803 9.96967C10.421 10.1103 10.5 10.3011 10.5 10.5ZM7.5 10.5V12C7.5 12.1989 7.42098 12.3897 7.28033 12.5303C7.13968 12.671 6.94891 12.75 6.75 12.75C6.55109 12.75 6.36032 12.671 6.21967 12.5303C6.07902 12.3897 6 12.1989 6 12V10.5C6 10.3011 6.07902 10.1103 6.21967 9.96967C6.36032 9.82902 6.55109 9.75 6.75 9.75C6.94891 9.75 7.13968 9.82902 7.28033 9.96967C7.42098 10.1103 7.5 10.3011 7.5 10.5ZM7.5 15.75V17.25C7.5 17.4489 7.42098 17.6397 7.28033 17.7803C7.13968 17.921 6.94891 18 6.75 18C6.55109 18 6.36032 17.921 6.21967 17.7803C6.07902 17.6397 6 17.4489 6 17.25V15.75C6 15.5511 6.07902 15.3603 6.21967 15.2197C6.36032 15.079 6.55109 15 6.75 15C6.94891 15 7.13968 15.079 7.28033 15.2197C7.42098 15.3603 7.5 15.5511 7.5 15.75ZM10.5 15.75V17.25C10.5 17.4489 10.421 17.6397 10.2803 17.7803C10.1397 17.921 9.94891 18 9.75 18C9.55109 18 9.36032 17.921 9.21967 17.7803C9.07902 17.6397 9 17.4489 9 17.25V15.75C9 15.5511 9.07902 15.3603 9.21967 15.2197C9.36032 15.079 9.55109 15 9.75 15C9.94891 15 10.1397 15.079 10.2803 15.2197C10.421 15.3603 10.5 15.5511 10.5 15.75Z"
				fill={props.color ?? "#A1A1AA"}
			/>
		</svg>
	);
};
export const CardIcon = (props: IIconProps) => {
	return (
		<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path
				d="M15.75 3.375H2.25C1.95163 3.375 1.66548 3.49353 1.4545 3.7045C1.24353 3.91548 1.125 4.20163 1.125 4.5V13.5C1.125 13.7984 1.24353 14.0845 1.4545 14.2955C1.66548 14.5065 1.95163 14.625 2.25 14.625H15.75C16.0484 14.625 16.3345 14.5065 16.5455 14.2955C16.7565 14.0845 16.875 13.7984 16.875 13.5V4.5C16.875 4.20163 16.7565 3.91548 16.5455 3.7045C16.3345 3.49353 16.0484 3.375 15.75 3.375ZM15.75 4.5V6.1875H2.25V4.5H15.75ZM15.75 13.5H2.25V7.3125H15.75V13.5ZM14.625 11.8125C14.625 11.9617 14.5657 12.1048 14.4602 12.2102C14.3548 12.3157 14.2117 12.375 14.0625 12.375H11.8125C11.6633 12.375 11.5202 12.3157 11.4148 12.2102C11.3093 12.1048 11.25 11.9617 11.25 11.8125C11.25 11.6633 11.3093 11.5202 11.4148 11.4148C11.5202 11.3093 11.6633 11.25 11.8125 11.25H14.0625C14.2117 11.25 14.3548 11.3093 14.4602 11.4148C14.5657 11.5202 14.625 11.6633 14.625 11.8125ZM10.125 11.8125C10.125 11.9617 10.0657 12.1048 9.96025 12.2102C9.85476 12.3157 9.71168 12.375 9.5625 12.375H8.4375C8.28832 12.375 8.14524 12.3157 8.03975 12.2102C7.93426 12.1048 7.875 11.9617 7.875 11.8125C7.875 11.6633 7.93426 11.5202 8.03975 11.4148C8.14524 11.3093 8.28832 11.25 8.4375 11.25H9.5625C9.71168 11.25 9.85476 11.3093 9.96025 11.4148C10.0657 11.5202 10.125 11.6633 10.125 11.8125Z"
				fill={props.color ?? "#52525B"}
			/>
		</svg>
	);
};
export const CalendarIcon = (props: IIconProps) => {
	return (
		<svg
			width="24"
			height="24"
			viewBox="0 0 24 24"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M19.5 3H17.25V2.25C17.25 2.05109 17.171 1.86032 17.0303 1.71967C16.8897 1.57902 16.6989 1.5 16.5 1.5C16.3011 1.5 16.1103 1.57902 15.9697 1.71967C15.829 1.86032 15.75 2.05109 15.75 2.25V3H8.25V2.25C8.25 2.05109 8.17098 1.86032 8.03033 1.71967C7.88968 1.57902 7.69891 1.5 7.5 1.5C7.30109 1.5 7.11032 1.57902 6.96967 1.71967C6.82902 1.86032 6.75 2.05109 6.75 2.25V3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H19.5C19.8978 21 20.2794 20.842 20.5607 20.5607C20.842 20.2794 21 19.8978 21 19.5V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM6.75 4.5V5.25C6.75 5.44891 6.82902 5.63968 6.96967 5.78033C7.11032 5.92098 7.30109 6 7.5 6C7.69891 6 7.88968 5.92098 8.03033 5.78033C8.17098 5.63968 8.25 5.44891 8.25 5.25V4.5H15.75V5.25C15.75 5.44891 15.829 5.63968 15.9697 5.78033C16.1103 5.92098 16.3011 6 16.5 6C16.6989 6 16.8897 5.92098 17.0303 5.78033C17.171 5.63968 17.25 5.44891 17.25 5.25V4.5H19.5V7.5H4.5V4.5H6.75ZM19.5 19.5H4.5V9H19.5V19.5Z"
				fill={props.color ?? "#71717A"}
			/>
		</svg>
	);
};
export const BudgetIcon = (props: IIconProps) => {
	return (
		<svg
			width="24"
			height="24"
			viewBox="0 0 24 24"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M12 8.25C11.2583 8.25 10.5333 8.46993 9.91661 8.88199C9.29993 9.29404 8.81928 9.87971 8.53545 10.5649C8.25162 11.2502 8.17736 12.0042 8.32205 12.7316C8.46675 13.459 8.8239 14.1272 9.34835 14.6517C9.8728 15.1761 10.541 15.5333 11.2684 15.6779C11.9958 15.8226 12.7498 15.7484 13.4351 15.4645C14.1203 15.1807 14.706 14.7001 15.118 14.0834C15.5301 13.4667 15.75 12.7417 15.75 12C15.75 11.0054 15.3549 10.0516 14.6517 9.34835C13.9484 8.64509 12.9946 8.25 12 8.25ZM12 14.25C11.555 14.25 11.12 14.118 10.75 13.8708C10.38 13.6236 10.0916 13.2722 9.92127 12.861C9.75097 12.4499 9.70642 11.9975 9.79323 11.561C9.88005 11.1246 10.0943 10.7237 10.409 10.409C10.7237 10.0943 11.1246 9.88005 11.561 9.79323C11.9975 9.70642 12.4499 9.75097 12.861 9.92127C13.2722 10.0916 13.6236 10.38 13.8708 10.75C14.118 11.12 14.25 11.555 14.25 12C14.25 12.5967 14.0129 13.169 13.591 13.591C13.169 14.0129 12.5967 14.25 12 14.25ZM22.5 5.25H1.5C1.30109 5.25 1.11032 5.32902 0.96967 5.46967C0.829018 5.61032 0.75 5.80109 0.75 6V18C0.75 18.1989 0.829018 18.3897 0.96967 18.5303C1.11032 18.671 1.30109 18.75 1.5 18.75H22.5C22.6989 18.75 22.8897 18.671 23.0303 18.5303C23.171 18.3897 23.25 18.1989 23.25 18V6C23.25 5.80109 23.171 5.61032 23.0303 5.46967C22.8897 5.32902 22.6989 5.25 22.5 5.25ZM18.1547 17.25H5.84531C5.5935 16.3984 5.13263 15.6233 4.50467 14.9953C3.87671 14.3674 3.10162 13.9065 2.25 13.6547V10.3453C3.10162 10.0935 3.87671 9.63263 4.50467 9.00467C5.13263 8.37671 5.5935 7.60162 5.84531 6.75H18.1547C18.4065 7.60162 18.8674 8.37671 19.4953 9.00467C20.1233 9.63263 20.8984 10.0935 21.75 10.3453V13.6547C20.8984 13.9065 20.1233 14.3674 19.4953 14.9953C18.8674 15.6233 18.4065 16.3984 18.1547 17.25ZM21.75 8.75344C20.8504 8.36662 20.1334 7.64959 19.7466 6.75H21.75V8.75344ZM4.25344 6.75C3.86662 7.64959 3.14959 8.36662 2.25 8.75344V6.75H4.25344ZM2.25 15.2466C3.14959 15.6334 3.86662 16.3504 4.25344 17.25H2.25V15.2466ZM19.7466 17.25C20.1334 16.3504 20.8504 15.6334 21.75 15.2466V17.25H19.7466Z"
				fill={props.color ?? "#A1A1AA"}
			/>
		</svg>
	);
};
export const ConstructionIcon = (props: IIconProps) => {
	return (
		<svg
			width="27"
			height="26"
			viewBox="0 0 27 26"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M23.1539 6.56445H3.84537C3.41863 6.56445 3.00936 6.73398 2.70761 7.03573C2.40585 7.33749 2.23633 7.74675 2.23633 8.1735V15.4142C2.23633 15.8409 2.40585 16.2502 2.70761 16.552C3.00936 16.8537 3.41863 17.0232 3.84537 17.0232H6.25893V20.2413C6.25893 20.4547 6.3437 20.6593 6.49457 20.8102C6.64545 20.9611 6.85008 21.0458 7.06346 21.0458C7.27683 21.0458 7.48146 20.9611 7.63234 20.8102C7.78322 20.6593 7.86798 20.4547 7.86798 20.2413V17.0232H19.1313V20.2413C19.1313 20.4547 19.216 20.6593 19.3669 20.8102C19.5178 20.9611 19.7224 21.0458 19.9358 21.0458C20.1492 21.0458 20.3538 20.9611 20.5047 20.8102C20.6556 20.6593 20.7403 20.4547 20.7403 20.2413V17.0232H23.1539C23.5806 17.0232 23.9899 16.8537 24.2916 16.552C24.5934 16.2502 24.7629 15.8409 24.7629 15.4142V8.1735C24.7629 7.74675 24.5934 7.33749 24.2916 7.03573C23.9899 6.73398 23.5806 6.56445 23.1539 6.56445ZM23.1539 13.07L18.2574 8.1735H23.1539V13.07ZM8.74189 8.1735L15.9826 15.4142H11.0167L3.84537 8.24289V8.1735H8.74189ZM3.84537 10.5177L8.74189 15.4142H3.84537V10.5177ZM23.1539 15.4142H18.2574L11.0167 8.1735H15.9826L23.1539 15.3458V15.4142Z"
				fill="#5C7DFF"
			/>
		</svg>
	);
};
export const ITServicesIcon = (props: IIconProps) => {
	return (
		<svg
			width="27"
			height="26"
			viewBox="0 0 27 26"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M6.49457 10.3514L3.27648 7.13327C3.20168 7.05855 3.14234 6.96982 3.10185 6.87215C3.06137 6.77449 3.04053 6.6698 3.04053 6.56407C3.04053 6.45834 3.06137 6.35365 3.10185 6.25599C3.14234 6.15832 3.20168 6.06959 3.27648 5.99487L6.49457 2.77679C6.64553 2.62582 6.85028 2.54102 7.06377 2.54102C7.27726 2.54102 7.482 2.62582 7.63297 2.77679C7.78393 2.92775 7.86873 3.13249 7.86873 3.34598C7.86873 3.55948 7.78393 3.76422 7.63297 3.91518L4.98307 6.56407L7.63297 9.21295C7.78393 9.36392 7.86873 9.56866 7.86873 9.78215C7.86873 9.99565 7.78393 10.2004 7.63296 10.3514C7.482 10.5023 7.27726 10.5871 7.06377 10.5871C6.85028 10.5871 6.64553 10.5023 6.49457 10.3514ZM10.5172 10.3514C10.5919 10.4262 10.6806 10.4855 10.7783 10.526C10.876 10.5665 10.9806 10.5873 11.0864 10.5873C11.1921 10.5873 11.2968 10.5665 11.3945 10.526C11.4921 10.4855 11.5809 10.4262 11.6556 10.3514L14.8737 7.13327C14.9485 7.05855 15.0078 6.96982 15.0483 6.87215C15.0888 6.77449 15.1096 6.6698 15.1096 6.56407C15.1096 6.45834 15.0888 6.35365 15.0483 6.25599C15.0078 6.15832 14.9485 6.06959 14.8737 5.99487L11.6556 2.77679C11.5046 2.62582 11.2999 2.54102 11.0864 2.54102C10.8729 2.54102 10.6681 2.62582 10.5172 2.77679C10.3662 2.92775 10.2814 3.13249 10.2814 3.34598C10.2814 3.55948 10.3662 3.76422 10.5172 3.91518L13.1671 6.56407L10.5172 9.21295C10.4424 9.28767 10.383 9.3764 10.3425 9.47407C10.3021 9.57174 10.2812 9.67643 10.2812 9.78215C10.2812 9.88788 10.3021 9.99257 10.3425 10.0902C10.383 10.1879 10.4424 10.2766 10.5172 10.3514ZM20.7406 4.15051H18.3271C18.1137 4.15051 17.9091 4.23527 17.7582 4.38614C17.6073 4.53702 17.5225 4.74165 17.5225 4.95503C17.5225 5.1684 17.6073 5.37303 17.7582 5.52391C17.9091 5.67479 18.1137 5.75955 18.3271 5.75955H20.7406V20.2409H6.25925V13.8048C6.25925 13.5914 6.17448 13.3868 6.02361 13.2359C5.87273 13.085 5.6681 13.0002 5.45472 13.0002C5.24135 13.0002 5.03672 13.085 4.88584 13.2359C4.73496 13.3868 4.6502 13.5914 4.6502 13.8048V20.2409C4.6502 20.6677 4.81973 21.0769 5.12148 21.3787C5.42323 21.6804 5.8325 21.85 6.25925 21.85H20.7406C21.1674 21.85 21.5766 21.6804 21.8784 21.3787C22.1801 21.0769 22.3497 20.6677 22.3497 20.2409V5.75955C22.3497 5.3328 22.1801 4.92354 21.8784 4.62178C21.5766 4.32003 21.1674 4.15051 20.7406 4.15051Z"
				fill="#5C7DFF"
			/>
		</svg>
	);
};
export const HealthcareIcon = (props: IIconProps) => {
	return (
		<svg
			width="27"
			height="26"
			viewBox="0 0 27 26"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M21.9474 15.4134C21.9474 15.652 21.8767 15.8854 21.7441 16.0838C21.6114 16.2823 21.423 16.4369 21.2025 16.5283C20.982 16.6196 20.7393 16.6435 20.5052 16.5969C20.2711 16.5504 20.0561 16.4355 19.8873 16.2667C19.7186 16.0979 19.6036 15.8829 19.5571 15.6488C19.5105 15.4147 19.5344 15.1721 19.6257 14.9515C19.7171 14.731 19.8717 14.5426 20.0702 14.41C20.2687 14.2774 20.502 14.2066 20.7407 14.2066C21.0607 14.2066 21.3677 14.3337 21.594 14.56C21.8203 14.7863 21.9474 15.0933 21.9474 15.4134ZM21.4899 19.3646C21.3131 20.5038 20.7352 21.5424 19.8602 22.2931C18.9853 23.0437 17.8709 23.4571 16.718 23.4586H14.3045C13.0247 23.4572 11.7976 22.9482 10.8927 22.0433C9.98768 21.1383 9.47868 19.9113 9.47735 18.6314V14.5575C7.92214 14.3616 6.49189 13.6048 5.45501 12.4293C4.41813 11.2537 3.8459 9.74017 3.8457 8.17266V4.15006C3.8457 3.72331 4.01523 3.31405 4.31698 3.01229C4.61873 2.71054 5.028 2.54102 5.45475 2.54102H7.06379C7.27716 2.54102 7.48179 2.62578 7.63267 2.77665C7.78355 2.92753 7.86831 3.13216 7.86831 3.34554C7.86831 3.55891 7.78355 3.76354 7.63267 3.91442C7.48179 4.0653 7.27716 4.15006 7.06379 4.15006H5.45475V8.17266C5.45469 8.81202 5.58165 9.44503 5.82825 10.0349C6.07485 10.6248 6.43618 11.1598 6.89126 11.6089C7.34634 12.058 7.8861 12.4122 8.4792 12.651C9.07231 12.8898 9.70693 13.0083 10.3462 12.9998C12.972 12.9656 15.109 10.7622 15.109 8.0892V4.15006H13.5C13.2866 4.15006 13.082 4.0653 12.9311 3.91442C12.7802 3.76354 12.6954 3.55891 12.6954 3.34554C12.6954 3.13216 12.7802 2.92753 12.9311 2.77665C13.082 2.62578 13.2866 2.54102 13.5 2.54102H15.109C15.5357 2.54102 15.945 2.71054 16.2468 3.01229C16.5485 3.31405 16.718 3.72331 16.718 4.15006V8.0892C16.718 11.3918 14.2512 14.1523 11.0864 14.5565V18.6314C11.0864 19.4849 11.4254 20.3035 12.0289 20.907C12.6325 21.5105 13.451 21.8495 14.3045 21.8495H16.718C17.4488 21.8483 18.1575 21.5989 18.728 21.1421C19.2984 20.6853 19.6968 20.0483 19.8577 19.3354C18.8861 19.1171 18.0299 18.5463 17.4549 17.7332C16.88 16.9201 16.627 15.9227 16.745 14.9338C16.8631 13.945 17.3437 13.0351 18.0939 12.3802C18.8441 11.7253 19.8105 11.372 20.8062 11.3885C21.8019 11.4051 22.7561 11.7904 23.4841 12.4699C24.2121 13.1494 24.6621 14.0748 24.7472 15.067C24.8323 16.0592 24.5463 17.0476 23.9446 17.8411C23.3429 18.6346 22.4682 19.1767 21.4899 19.3626V19.3646ZM23.1542 15.4134C23.1542 14.936 23.0127 14.4694 22.7475 14.0725C22.4823 13.6755 22.1053 13.3662 21.6643 13.1835C21.2233 13.0008 20.738 12.953 20.2698 13.0462C19.8016 13.1393 19.3715 13.3692 19.034 13.7067C18.6965 14.0443 18.4666 14.4743 18.3735 14.9425C18.2803 15.4107 18.3281 15.896 18.5108 16.337C18.6935 16.778 19.0028 17.155 19.3997 17.4202C19.7967 17.6854 20.2633 17.8269 20.7407 17.8269C21.3808 17.8269 21.9947 17.5726 22.4473 17.12C22.8999 16.6674 23.1542 16.0535 23.1542 15.4134Z"
				fill="#5C7DFF"
			/>
		</svg>
	);
};
export const ConsultancyIcon = (props: IIconProps) => {
	return (
		<svg
			width="27"
			height="26"
			viewBox="0 0 27 26"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M22.3495 5.7591H18.3269V4.95458C18.3269 4.31446 18.0726 3.70056 17.62 3.24793C17.1674 2.7953 16.5535 2.54102 15.9134 2.54102H11.0862C10.4461 2.54102 9.83221 2.7953 9.37958 3.24793C8.92695 3.70056 8.67266 4.31446 8.67266 4.95458V5.7591H4.65006C4.22331 5.7591 3.81405 5.92862 3.51229 6.23038C3.21054 6.53213 3.04102 6.9414 3.04102 7.36814V20.2405C3.04102 20.6672 3.21054 21.0765 3.51229 21.3782C3.81405 21.68 4.22331 21.8495 4.65006 21.8495H22.3495C22.7763 21.8495 23.1855 21.68 23.4873 21.3782C23.789 21.0765 23.9586 20.6672 23.9586 20.2405V7.36814C23.9586 6.9414 23.789 6.53213 23.4873 6.23038C23.1855 5.92862 22.7763 5.7591 22.3495 5.7591ZM10.2817 4.95458C10.2817 4.74121 10.3665 4.53657 10.5173 4.3857C10.6682 4.23482 10.8729 4.15006 11.0862 4.15006H15.9134C16.1267 4.15006 16.3314 4.23482 16.4822 4.3857C16.6331 4.53657 16.7179 4.74121 16.7179 4.95458V5.7591H10.2817V4.95458ZM22.3495 7.36814V11.5527C19.634 13.0307 16.5915 13.8049 13.4998 13.8043C10.4082 13.8049 7.36574 13.0311 4.65006 11.5537V7.36814H22.3495ZM22.3495 20.2405H4.65006V13.3658C7.40547 14.7136 10.4324 15.4139 13.4998 15.4134C16.5672 15.4134 19.5941 14.7128 22.3495 13.3648V20.2405ZM11.0862 11.3907C11.0862 11.1774 11.171 10.9727 11.3219 10.8219C11.4727 10.671 11.6774 10.5862 11.8907 10.5862H15.1088C15.3222 10.5862 15.5268 10.671 15.6777 10.8219C15.8286 10.9727 15.9134 11.1774 15.9134 11.3907C15.9134 11.6041 15.8286 11.8088 15.6777 11.9596C15.5268 12.1105 15.3222 12.1953 15.1088 12.1953H11.8907C11.6774 12.1953 11.4727 12.1105 11.3219 11.9596C11.171 11.8088 11.0862 11.6041 11.0862 11.3907Z"
				fill="#5C7DFF"
			/>
		</svg>
	);
};
export const EnergyIcon = (props: IIconProps) => {
	return (
		<svg
			width="27"
			height="26"
			viewBox="0 0 27 26"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M18.3269 23.459C18.3269 23.6724 18.2421 23.877 18.0913 24.0279C17.9404 24.1788 17.7357 24.2635 17.5224 24.2635H9.47716C9.26379 24.2635 9.05916 24.1788 8.90828 24.0279C8.7574 23.877 8.67264 23.6724 8.67264 23.459C8.67264 23.2456 8.7574 23.041 8.90828 22.8901C9.05916 22.7393 9.26379 22.6545 9.47716 22.6545H17.5224C17.7357 22.6545 17.9404 22.7393 18.0913 22.8901C18.2421 23.041 18.3269 23.2456 18.3269 23.459ZM22.3495 10.5867C22.353 11.9279 22.05 13.2521 21.4637 14.4583C20.8774 15.6646 20.0233 16.7209 18.9665 17.5468C18.7689 17.6983 18.6085 17.8929 18.4977 18.1158C18.3868 18.3387 18.3284 18.5841 18.3269 18.833V19.4364C18.3269 19.8632 18.1574 20.2724 17.8556 20.5742C17.5539 20.8759 17.1446 21.0455 16.7179 21.0455H10.2817C9.85494 21.0455 9.44567 20.8759 9.14392 20.5742C8.84216 20.2724 8.67264 19.8632 8.67264 19.4364V18.833C8.67248 18.587 8.61591 18.3444 8.5073 18.1237C8.39869 17.9029 8.24092 17.7101 8.04612 17.5599C6.99213 16.739 6.13866 15.6892 5.55029 14.4898C4.96192 13.2904 4.65407 11.9729 4.65003 10.637C4.62389 5.84302 8.49766 1.85259 13.2866 1.73694C14.4665 1.70851 15.6403 1.91641 16.7387 2.3484C17.8372 2.78039 18.8381 3.42775 19.6826 4.25237C20.527 5.07699 21.198 6.06222 21.6561 7.15005C22.1141 8.23789 22.3498 9.40635 22.3495 10.5867ZM20.7405 10.5867C20.7407 9.62091 20.5478 8.66485 20.173 7.77476C19.7983 6.88468 19.2492 6.07856 18.5582 5.40386C17.8672 4.72916 17.0482 4.19952 16.1494 3.8461C15.2507 3.49269 14.2903 3.32265 13.3248 3.34599C9.40274 3.43851 6.23796 6.70386 6.25908 10.6259C6.26268 11.7186 6.5148 12.7962 6.99637 13.7772C7.47794 14.7581 8.17634 15.6166 9.0387 16.2877C9.42632 16.589 9.73985 16.9751 9.9553 17.4163C10.1707 17.8574 10.2824 18.3421 10.2817 18.833V19.4364H12.6952V14.9422L9.71248 11.9604C9.56152 11.8094 9.47671 11.6047 9.47671 11.3912C9.47671 11.1777 9.56152 10.973 9.71248 10.822C9.86344 10.671 10.0682 10.5862 10.2817 10.5862C10.4952 10.5862 10.6999 10.671 10.8509 10.822L13.4998 13.4719L16.1487 10.822C16.2234 10.7473 16.3121 10.688 16.4098 10.6475C16.5075 10.6071 16.6121 10.5862 16.7179 10.5862C16.8236 10.5862 16.9282 10.6071 17.0259 10.6475C17.1236 10.688 17.2123 10.7473 17.2871 10.822C17.3618 10.8967 17.4211 10.9855 17.4615 11.0832C17.502 11.1808 17.5228 11.2855 17.5228 11.3912C17.5228 11.4969 17.502 11.6016 17.4615 11.6992C17.4211 11.7969 17.3618 11.8856 17.2871 11.9604L14.3043 14.9422V19.4364H16.7179V18.833C16.7187 18.3406 16.8322 17.8549 17.0496 17.4131C17.2669 16.9712 17.5824 16.5849 17.9719 16.2837C18.8369 15.6077 19.5359 14.7431 20.0158 13.7557C20.4956 12.7684 20.7434 11.6844 20.7405 10.5867Z"
				fill="#5C7DFF"
			/>
		</svg>
	);
};
export const AgricultureIcon = (props: IIconProps) => {
	return (
		<svg
			width="27"
			height="26"
			viewBox="0 0 27 26"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M25.5304 4.13941C25.5189 3.94259 25.4356 3.75685 25.2962 3.61744C25.1568 3.47802 24.971 3.39466 24.7742 3.38316C19.5689 3.08147 15.3894 4.66436 13.5934 7.62903C12.4067 9.58904 12.4087 11.9694 13.5773 14.2402C12.9121 15.032 12.426 15.9582 12.1523 16.9554L10.5161 15.3132C11.3025 13.671 11.2723 11.9594 10.4155 10.5374C9.08803 8.34606 6.02381 7.17145 2.21943 7.39471C2.02261 7.4062 1.83686 7.48957 1.69745 7.62898C1.55804 7.76839 1.47468 7.95414 1.46318 8.15096C1.23892 11.9553 2.41453 15.0196 4.60584 16.347C5.32897 16.7888 6.15993 17.0227 7.00734 17.0228C7.82986 17.0127 8.63959 16.8178 9.37665 16.4526L11.8908 18.9667V21.8499C11.8908 22.0633 11.9755 22.268 12.1264 22.4188C12.2773 22.5697 12.4819 22.6545 12.6953 22.6545C12.9087 22.6545 13.1133 22.5697 13.2642 22.4188C13.4151 22.268 13.4998 22.0633 13.4998 21.8499V18.8843C13.4962 17.6044 13.9318 16.362 14.7338 15.3645C15.7685 15.9053 16.9161 16.1947 18.0836 16.2092C19.2123 16.2129 20.3201 15.9045 21.2846 15.3182C24.2492 13.5242 25.8362 9.34467 25.5304 4.13941ZM5.43551 14.9713C3.89284 14.037 3.01993 11.8256 3.04105 8.9776C5.88905 8.95347 8.10048 9.82939 9.03473 11.3721C9.52247 12.1766 9.60192 13.1149 9.28212 14.0813L6.82733 11.6265C6.67522 11.482 6.47268 11.4026 6.2629 11.4053C6.05311 11.408 5.85267 11.4925 5.70431 11.6409C5.55596 11.7892 5.47143 11.9897 5.46874 12.1995C5.46605 12.4092 5.54543 12.6118 5.68994 12.7639L8.14473 15.2187C7.1783 15.5385 6.24103 15.459 5.43551 14.9713ZM20.4509 13.9435C19.1033 14.7591 17.5194 14.8214 15.9104 14.1446L21.3097 8.74429C21.4542 8.59219 21.5336 8.38965 21.5309 8.17986C21.5282 7.97007 21.4437 7.76963 21.2953 7.62128C21.147 7.47292 20.9465 7.38839 20.7368 7.38571C20.527 7.38302 20.3244 7.46239 20.1723 7.6069L14.772 13.0002C14.0922 11.3912 14.1535 9.80626 14.9731 8.45969C16.375 6.1467 19.6997 4.85745 23.9566 4.95701C24.0531 9.21293 22.7659 12.5416 20.4509 13.9435Z"
				fill="#5C7DFF"
			/>
		</svg>
	);
};
export const LogisticsIcon = (props: IIconProps) => {
	return (
		<svg
			width="27"
			height="26"
			viewBox="0 0 27 26"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M23.1219 6.78065L14.2721 1.93844C14.0357 1.80779 13.7699 1.73926 13.4998 1.73926C13.2296 1.73926 12.9639 1.80779 12.7275 1.93844L3.87772 6.78267C3.62499 6.92095 3.41402 7.12455 3.26684 7.3722C3.11966 7.61986 3.04167 7.90249 3.04102 8.19058V17.8086C3.04167 18.0967 3.11966 18.3793 3.26684 18.627C3.41402 18.8747 3.62499 19.0783 3.87772 19.2165L12.7275 24.0608C12.9639 24.1914 13.2296 24.26 13.4998 24.26C13.7699 24.26 14.0357 24.1914 14.2721 24.0608L23.1219 19.2165C23.3746 19.0783 23.5856 18.8747 23.7327 18.627C23.8799 18.3793 23.9579 18.0967 23.9586 17.8086V8.19158C23.9585 7.90298 23.8807 7.61972 23.7335 7.37149C23.5863 7.12326 23.375 6.91918 23.1219 6.78065ZM13.4998 3.34635L21.5792 7.77122L18.5854 9.41043L10.505 4.98557L13.4998 3.34635ZM13.4998 12.1961L5.42039 7.77122L8.82955 5.90473L16.9089 10.3296L13.4998 12.1961ZM4.65006 9.17913L12.6953 13.5819V22.2094L4.65006 17.8096V9.17913ZM22.3495 17.8056L14.3043 22.2094V13.5859L17.5224 11.825V15.4142C17.5224 15.6275 17.6072 15.8322 17.758 15.9831C17.9089 16.1339 18.1135 16.2187 18.3269 16.2187C18.5403 16.2187 18.7449 16.1339 18.8958 15.9831C19.0467 15.8322 19.1314 15.6275 19.1314 15.4142V10.9441L22.3495 9.17913V17.8046V17.8056Z"
				fill="#5C7DFF"
			/>
		</svg>
	);
};
export const EducationIcon = (props: IIconProps) => {
	return (
		<svg
			width="27"
			height="26"
			viewBox="0 0 27 26"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M25.9455 9.07256L13.8777 2.63639C13.7613 2.57441 13.6314 2.54199 13.4995 2.54199C13.3677 2.54199 13.2378 2.57441 13.1214 2.63639L1.05359 9.07256C0.924867 9.14116 0.817219 9.24346 0.742156 9.36852C0.667094 9.49358 0.627441 9.63669 0.627441 9.78255C0.627441 9.92841 0.667094 10.0715 0.742156 10.1966C0.817219 10.3216 0.924867 10.4239 1.05359 10.4925L3.84528 11.9819V16.8513C3.84444 17.2465 3.98988 17.628 4.25357 17.9223C5.57097 19.3895 8.52256 21.8504 13.4995 21.8504C15.1498 21.864 16.7876 21.5651 18.3267 20.9694V24.2639C18.3267 24.4773 18.4114 24.6819 18.5623 24.8328C18.7132 24.9837 18.9178 25.0685 19.1312 25.0685C19.3446 25.0685 19.5492 24.9837 19.7001 24.8328C19.8509 24.6819 19.9357 24.4773 19.9357 24.2639V20.192C20.9848 19.5864 21.9328 18.8206 22.7455 17.9223C23.0092 17.628 23.1546 17.2465 23.1538 16.8513V11.9819L25.9455 10.4925C26.0742 10.4239 26.1818 10.3216 26.2569 10.1966C26.332 10.0715 26.3716 9.92841 26.3716 9.78255C26.3716 9.63669 26.332 9.49358 26.2569 9.36852C26.1818 9.24346 26.0742 9.14116 25.9455 9.07256ZM13.4995 20.2413C9.14808 20.2413 6.58869 18.1154 5.45432 16.8513V12.8397L13.1214 16.9287C13.2378 16.9907 13.3677 17.0231 13.4995 17.0231C13.6314 17.0231 13.7613 16.9907 13.8777 16.9287L18.3267 14.5564V19.2166C17.0595 19.8079 15.4626 20.2413 13.4995 20.2413ZM21.5447 16.8473C21.0625 17.3824 20.5229 17.863 19.9357 18.2803V13.6975L21.5447 12.8397V16.8473ZM19.5334 12.0895L19.5113 12.0764L13.8797 9.07256C13.6918 8.97659 13.4737 8.95827 13.2724 9.02154C13.0712 9.08482 12.9028 9.22463 12.8036 9.41085C12.7044 9.59707 12.6824 9.8148 12.7422 10.0171C12.802 10.2195 12.9389 10.3902 13.1234 10.4925L17.8238 13.0006L13.4995 15.3066L3.14132 9.78255L13.4995 4.25851L23.8577 9.78255L19.5334 12.0895Z"
				fill="#5C7DFF"
			/>
		</svg>
	);
};
export const EmptyStateIcon = (props: IIconProps) => {
	return (
		<svg
			width="48"
			height="48"
			viewBox="0 0 48 48"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M43.6875 27.6075L35.8931 9.86251C35.837 9.73458 35.7575 9.61822 35.6587 9.51939C34.6039 8.46486 33.1734 7.87246 31.6819 7.87246C30.1903 7.87246 28.7598 8.46486 27.705 9.51939C27.4944 9.7297 27.3757 10.0149 27.375 10.3125V15.375H20.625V10.3125C20.6251 10.1645 20.596 10.0179 20.5394 9.88119C20.4828 9.74445 20.3997 9.62023 20.295 9.51564C19.2401 8.46111 17.8097 7.86871 16.3181 7.86871C14.8266 7.86871 13.3961 8.46111 12.3412 9.51564C12.2425 9.61447 12.163 9.73083 12.1069 9.85876L4.31248 27.6075C3.72105 28.7702 3.40104 30.0518 3.37652 31.356C3.35201 32.6602 3.62363 33.953 4.17095 35.137C4.71827 36.3211 5.52703 37.3656 6.53636 38.1919C7.54569 39.0182 8.7293 39.6048 9.9981 39.9076C11.2669 40.2103 12.5879 40.2214 13.8615 39.9398C15.1352 39.6583 16.3285 39.0915 17.3514 38.2822C18.3744 37.4728 19.2005 36.442 19.7675 35.2672C20.3345 34.0925 20.6277 32.8044 20.625 31.5V17.625H27.375V31.5C27.3722 32.8044 27.6654 34.0925 28.2324 35.2672C28.7994 36.442 29.6255 37.4728 30.6485 38.2822C31.6715 39.0915 32.8647 39.6583 34.1384 39.9398C35.4121 40.2214 36.7331 40.2103 38.0019 39.9076C39.2707 39.6048 40.4543 39.0182 41.4636 38.1919C42.4729 37.3656 43.2817 36.3211 43.829 35.137C44.3763 33.953 44.648 32.6602 44.6234 31.356C44.5989 30.0518 44.2789 28.7702 43.6875 27.6075ZM12 37.875C10.7391 37.875 9.50658 37.5011 8.45822 36.8006C7.40986 36.1001 6.59276 35.1045 6.11025 33.9396C5.62774 32.7747 5.50149 31.4929 5.74748 30.2563C5.99346 29.0197 6.60062 27.8838 7.49218 26.9922C8.38374 26.1006 9.51965 25.4935 10.7563 25.2475C11.9929 25.0015 13.2747 25.1278 14.4396 25.6103C15.6045 26.0928 16.6001 26.9099 17.3006 27.9583C18.0011 29.0066 18.375 30.2392 18.375 31.5C18.375 33.1908 17.7033 34.8123 16.5078 36.0078C15.3122 37.2034 13.6907 37.875 12 37.875ZM12 22.875C10.802 22.8746 9.61722 23.1249 8.52186 23.61L14.0737 10.9763C14.6605 10.4537 15.4111 10.1522 16.1963 10.1238C16.9815 10.0954 17.752 10.3419 18.375 10.8206V25.6875C17.5672 24.8001 16.5828 24.0914 15.4849 23.6071C14.387 23.1227 13.2 22.8734 12 22.875ZM29.625 10.8225C30.248 10.3437 31.0184 10.0973 31.8036 10.1257C32.5888 10.1541 33.3395 10.4556 33.9262 10.9781L39.4781 23.61C37.8256 22.8778 35.9845 22.6844 34.216 23.0573C32.4474 23.4302 30.8412 24.3504 29.625 25.6875V10.8225ZM36 37.875C34.7391 37.875 33.5066 37.5011 32.4582 36.8006C31.4099 36.1001 30.5928 35.1045 30.1102 33.9396C29.6277 32.7747 29.5015 31.4929 29.7475 30.2563C29.9935 29.0197 30.6006 27.8838 31.4922 26.9922C32.3837 26.1006 33.5196 25.4935 34.7563 25.2475C35.9929 25.0015 37.2747 25.1278 38.4396 25.6103C39.6045 26.0928 40.6001 26.9099 41.3006 27.9583C42.0011 29.0066 42.375 30.2392 42.375 31.5C42.375 33.1908 41.7033 34.8123 40.5078 36.0078C39.3122 37.2034 37.6907 37.875 36 37.875Z"
				fill="#71717A"
			/>
		</svg>
	);
};

export const CheckNewIcon = (props: IIconProps) => {
	return (
		<svg
			width="23"
			height="23"
			viewBox="0 0 23 23"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			{...props}>
			<path
				d="M11.5 2.84961C9.73233 2.84961 8.00436 3.37378 6.53459 4.35585C5.06483 5.33791 3.91929 6.73376 3.24283 8.36688C2.56637 9.99999 2.38938 11.797 2.73424 13.5307C3.07909 15.2644 3.93031 16.8569 5.18024 18.1069C6.43017 19.3568 8.02268 20.208 9.75638 20.5529C11.4901 20.8977 13.2871 20.7207 14.9202 20.0443C16.5534 19.3678 17.9492 18.2223 18.9313 16.7525C19.9133 15.2828 20.4375 13.5548 20.4375 11.7871C20.435 9.41751 19.4926 7.14567 17.817 5.47011C16.1414 3.79454 13.8696 2.85211 11.5 2.84961ZM15.4239 10.211L10.6114 15.0235C10.5476 15.0874 10.4717 15.1381 10.3883 15.1727C10.3048 15.2073 10.2154 15.2251 10.125 15.2251C10.0347 15.2251 9.94519 15.2073 9.86173 15.1727C9.77827 15.1381 9.70245 15.0874 9.6386 15.0235L7.5761 12.961C7.44709 12.832 7.37462 12.657 7.37462 12.4746C7.37462 12.2922 7.44709 12.1172 7.5761 11.9882C7.7051 11.8592 7.88007 11.7867 8.0625 11.7867C8.24494 11.7867 8.41991 11.8592 8.54891 11.9882L10.125 13.5652L14.4511 9.2382C14.515 9.17433 14.5908 9.12366 14.6743 9.08909C14.7577 9.05452 14.8472 9.03673 14.9375 9.03673C15.0278 9.03673 15.1173 9.05452 15.2007 9.08909C15.2842 9.12366 15.36 9.17433 15.4239 9.2382C15.4878 9.30208 15.5385 9.37791 15.573 9.46137C15.6076 9.54483 15.6254 9.63428 15.6254 9.72461C15.6254 9.81494 15.6076 9.90439 15.573 9.98785C15.5385 10.0713 15.4878 10.1471 15.4239 10.211Z"
				fill="#335CFF"
			/>
		</svg>
	);
};

export const AfricaSkillzLogoIcon = (props: IIconProps) => {
	return (
		<svg
			width="109"
			height="40"
			viewBox="0 0 109 40"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			xmlnsXlink="http://www.w3.org/1999/xlink"
			{...props}>
			<rect y="0.615234" width="108.087" height="38.8437" fill="url(#pattern0_1872_12012)" />
			<defs>
				<pattern
					id="pattern0_1872_12012"
					patternContentUnits="objectBoundingBox"
					width="1"
					height="1">
					<use xlinkHref="#image0_1872_12012" transform="scale(0.00390625 0.0108696)" />
				</pattern>
				<image
					id="image0_1872_12012"
					width="256"
					height="92"
					preserveAspectRatio="none"
					xlinkHref="data:image/png;base64,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"
				/>
			</defs>
		</svg>
	);
};
export const AfricaSkillzLogoColoredIcon = (props: IIconProps) => {
	return (
		<svg
			width="147"
			height="63"
			viewBox="0 0 147 63"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
			xmlnsXlink="http://www.w3.org/1999/xlink"
			{...props}>
			<rect x="8" y="8" width="130.783" height="47" fill="url(#pattern0_1872_9389)" />
			<defs>
				<pattern
					id="pattern0_1872_9389"
					patternContentUnits="objectBoundingBox"
					width="1"
					height="1">
					<use xlinkHref="#image0_1872_9389" transform="scale(0.00390625 0.0108696)" />
				</pattern>
				<image
					id="image0_1872_9389"
					width="256"
					height="92"
					preserveAspectRatio="none"
					xlinkHref="data:image/png;base64,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"
				/>
			</defs>
		</svg>
	);
};
