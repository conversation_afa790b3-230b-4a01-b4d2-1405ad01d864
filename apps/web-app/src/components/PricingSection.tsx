"use client";

import { useState } from "react";
import { Button } from "./ui";

import Image from "next/image";

interface PricingPlan {
	id: string;
	name: string;
	price: number | string;
	annualPrice: number | string;
	description: string;
	features: string[];
	isPopular?: boolean;
	buttonVariant: "primary" | "outline";
	// link: string;
}

const pricingPlans: PricingPlan[] = [
	{
		id: "1",
		name: "Basic",
		price: "Free",
		annualPrice: "Free",
		description: "Perfect for small businesses",
		features: [
			"5 job postings per month",
			"Basic candidate search",
			"30-day job listings",
			"Email support",
			"Basic analytics",
		],
		buttonVariant: "primary",
	},
	{
		id: "2",
		name: "Starter",
		price: 49,
		annualPrice: 39,
		description: "Ideal for growing companies",
		features: [
			"15 job postings per month",
			"Detailed analytics",
			"Advanced candidate search",
			"Featured job listings",
			"Priority email support",
			"Team collaboration tools",
		],
		isPopular: false,
		buttonVariant: "primary",
	},
	{
		id: "3",
		name: "Professional",
		price: 129,
		annualPrice: 103,
		description: "For large organizations",
		features: [
			"Unlimited job postings",
			"Premium candidate search",
			"Featured job listings",
			"Dedicated account manager",
			"API access",
			"Custom reporting",
			"Employer branding tools",
		],
		buttonVariant: "primary",
		isPopular: true,
	},
	{
		id: "4",
		name: "Enterprise",
		price: 299,
		annualPrice: 239,
		description: "For Enterprises",
		features: [
			"Unlimited job postings",
			"Premium candidate search",
			"Featured job listings",
			"Dedicated account manager",
			"API access",
			"Custom reporting",
			"Employer branding tools",
		],
		buttonVariant: "primary",
		isPopular: false,
	},
];

const tabs = [
	{ id: "recruiter", label: "Recruiters" },
	{ id: "employers", label: "Employers" },
	{ id: "facilitators", label: "Facilitators" },
];

export default function PricingSection({
	onContinue,
}: {
	onContinue: () => void;
}) {
	const [activeTab, setActiveTab] = useState("recruiter");
	const [isAnnual, setIsAnnual] = useState(false);
	// Remove cardWidth, cardRef, useEffect, and GAP state/logic

	return (
		<section className=" bg-white ">
			<div className=" mx-auto">
				{/* Tab Navigation */}
				<div className="flex items-center justify-between w-full border-y border-[#E4E4E7] py-4">
					<div className="flex justify-center">
						<div className="bg-neutral-200 rounded-[10px] md:rounded-[12px] p-1 flex flex-wrap gap-2 ">
							{tabs.map((tab) => (
								<button
									key={tab.id}
									onClick={() => setActiveTab(tab.id)}
									className={`px-6 py-2 rounded-[6px] md:rounded-[8px] cursor-pointer  text-[14px] leading-[22px] font-medium transition-all duration-200 ${
										activeTab === tab.id
											? "bg-white text-neutral-700 shadow-sm"
											: "text-neutral-600 hover:text-neutral-700"
									}`}>
									{tab.label}
								</button>
							))}
						</div>
					</div>

					{/* Billing Toggle */}
					<div className="flex justify-center items-center gap-2">
						<span
							className={`text-[16px]  leading-[26px] md:leading-6 ${
								!isAnnual
									? "text-neutral-500 font-semibold"
									: "text-neutral-500 font-normal"
							}`}>
							Monthly
						</span>
						<button
							onClick={() => setIsAnnual(!isAnnual)}
							className={`relative w-8 h-4 sm:w-12 sm:h-6 rounded-full transition-colors cursor-pointer duration-200 ${
								isAnnual ? "bg-brand-500" : "bg-neutral-300"
							}`}>
							<div
								className={`absolute top-0.5 w-3 h-3 sm:w-5 sm:h-5 bg-white rounded-full transition-transform duration-200 ${
									isAnnual
										? "translate-x-4 sm:translate-x-6"
										: "translate-x-0.5"
								}`}
							/>
						</button>
						<span
							className={`text-[16px]  leading-[26px] md:leading-6 ${
								isAnnual
									? "text-neutral-500 font-semibold"
									: "text-neutral-500 font-normal"
							}`}>
							Annual{" "}
							<span className="text-green-600 font-medium">(Save 20%)</span>
						</span>
					</div>
				</div>

				{/* Pricing Cards - Carousel on mobile, grid on desktop */}
				<div className="relative py-5">
					<div className="flex items-start gap-5  mx-auto pb-2 overflow-x-auto">
						{pricingPlans.map((plan) => (
							<div
								key={plan.id}
								className={`bg-white w-fit rounded-[16px] p-8 transition-all duration-300 relative mx-auto ${
									plan.isPopular
										? "border border-success-500 md:shadow-lg"
										: "border border-neutral-200 hover:shadow-lg"
								}`}
								style={{
									boxShadow: "0px 16px 32px -12px #0E121B1A",
								}}>
								{/* Plan Header */}
								<div className="">
									<div className="flex justify-between items-center ">
										<span className="px-3 py-1 rounded-full bg-[#EBEFFF] w-fit mr-2">
											<h3 className="text-[#71717A] whitespace-nowrap font-semibold text-[18px] leading-[24px] ">
												{plan.name}
											</h3>
										</span>

										{plan.isPopular && (
											<div>
												<Image
													src="/popularbadge.png"
													alt="Popular"
													width={60}
													height={18}
													className="sm:w-[90px] sm:h-[26px]"
												/>
											</div>
										)}
									</div>
									<div className="mb-3 mt-2 text-left">
										<p className="font-normal text-2xl md:text-[48px] md:leading-[64px] text-neutral-900">
											{(isAnnual ? plan.annualPrice : plan.price) === "Free"
												? "Free"
												: `$${isAnnual ? plan.annualPrice : plan.price}`}
										</p>
									</div>
									<p className="text-[#3F3F46] text-[16px] whitespace-nowrap leading-[26px] font-semibold mb-2">
										{plan.description}
									</p>
								</div>

								{/* Features List */}
								<div className="mb-8 ">
									{plan.features.map((feature, index) => (
										<div
											key={index}
											className="flex items-center gap-2 sm:gap-3 mb-3">
											<svg
												width="23"
												height="22"
												viewBox="0 0 23 22"
												fill="none"
												xmlns="http://www.w3.org/2000/svg">
												<path
													d="M11.75 2.0625C9.98233 2.0625 8.25436 2.58668 6.7846 3.56874C5.31483 4.55081 4.16929 5.94665 3.49283 7.57977C2.81637 9.21288 2.63938 11.0099 2.98424 12.7436C3.32909 14.4773 4.18031 16.0698 5.43024 17.3198C6.68017 18.5697 8.27268 19.4209 10.0064 19.7658C11.7401 20.1106 13.5371 19.9336 15.1702 19.2572C16.8034 18.5807 18.1992 17.4352 19.1813 15.9654C20.1633 14.4956 20.6875 12.7677 20.6875 11C20.685 8.6304 19.7426 6.35856 18.067 4.683C16.3914 3.00743 14.1196 2.065 11.75 2.0625ZM15.6739 9.42391L10.8614 14.2364C10.7976 14.3003 10.7217 14.351 10.6383 14.3856C10.5548 14.4202 10.4654 14.438 10.375 14.438C10.2847 14.438 10.1952 14.4202 10.1117 14.3856C10.0283 14.351 9.95245 14.3003 9.8886 14.2364L7.8261 12.1739C7.69709 12.0449 7.62462 11.8699 7.62462 11.6875C7.62462 11.5051 7.69709 11.3301 7.8261 11.2011C7.9551 11.0721 8.13007 10.9996 8.3125 10.9996C8.49494 10.9996 8.66991 11.0721 8.79891 11.2011L10.375 12.778L14.7011 8.45109C14.765 8.38722 14.8408 8.33655 14.9243 8.30198C15.0077 8.26741 15.0972 8.24962 15.1875 8.24962C15.2778 8.24962 15.3673 8.26741 15.4507 8.30198C15.5342 8.33655 15.61 8.38722 15.6739 8.45109C15.7378 8.51497 15.7885 8.5908 15.823 8.67426C15.8576 8.75772 15.8754 8.84717 15.8754 8.9375C15.8754 9.02783 15.8576 9.11728 15.823 9.20074C15.7885 9.2842 15.7378 9.36003 15.6739 9.42391Z"
													fill="#7692FF"
												/>
											</svg>

											<span className="text-[#71717A] text-[16px] whitespace-nowrap leading-[26px] font-semibold">
												{feature}
											</span>
										</div>
									))}
								</div>

								{/* CTA Button */}
								<Button
									variant={plan.buttonVariant}
									size="lg"
									fullWidth
									onClick={onContinue}
									className="w-full ">
									Get Started
								</Button>
							</div>
						))}
					</div>
					{/* Navigation arrows for mobile only - removed as per design */}
				</div>
			</div>
		</section>
	);
}
