"use client";

import { useState } from "react";
import { testCompleteLoginFlow, testLogin } from "@/utils/loginTest";
import Button from "@ui/ui/Button";

export default function LoginTester() {
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");
	const [results, setResults] = useState<string[]>([]);
	const [isLoading, setIsLoading] = useState(false);

	const addResult = (message: string) => {
		setResults((prev) => [
			...prev,
			`${new Date().toLocaleTimeString()}: ${message}`,
		]);
	};

	const testCompleteFlow = async () => {
		setIsLoading(true);
		addResult("Starting complete login flow test...");

		try {
			const result = await testCompleteLoginFlow();
			if (result.success) {
				addResult(`✅ Complete login flow test successful!`);
				addResult(`Test email: ${result.testEmail}`);
				addResult(`Login data: ${JSON.stringify(result.loginData, null, 2)}`);
			} else {
				addResult(`❌ Complete login flow test failed: ${result.error}`);
				addResult(`Details: ${JSON.stringify(result.details, null, 2)}`);
				if (result.testEmail) {
					addResult(`Test email created: ${result.testEmail}`);
				}
			}
		} catch (error) {
			addResult(`❌ Complete login flow test error: ${error}`);
		}

		setIsLoading(false);
	};

	const testManualLogin = async () => {
		if (!email || !password) {
			addResult("❌ Please provide both email and password");
			return;
		}

		setIsLoading(true);
		addResult(`Testing manual login for ${email}...`);

		try {
			const result = await testLogin(email, password);
			if (result.success) {
				addResult(`✅ Manual login successful!`);
				addResult(`Login data: ${JSON.stringify(result.data, null, 2)}`);
			} else {
				addResult(
					`❌ Manual login failed: ${JSON.stringify(result.error, null, 2)}`
				);
			}
		} catch (error) {
			addResult(`❌ Manual login error: ${error}`);
		}

		setIsLoading(false);
	};

	const clearResults = () => {
		setResults([]);
	};

	return (
		<div className="p-6 max-w-4xl mx-auto bg-white rounded-lg shadow-lg">
			<h2 className="text-2xl font-bold mb-6">Login Functionality Tester</h2>

			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				{/* Automatic Test */}
				<div className="space-y-4">
					<h3 className="text-lg font-semibold">Automatic Test</h3>
					<p className="text-sm text-gray-600">
						This will create a test user and then attempt to login with those
						credentials.
					</p>
					<Button
						onClick={testCompleteFlow}
						disabled={isLoading}
						variant="primary"
						size="md"
						className="w-full">
						{isLoading ? "Testing..." : "Test Complete Login Flow"}
					</Button>
				</div>

				{/* Manual Test */}
				<div className="space-y-4">
					<h3 className="text-lg font-semibold">Manual Test</h3>
					<div>
						<label className="block text-sm font-medium mb-2">Email:</label>
						<input
							type="email"
							value={email}
							onChange={(e) => setEmail(e.target.value)}
							className="w-full p-2 border border-gray-300 rounded-md"
							placeholder="Enter email address"
						/>
					</div>

					<div>
						<label className="block text-sm font-medium mb-2">Password:</label>
						<input
							type="password"
							value={password}
							onChange={(e) => setPassword(e.target.value)}
							className="w-full p-2 border border-gray-300 rounded-md"
							placeholder="Enter password"
						/>
					</div>

					<Button
						onClick={testManualLogin}
						disabled={isLoading || !email || !password}
						variant="primary"
						size="md"
						className="w-full">
						{isLoading ? "Testing..." : "Test Manual Login"}
					</Button>
				</div>
			</div>

			<div className="mt-6 flex justify-center">
				<Button onClick={clearResults} variant="secondary" size="md">
					Clear Results
				</Button>
			</div>

			<div className="mt-6">
				<h3 className="text-lg font-semibold mb-3">Test Results:</h3>
				<div className="bg-gray-100 p-4 rounded-md max-h-96 overflow-y-auto">
					{results.length === 0 ? (
						<p className="text-gray-500">No results yet...</p>
					) : (
						<div className="space-y-2">
							{results.map((result, index) => (
								<div
									key={index}
									className="text-sm font-mono whitespace-pre-wrap">
									{result}
								</div>
							))}
						</div>
					)}
				</div>
			</div>

			<div className="mt-6 p-4 bg-blue-50 rounded-md">
				<h4 className="font-semibold text-blue-800 mb-2">Instructions:</h4>
				<ol className="text-sm text-blue-700 space-y-1">
					<li>
						1. <strong>Automatic Test:</strong> Click &quot;Test Complete Login
						Flow&quot; to create a test user and login automatically
					</li>
					<li>
						2. <strong>Manual Test:</strong> Enter existing credentials and
						click &quot;Test Manual Login&quot;
					</li>
					<li>
						3. Check the results to see if the login functionality is working
						correctly
					</li>
					<li>4. Look for success/error messages and API response data</li>
				</ol>
			</div>
		</div>
	);
}
