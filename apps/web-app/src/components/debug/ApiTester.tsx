"use client";

import { useState } from "react";
import { testEmailVerification, testRegistration } from "@/utils/apiTest";
import Button from "@ui/ui/Button";

export default function ApiTester() {
	const [email, setEmail] = useState("");
	const [code, setCode] = useState("");
	const [results, setResults] = useState<string[]>([]);
	const [isLoading, setIsLoading] = useState(false);

	const addResult = (message: string) => {
		setResults((prev) => [
			...prev,
			`${new Date().toLocaleTimeString()}: ${message}`,
		]);
	};

	const testRegistrationFlow = async () => {
		setIsLoading(true);
		addResult("Testing registration...");

		try {
			const result = await testRegistration();
			if (result.success && result.email) {
				addResult(`Registration successful for: ${result.email}`);
				setEmail(result.email);
			} else {
				addResult(`Registration failed: ${JSON.stringify(result.error)}`);
			}
		} catch (error) {
			addResult(`Registration error: ${error}`);
		}

		setIsLoading(false);
	};

	const testVerificationFlow = async () => {
		if (!email || !code) {
			addResult("Please provide both email and verification code");
			return;
		}

		setIsLoading(true);
		addResult(`Testing verification for ${email} with code ${code}...`);

		try {
			const result = await testEmailVerification(email, code);
			addResult(`Verification successful: ${JSON.stringify(result)}`);
		} catch (error) {
			addResult(`All verification formats failed: ${error}`);
		}

		setIsLoading(false);
	};

	const clearResults = () => {
		setResults([]);
	};

	return (
		<div className="p-6 max-w-2xl mx-auto bg-white rounded-lg shadow-lg">
			<h2 className="text-2xl font-bold mb-6">API Tester</h2>

			<div className="space-y-4">
				<div>
					<label className="block text-sm font-medium mb-2">Email:</label>
					<input
						type="email"
						value={email}
						onChange={(e) => setEmail(e.target.value)}
						className="w-full p-2 border border-gray-300 rounded-md"
						placeholder="Enter email address"
					/>
				</div>

				<div>
					<label className="block text-sm font-medium mb-2">
						Verification Code:
					</label>
					<input
						type="text"
						value={code}
						onChange={(e) => setCode(e.target.value)}
						className="w-full p-2 border border-gray-300 rounded-md"
						placeholder="Enter verification code"
					/>
				</div>

				<div className="flex gap-4">
					<Button
						onClick={testRegistrationFlow}
						disabled={isLoading}
						variant="primary"
						size="md">
						Test Registration
					</Button>

					<Button
						onClick={testVerificationFlow}
						disabled={isLoading || !email || !code}
						variant="primary"
						size="md">
						Test Verification
					</Button>

					<Button onClick={clearResults} variant="secondary" size="md">
						Clear Results
					</Button>
				</div>
			</div>

			<div className="mt-6">
				<h3 className="text-lg font-semibold mb-3">Test Results:</h3>
				<div className="bg-gray-100 p-4 rounded-md max-h-96 overflow-y-auto">
					{results.length === 0 ? (
						<p className="text-gray-500">No results yet...</p>
					) : (
						<div className="space-y-2">
							{results.map((result, index) => (
								<div key={index} className="text-sm font-mono">
									{result}
								</div>
							))}
						</div>
					)}
				</div>
			</div>

			<div className="mt-6 p-4 bg-blue-50 rounded-md">
				<h4 className="font-semibold text-blue-800 mb-2">Instructions:</h4>
				<ol className="text-sm text-blue-700 space-y-1">
					<li>1. Click &quot;Test Registration&quot; to create a test user</li>
					<li>
						2. Check your email (or use a temp email service) for the
						verification code
					</li>
					<li>
						3. Enter the verification code and click &quot;Test
						Verification&quot;
					</li>
					<li>4. Check the results to see which format works</li>
				</ol>
			</div>
		</div>
	);
}
