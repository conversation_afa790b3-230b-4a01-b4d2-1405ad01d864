"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";

interface AuthGuardProps {
	children: React.ReactNode;
	redirectTo?: string;
	requireAuth?: boolean; // true = require auth, false = require no auth (for login pages)
}

export default function AuthGuard({ 
	children, 
	redirectTo = "/dashboard", 
	requireAuth = false 
}: AuthGuardProps) {
	const { isAuthenticated, user, isLoading } = useAuth();
	const router = useRouter();
	const [shouldRender, setShouldRender] = useState(false);

	useEffect(() => {
		// Don't do anything while auth is loading
		if (isLoading) {
			return;
		}

		if (requireAuth) {
			// Protected route - require authentication
			if (!isAuthenticated) {
				router.replace("/");
				return;
			}
		} else {
			// Public route (like login) - redirect if already authenticated
			if (isAuthenticated && user) {
				const userRole = user.role?.name || user.userType;
				switch (userRole) {
					case "recruiter":
					case "employer":
					case "facilitator":
					case "job-seeker":
						router.replace(redirectTo);
						return;
					default:
						router.replace(redirectTo);
						return;
				}
			}
		}

		// If we get here, user should see this page
		setShouldRender(true);
	}, [isAuthenticated, user, isLoading, router, redirectTo, requireAuth]);

	// Show loading state while checking authentication
	if (isLoading || !shouldRender) {
		return (
			<div className="min-h-screen flex items-center justify-center bg-white">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#335CFF] mx-auto mb-4"></div>
					<p className="text-[#71717A] text-sm">Loading...</p>
				</div>
			</div>
		);
	}

	return <>{children}</>;
}
