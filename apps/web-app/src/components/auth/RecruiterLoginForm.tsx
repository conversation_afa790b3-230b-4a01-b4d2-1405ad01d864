"use client";

import Button from "@ui/ui/Button";
import { AfricaSkillzLogoIcon, ArrowRightIcon, RecruiterIcon } from "@ui/icons";
import { useState, useEffect } from "react";
import { Eye, EyeOff, Mail } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import { LoginFormData, loginSchema } from "@/lib/validationSchemas";
import Link from "next/link";
import Image from "next/image";

export default function RecruiterLoginForm() {
	const [formData, setFormData] = useState({
		email: "",
		password: "",
	});
	const [showPassword, setShowPassword] = useState(false);
	const [validationErrors, setValidationErrors] = useState<
		Record<string, string>
	>({});
	const [touchedFields, setTouchedFields] = useState<Record<string, boolean>>(
		{}
	);

	const {
		login,
		isLoading,
		clearAuthError,
		isAuthenticated,
		user,
		error: authError,
	} = useAuth();
	const router = useRouter();

	// Clear auth errors when component mounts
	useEffect(() => {
		clearAuthError();
	}, [clearAuthError]);

	// Redirect to dashboard after successful authentication
	useEffect(() => {
		if (isAuthenticated && user) {
			router.push("/dashboard");
		}
	}, [isAuthenticated, user, router]);

	// Validate individual field
	const validateField = (fieldName: string, value: string) => {
		try {
			switch (fieldName) {
				case "email":
					loginSchema.shape.email.parse(value);
					break;
				case "password":
					loginSchema.shape.password.parse(value);
					break;
			}
			// Clear error if validation passes
			setValidationErrors((prev) => ({
				...prev,
				[fieldName]: "",
			}));
		} catch (error: unknown) {
			if (error && typeof error === "object" && "issues" in error) {
				const zodError = error as { issues: Array<{ message: string }> };
				if (zodError.issues && zodError.issues.length > 0) {
					setValidationErrors((prev) => ({
						...prev,
						[fieldName]: zodError.issues[0].message,
					}));
				}
			}
		}
	};

	// Validate entire form for submission
	const validateForm = () => {
		try {
			loginSchema.parse(formData);
			setValidationErrors({});
			return true;
		} catch (error: unknown) {
			console.log("Form validation error:", error);
			const errors: Record<string, string> = {};
			if (error && typeof error === "object" && "issues" in error) {
				const zodError = error as {
					issues: Array<{ path: string[]; message: string }>;
				};
				zodError.issues.forEach((err) => {
					errors[err.path[0]] = err.message;
				});
			}
			setValidationErrors(errors);
			return false;
		}
	};

	// Handle field blur (when user leaves the field)
	const handleFieldBlur = (fieldName: string, value: string) => {
		console.log("Field blurred:", fieldName, "with value:", value);
		setTouchedFields((prev) => ({
			...prev,
			[fieldName]: true,
		}));
		validateField(fieldName, value);
	};

	// Handle form submission with validation
	const handleSubmit = async () => {
		// Mark all fields as touched on submit
		setTouchedFields({
			email: true,
			password: true,
		});

		if (validateForm()) {
			await login(formData as LoginFormData);
		}
	};

	// Handle input change
	const handleInputChange = (fieldName: string, value: string) => {
		setFormData((prev) => ({
			...prev,
			[fieldName]: value,
		}));

		// Clear validation error when user starts typing
		if (validationErrors[fieldName]) {
			setValidationErrors((prev) => ({
				...prev,
				[fieldName]: "",
			}));
		}
	};

	return (
		<main className="w-full grid grid-cols-[40%_60%]">
			<div className="p-[62px] bg-gradient-to-b from-[#7893FF] to-[#2441B5] h-screen">
				<AfricaSkillzLogoIcon />
				<div className=" relative">
					<div className="relative w-[352px] h-[409px] rounded-[20px] mt-[150px] m-auto">
						<Image src="/recruiter-signup.png" fill alt="sign up avatar" />
					</div>

					<div className="p-4 rounded-2xl w-fit shadow-lg absolute top-[55%] -left-5 bg-white">
						<RecruiterIcon className="w-10 h-10" />
						<p className="font-bold text-[18px] leading-[24px] text-[#18181B] mt-2">
							Mangage Job Listing
						</p>
					</div>
					<div className="p-4 rounded-2xl w-fit shadow-lg absolute top-[70%] right-5 bg-white">
						<span className="px-3 py-1 rounded-[24px] bg-[#D25625] font-medium text-[12px] leading-[16px] text-[#EBEFFF]">
							Best Talents
						</span>
						<p className="font-bold text-[18px] leading-[24px] text-[#18181B] mt-2">
							Recruit Smarter!
						</p>
					</div>
					<div className="p-4 rounded-2xl shadow-lg absolute top-[25%] right-10 bg-white w-fit">
						<p className="font-bold text-[18px] leading-[24px] text-[#18181B] mt-2">
							Create
						</p>
					</div>
				</div>
			</div>

			<div className="w-full h-screen bg-white flex items-center justify-center">
				<div className="w-[400px] max-w-[90%]">
					<div className="text-left mb-8">
						<h2 className="font-bold text-[24px] leading-[32px] text-[#18181B] mb-1">
							Login
						</h2>
					</div>

					<div className="space-y-4 ">
						{/* Email Field */}
						<div>
							<label
								htmlFor="email"
								className="block font-medium text-[14px] leading-[22px] text-[#18181B] mb-1">
								Email Address
							</label>
							<div className="relative">
								<span className="absolute left-3 top-1/2 -translate-y-1/2 text-[#A1A1AA]">
									<Mail className="w-5 h-5" />
								</span>
								<input
									id="email"
									type="email"
									autoComplete="email"
									required
									value={formData.email}
									onChange={(e) => handleInputChange("email", e.target.value)}
									onBlur={(e) => handleFieldBlur("email", e.target.value)}
									placeholder="e.g <EMAIL>"
									className={`w-full pl-10 pr-4 py-2.5 border rounded-lg bg-[#FAFAFA] text-base text-[#18181B] focus:outline-none focus:ring-2 placeholder:text-[#A1A1AA] placeholder:font-regular placeholder:text-[14px] placeholder:leading-[22px] placeholder:opacity-100 ${
										touchedFields.email && validationErrors.email
											? "border-red-500 focus:ring-red-500"
											: "border-[#E4E4E7] focus:ring-[#3F3F46]"
									}`}
								/>
							</div>
							{touchedFields.email && validationErrors.email && (
								<p className="mt-1 text-sm text-red-600">
									{validationErrors.email}
								</p>
							)}
						</div>

						{/* Password Field */}
						<div>
							<label
								htmlFor="password"
								className="block font-medium text-[14px] leading-[22px] text-[#18181B] mb-1">
								Password
							</label>
							<div className="relative">
								<input
									id="password"
									type={showPassword ? "text" : "password"}
									autoComplete="current-password"
									required
									value={formData.password}
									onChange={(e) =>
										handleInputChange("password", e.target.value)
									}
									onBlur={(e) => handleFieldBlur("password", e.target.value)}
									placeholder="Enter password"
									className={`w-full px-4 py-2.5 border rounded-lg bg-[#FAFAFA] text-base text-[#18181B] focus:outline-none focus:ring-2 placeholder:text-[#A1A1AA] placeholder:font-regular placeholder:text-[14px] placeholder:leading-[22px] placeholder:opacity-100 ${
										touchedFields.password && validationErrors.password
											? "border-red-500 focus:ring-red-500"
											: "border-[#E4E4E7] focus:ring-[#3F3F46]"
									}`}
								/>
								<button
									type="button"
									className="absolute right-3 top-1/2 -translate-y-1/2 text-[#A1A1AA]"
									onClick={() => setShowPassword((v) => !v)}
									tabIndex={-1}>
									{showPassword ? (
										<EyeOff className="w-5 h-5" />
									) : (
										<Eye className="w-5 h-5" />
									)}
								</button>
							</div>
							{touchedFields.password && validationErrors.password && (
								<p className="mt-1 text-sm text-red-600">
									{validationErrors.password}
								</p>
							)}
						</div>

						{/* Remember Me and Forgot Password */}
						<div className="flex items-center justify-between">
							{/* <label className="flex items-center">
								<input
									type="checkbox"
									className="w-4 h-4 text-[#335CFF] bg-gray-100 border-gray-300 rounded focus:ring-[#335CFF] focus:ring-2"
								/>
								<span className="ml-2 text-sm text-[#71717A]">Remember me</span>
							</label> */}
							<Link
								href="#"
								className="text-sm text-[#335CFF] hover:underline font-medium ml-auto">
								Forgot Password?
							</Link>
						</div>

						{/* Auth Error Display */}
						{authError && (
							<div className="p-3 bg-red-50 border border-red-200 rounded-lg">
								<p className="text-sm text-red-600">{authError}</p>
							</div>
						)}

						{/* Submit Button */}
						<Button
							variant="primary"
							className="w-full"
							size="md"
							onClick={handleSubmit}
							disabled={isLoading}>
							{isLoading ? "Signing in..." : "Continue"}
						</Button>

						{/* Sign Up Link */}
						<div className="text-center w-full flex justify-center">
							<p className="text-sm font-semibold text-[#71717A] flex items-center">
								Don&apos;t have an account?{" "}
							</p>
							<Link
								href="/auth/sign-up/recruiter"
								className="text-[#335CFF] hover:underline font-medium flex items-center ml-1">
								Signup→
							</Link>
						</div>
					</div>
				</div>
			</div>
		</main>
	);
}
