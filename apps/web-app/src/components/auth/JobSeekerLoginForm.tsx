"use client";

import Button from "@ui/ui/Button";
import { AfricaSkillzLogoIcon } from "@ui/icons";
import { useState, useEffect } from "react";
import { Eye, EyeOff, Mail } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import { LoginFormData, loginSchema } from "@/lib/validationSchemas";
import Link from "next/link";
import Image from "next/image";

export default function JobSeekerLoginForm() {
	const [formData, setFormData] = useState({
		email: "",
		password: "",
	});
	const [showPassword, setShowPassword] = useState(false);
	const [validationErrors, setValidationErrors] = useState<
		Record<string, string>
	>({});
	const [touchedFields, setTouchedFields] = useState<Record<string, boolean>>(
		{}
	);

	const {
		login,
		isLoading,
		clearAuthError,
		isAuthenticated,
		user,
		error: authError,
	} = useAuth();
	const router = useRouter();

	// Clear auth errors when component mounts
	useEffect(() => {
		clearAuthError();
	}, [clearAuthError]);

	// Redirect to dashboard after successful authentication
	useEffect(() => {
		if (isAuthenticated && user) {
			router.push("/dashboard");
		}
	}, [isAuthenticated, user, router]);

	// Validate individual field
	const validateField = (fieldName: string, value: string) => {
		try {
			switch (fieldName) {
				case "email":
					loginSchema.shape.email.parse(value);
					break;
				case "password":
					loginSchema.shape.password.parse(value);
					break;
			}
			// Clear error if validation passes
			setValidationErrors((prev) => ({
				...prev,
				[fieldName]: "",
			}));
		} catch (error: unknown) {
			if (error && typeof error === "object" && "issues" in error) {
				const zodError = error as { issues: Array<{ message: string }> };
				if (zodError.issues && zodError.issues.length > 0) {
					setValidationErrors((prev) => ({
						...prev,
						[fieldName]: zodError.issues[0].message,
					}));
				}
			}
		}
	};

	// Validate entire form for submission
	const validateForm = () => {
		try {
			loginSchema.parse(formData);
			setValidationErrors({});
			return true;
		} catch (error: unknown) {
			console.log("Form validation error:", error);
			const errors: Record<string, string> = {};
			if (error && typeof error === "object" && "issues" in error) {
				const zodError = error as {
					issues: Array<{ path: string[]; message: string }>;
				};
				zodError.issues.forEach((err) => {
					errors[err.path[0]] = err.message;
				});
			}
			setValidationErrors(errors);
			return false;
		}
	};

	// Handle field blur (when user leaves the field)
	const handleFieldBlur = (fieldName: string, value: string) => {
		console.log("Field blurred:", fieldName, "with value:", value);
		setTouchedFields((prev) => ({
			...prev,
			[fieldName]: true,
		}));
		validateField(fieldName, value);
	};

	// Handle form submission with validation
	const handleSubmit = async () => {
		// Mark all fields as touched on submit
		setTouchedFields({
			email: true,
			password: true,
		});

		if (validateForm()) {
			await login(formData as LoginFormData);
		}
	};

	// Handle input change
	const handleInputChange = (fieldName: string, value: string) => {
		setFormData((prev) => ({
			...prev,
			[fieldName]: value,
		}));

		// Clear validation error when user starts typing
		if (validationErrors[fieldName]) {
			setValidationErrors((prev) => ({
				...prev,
				[fieldName]: "",
			}));
		}
	};

	return (
		<main className="w-full grid grid-cols-[40%_60%]">
			<div className="p-[62px] bg-gradient-to-b from-[#7893FF] to-[#2441B5] h-screen">
				<AfricaSkillzLogoIcon />
				<div className=" relative">
					<div className="relative w-[352px] h-[409px] rounded-[20px] mt-[150px] m-auto">
						<Image src="/job-seeker-signup.png" fill alt="sign up avatar" />
					</div>

					<div className="p-4 rounded-2xl w-fit shadow-lg absolute top-[55%] left-0 bg-white">
						<svg
							width="40"
							height="40"
							viewBox="0 0 40 40"
							fill="none"
							xmlns="http://www.w3.org/2000/svg">
							<path
								opacity="0.2"
								d="M6.25 10H13.75V32.5H6.25C5.91848 32.5 5.60054 32.3683 5.36612 32.1339C5.1317 31.8995 5 31.5815 5 31.25V11.25C5 10.9185 5.1317 10.6005 5.36612 10.3661C5.60054 10.1317 5.91848 10 6.25 10ZM33.75 10H26.25V32.5H33.75C34.0815 32.5 34.3995 32.3683 34.6339 32.1339C34.8683 31.8995 35 31.5815 35 31.25V11.25C35 10.9185 34.8683 10.6005 34.6339 10.3661C34.3995 10.1317 34.0815 10 33.75 10Z"
								fill="#335CFF"
							/>
							<path
								d="M33.75 8.75H27.5V7.5C27.5 6.50544 27.1049 5.55161 26.4017 4.84835C25.6984 4.14509 24.7446 3.75 23.75 3.75H16.25C15.2554 3.75 14.3016 4.14509 13.5983 4.84835C12.8951 5.55161 12.5 6.50544 12.5 7.5V8.75H6.25C5.58696 8.75 4.95107 9.01339 4.48223 9.48223C4.01339 9.95107 3.75 10.587 3.75 11.25V31.25C3.75 31.913 4.01339 32.5489 4.48223 33.0178C4.95107 33.4866 5.58696 33.75 6.25 33.75H33.75C34.413 33.75 35.0489 33.4866 35.5178 33.0178C35.9866 32.5489 36.25 31.913 36.25 31.25V11.25C36.25 10.587 35.9866 9.95107 35.5178 9.48223C35.0489 9.01339 34.413 8.75 33.75 8.75ZM15 7.5C15 7.16848 15.1317 6.85054 15.3661 6.61612C15.6005 6.3817 15.9185 6.25 16.25 6.25H23.75C24.0815 6.25 24.3995 6.3817 24.6339 6.61612C24.8683 6.85054 25 7.16848 25 7.5V8.75H15V7.5ZM25 11.25V31.25H15V11.25H25ZM6.25 11.25H12.5V31.25H6.25V11.25ZM33.75 31.25H27.5V11.25H33.75V31.25Z"
								fill="#335CFF"
							/>
						</svg>

						<p className="font-bold text-[18px] leading-[24px] text-[#18181B] mt-2">
							Get Hired!
						</p>
					</div>
					<div className="p-4 rounded-2xl w-fit shadow-lg absolute top-[70%] right-5 bg-white">
						<span className="px-3 py-1 rounded-[24px] bg-[#D25625] font-medium text-[12px] leading-[16px] text-[#EBEFFF]">
							Best Jobs
						</span>
						<p className="font-bold text-[18px] leading-[24px] text-[#18181B] mt-2">
							Top Recruiters
						</p>
					</div>
					<div className="p-4 rounded-2xl shadow-lg absolute top-[25%] right-14 bg-white w-fit max-w-[185px]">
						<p className="font-bold text-[18px] leading-[24px] text-[#18181B]  ">
							Jobs
						</p>
					</div>
				</div>
			</div>

			<div className="w-full h-screen bg-white flex items-center justify-center">
				<div className="w-[400px] max-w-[90%]">
					<div className="text-left mb-8">
						<h2 className="font-bold text-[32px] leading-[40px] text-[#18181B] mb-1">
							Login
						</h2>
					</div>

					<div className="space-y-4">
						{/* Email Field */}
						<div>
							<label
								htmlFor="email"
								className="block font-medium text-[14px] leading-[22px] text-[#18181B] mb-1">
								Email Address
							</label>
							<div className="relative">
								<span className="absolute left-3 top-1/2 -translate-y-1/2 text-[#A1A1AA]">
									<Mail className="w-5 h-5" />
								</span>
								<input
									id="email"
									type="email"
									autoComplete="email"
									required
									value={formData.email}
									onChange={(e) => handleInputChange("email", e.target.value)}
									onBlur={(e) => handleFieldBlur("email", e.target.value)}
									placeholder="e.g <EMAIL>"
									className={`w-full pl-10 pr-4 py-2.5 border rounded-lg bg-[#FAFAFA] text-base text-[#18181B] focus:outline-none focus:ring-2 placeholder:text-[#A1A1AA] placeholder:font-regular placeholder:text-[14px] placeholder:leading-[22px] placeholder:opacity-100 ${
										touchedFields.email && validationErrors.email
											? "border-red-500 focus:ring-red-500"
											: "border-[#E4E4E7] focus:ring-[#3F3F46]"
									}`}
								/>
							</div>
							{touchedFields.email && validationErrors.email && (
								<p className="mt-1 text-sm text-red-600">
									{validationErrors.email}
								</p>
							)}
						</div>

						{/* Password Field */}
						<div>
							<label
								htmlFor="password"
								className="block font-medium text-[14px] leading-[22px] text-[#18181B] mb-1">
								Password
							</label>
							<div className="relative">
								<input
									id="password"
									type={showPassword ? "text" : "password"}
									autoComplete="current-password"
									required
									value={formData.password}
									onChange={(e) =>
										handleInputChange("password", e.target.value)
									}
									onBlur={(e) => handleFieldBlur("password", e.target.value)}
									placeholder="Enter password"
									className={`w-full px-4 py-2.5 border rounded-lg bg-[#FAFAFA] text-base text-[#18181B] focus:outline-none focus:ring-2 placeholder:text-[#A1A1AA] placeholder:font-regular placeholder:text-[14px] placeholder:leading-[22px] placeholder:opacity-100 ${
										touchedFields.password && validationErrors.password
											? "border-red-500 focus:ring-red-500"
											: "border-[#E4E4E7] focus:ring-[#3F3F46]"
									}`}
								/>
								<button
									type="button"
									className="absolute right-3 top-1/2 -translate-y-1/2 text-[#A1A1AA]"
									onClick={() => setShowPassword((v) => !v)}
									tabIndex={-1}>
									{showPassword ? (
										<EyeOff className="w-5 h-5" />
									) : (
										<Eye className="w-5 h-5" />
									)}
								</button>
							</div>
							{touchedFields.password && validationErrors.password && (
								<p className="mt-1 text-sm text-red-600">
									{validationErrors.password}
								</p>
							)}
						</div>

						{/* Remember Me and Forgot Password */}
						<div className="flex items-center justify-between">
							{/* <label className="flex items-center">
								<input
									type="checkbox"
									className="w-4 h-4 text-[#335CFF] bg-gray-100 border-gray-300 rounded focus:ring-[#335CFF] focus:ring-2"
								/>
								<span className="ml-2 text-sm text-[#71717A]">Remember me</span>
							</label> */}
							<Link
								href="#"
								className="text-sm text-[#335CFF] hover:underline font-medium ml-auto">
								Forgot Password?
							</Link>
						</div>

						{/* Auth Error Display */}
						{authError && (
							<div className="p-3 bg-red-50 border border-red-200 rounded-lg">
								<p className="text-sm text-red-600">{authError}</p>
							</div>
						)}

						{/* Submit Button */}
						<Button
							variant="primary"
							className="w-full"
							size="md"
							onClick={handleSubmit}
							disabled={isLoading}>
							{isLoading ? "Signing in..." : "Continue"}
						</Button>

						{/* Sign Up Link */}
						<div className="text-center w-full flex justify-center">
							<p className="text-sm font-semibold text-[#71717A] flex items-center">
								Don&apos;t have an account?{" "}
							</p>
							<Link
								href="/auth/sign-up/job-seeker"
								className="text-[#335CFF] hover:underline font-medium flex items-center ml-1">
								Signup→
							</Link>
						</div>
					</div>
				</div>
			</div>
		</main>
	);
}
