import { z } from "zod";

// Password validation schema based on API requirements
const passwordSchema = z
	.string()
	.min(8, "Password must be at least 8 characters")
	.regex(/[A-Z]/, "Password must contain at least one uppercase letter")
	.regex(/[a-z]/, "Password must contain at least one lowercase letter")
	.regex(/\d/, "Password must contain at least one number")
	.regex(
		/[!@#$%^&*(),.?":{}|<>]/,
		"Password must contain at least one special character"
	);

// Full name validation (must have at least first and last name)
const fullNameSchema = z
	.string()
	.min(3, "Please enter your full name")
	.regex(/^[a-zA-Z\s]+$/, "Name can only contain letters and spaces")
	.refine((name) => {
		const parts = name.trim().split(/\s+/);
		return parts.length >= 2 && parts.every((part) => part.length > 0);
	}, "Please enter both first and last name");

// Email validation
const emailSchema = z
	.string()
	.email("Please enter a valid email address")
	.min(1, "Email is required");

// Company/Organization name validation
const organizationNameSchema = z
	.string()
	.min(2, "Organization name must be at least 2 characters")
	.max(100, "Organization name must be less than 100 characters");

// Individual name validation (for job seekers)
const nameSchema = z
	.string()
	.min(2, "Name must be at least 2 characters")
	.max(50, "Name must be less than 50 characters")
	.regex(/^[a-zA-Z\s]+$/, "Name can only contain letters and spaces");

// Registration form validation schema (for recruiters/employers/facilitators)
export const registrationSchema = z
	.object({
		companyName: organizationNameSchema,
		representativeName: fullNameSchema,
		email: emailSchema,
		password: passwordSchema,
		confirmPassword: z.string().min(1, "Please confirm your password"),
	})
	.refine((data) => data.password === data.confirmPassword, {
		message: "Passwords do not match",
		path: ["confirmPassword"],
	});

// Job seeker registration form validation schema
export const jobSeekerRegistrationSchema = z
	.object({
		firstName: nameSchema,
		lastName: nameSchema,
		email: emailSchema,
		password: passwordSchema,
		confirmPassword: z.string().min(1, "Please confirm your password"),
	})
	.refine((data) => data.password === data.confirmPassword, {
		message: "Passwords do not match",
		path: ["confirmPassword"],
	});

// Email verification schema
export const emailVerificationSchema = z.object({
	verifyCode: z
		.string()
		.length(6, "Verification code must be 6 digits")
		.regex(/^\d{6}$/, "Verification code must contain only numbers"),
});

// Login form validation schema
export const loginSchema = z.object({
	email: emailSchema,
	password: z.string().min(1, "Password is required"),
});

// Types derived from schemas
export type RegistrationFormData = z.infer<typeof registrationSchema>;
export type JobSeekerRegistrationFormData = z.infer<
	typeof jobSeekerRegistrationSchema
>;
export type EmailVerificationFormData = z.infer<typeof emailVerificationSchema>;
export type LoginFormData = z.infer<typeof loginSchema>;

// Helper function to extract validation errors
export const getFieldError = (
	errors: Record<string, { message?: string }>,
	fieldName: string
): string | undefined => {
	return errors[fieldName]?.message;
};

// Helper function to check if field has error
export const hasFieldError = (
	errors: Record<string, unknown>,
	fieldName: string
): boolean => {
	return !!errors[fieldName];
};
