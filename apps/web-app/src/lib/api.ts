import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";

// Create axios instance with default config
const api: AxiosInstance = axios.create({
	baseURL:
		process.env.NEXT_PUBLIC_API_BASE_URL ||
		"https://test-render-845v.onrender.com/api/v1",
	timeout: 30000,
	headers: {
		"Content-Type": "application/json",
	},
});

// Request interceptor to add auth token
api.interceptors.request.use(
	(config) => {
		// Don't add token to auth endpoints (register, login, etc.)
		const authEndpoints = [
			"/auth/register",
			"/auth/login",
			"/auth/verify",
			"/auth/resend-verification",
			"/auth/send-otp",
			"/auth/forgot-password",
			"/auth/reset-password",
		];
		const isAuthEndpoint = authEndpoints.some((endpoint) =>
			config.url?.includes(endpoint)
		);

		if (!isAuthEndpoint) {
			// Get token from localStorage or Redux store
			const token =
				typeof window !== "undefined"
					? localStorage.getItem("authToken")
					: null;

			if (token) {
				config.headers.Authorization = `Bearer ${token}`;
			}
		}

		return config;
	},
	(error) => {
		return Promise.reject(error);
	}
);

// Response interceptor for error handling and token refresh
api.interceptors.response.use(
	(response: AxiosResponse) => {
		return response;
	},
	async (error) => {
		const originalRequest = error.config;

		// Handle network errors
		if (
			error.code === "ECONNRESET" ||
			error.code === "ENOTFOUND" ||
			error.code === "ECONNREFUSED"
		) {
			console.error("Network error:", error.message);
			// You might want to show a user-friendly message here
		}

		// Handle 401 errors (token expired)
		if (error.response?.status === 401 && !originalRequest._retry) {
			originalRequest._retry = true;

			// Don't try to refresh token for auth endpoints
			const authEndpoints = [
				"/auth/register",
				"/auth/login",
				"/auth/verify",
				"/auth/resend-verification",
				"/auth/send-otp",
				"/auth/forgot-password",
				"/auth/reset-password",
				"/auth/refresh-token",
			];

			const isAuthEndpoint = authEndpoints.some((endpoint) =>
				originalRequest.url?.includes(endpoint)
			);

			if (!isAuthEndpoint && typeof window !== "undefined") {
				const refreshToken = localStorage.getItem("refreshToken");

				if (refreshToken) {
					try {
						console.log("🔄 Attempting automatic token refresh...");

						// Try to refresh the token
						const refreshResponse = await axios.post(
							`${api.defaults.baseURL}/auth/refresh-token`,
							{ refreshToken },
							{
								headers: {
									"Content-Type": "application/json",
								},
							}
						);

						if (refreshResponse.data.success && refreshResponse.data.data) {
							// Handle both new nested structure and legacy flat structure
							const responseData = refreshResponse.data.data;
							const tokens = responseData.tokens || responseData;
							const { accessToken, expiresIn } = tokens;
							const newToken = accessToken;

							// Update localStorage
							if (newToken) {
								localStorage.setItem("authToken", newToken);
								localStorage.setItem("accessToken", newToken);
							}
							if (expiresIn) {
								localStorage.setItem("expiresIn", expiresIn.toString());
							}

							// Update the original request with new token
							originalRequest.headers.Authorization = `Bearer ${newToken}`;

							console.log(
								"✅ Token refreshed successfully, retrying original request"
							);

							// Retry the original request
							return api(originalRequest);
						}
					} catch (refreshError) {
						console.error("❌ Token refresh failed:", refreshError);
						// Fall through to logout logic
					}
				}
			}

			// If we get here, token refresh failed or no refresh token
			console.log("🚪 Redirecting to login due to authentication failure");
			localStorage.removeItem("authToken");
			localStorage.removeItem("accessToken");
			localStorage.removeItem("refreshToken");
			localStorage.removeItem("expiresIn");
			localStorage.removeItem("user");
			localStorage.removeItem("company");

			// Redirect to website home page
			window.location.href =
				process.env.NEXT_PUBLIC_WEBSITE_URL || "http://localhost:3000";
		}

		return Promise.reject(error);
	}
);

// API endpoints
export const endpoints = {
	// Authentication
	auth: {
		register: "/auth/register",
		login: "/auth/login",
		logout: "/auth/logout",
		verifyEmail: "/auth/verify",
		resendVerification: "/auth/resend-verification",
		sendOtp: "/auth/send-otp",
		forgotPassword: "/auth/forgot-password",
		resetPassword: "/auth/reset-password",
		refreshToken: "/auth/refresh-token",
	},

	// User management
	user: {
		profile: "/user/profile",
		updateProfile: "/user/profile",
		uploadAvatar: "/user/avatar",
		changePassword: "/user/change-password",
	},

	// Company/Organization
	company: {
		create: "/company",
		update: "/company",
		uploadLogo: "/company/logo",
	},

	// Subscription/Payment
	subscription: {
		plans: "/subscription/plans",
		subscribe: "/subscription/subscribe",
		cancel: "/subscription/cancel",
		status: "/subscription/status",
	},
};

// Generic API methods
export const apiMethods = {
	get: <T = unknown>(
		url: string,
		config?: AxiosRequestConfig
	): Promise<AxiosResponse<T>> => api.get(url, config),

	post: <T = unknown>(
		url: string,
		data?: unknown,
		config?: AxiosRequestConfig
	): Promise<AxiosResponse<T>> => api.post(url, data, config),

	put: <T = unknown>(
		url: string,
		data?: unknown,
		config?: AxiosRequestConfig
	): Promise<AxiosResponse<T>> => api.put(url, data, config),

	patch: <T = unknown>(
		url: string,
		data?: unknown,
		config?: AxiosRequestConfig
	): Promise<AxiosResponse<T>> => api.patch(url, data, config),

	delete: <T = unknown>(
		url: string,
		config?: AxiosRequestConfig
	): Promise<AxiosResponse<T>> => api.delete(url, config),
};

export default api;
