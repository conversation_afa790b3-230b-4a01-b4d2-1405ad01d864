const courses = [
	{
		id: "1",
		title: "Advance Data Science",
		courseCompany: "Data Center Academy",
		isCourse: true,
		company: "The course cards include visual indicators for categor...",
		companyLogo: "/course-img1.png",
		workType: "Full-time",
		timezone: "GMT +1",
		city: "San Fransisco, CA",

		tags: [
			{
				label: "MSC",
			},
			{
				label: "Postgraduate",
				bgColor: "bg-[#FEF6F6]",
				textColor: "text-[#F17171]",
				borderColor: "text-[#F17171]",
			},
		],
		isBookmarked: false,
		showTime: false,
	},
	{
		id: "2",
		title: "Advance Data Science",
		courseCompany: "Data Center Academy",
		isCourse: true,
		company: "The course cards include visual indicators for categor...",
		companyLogo: "/course-img2.png",
		workType: "Full-time",
		timezone: "GMT +1",
		city: "San Fransisco, CA",

		tags: [
			{
				label: "MSC",
			},
			{
				label: "Undergraduate",
				bgColor: "bg-[#EBEFFF]",
				textColor: "text-[#335CFF]",
				borderColor: "text-[#335CFF]",
			},
		],
		isBookmarked: false,
		showTime: false,
	},
	// Duplicate the above two jobs three more times with unique ids
	{
		id: "4",
		title: "Advance Data Science",
		courseCompany: "Data Center Academy",
		isCourse: true,
		company: "The course cards include visual indicators for categor...",
		companyLogo: "/course-img2.png",
		workType: "Full-time",
		timezone: "GMT +1",
		city: "San Fransisco, CA",
		tags: [
			{
				label: "MSC",
			},
			{
				label: "Undergraduate",
				bgColor: "bg-[#EBEFFF]",
				textColor: "text-[#335CFF]",
				borderColor: "text-[#335CFF]",
			},
		],
		isBookmarked: false,
		showTime: false,
	},
	{
		id: "3",
		title: "Advance Data Science",
		courseCompany: "Data Center Academy",
		isCourse: true,
		company: "The course cards include visual indicators for categor...",
		companyLogo: "/course-img1.png",
		workType: "Full-time",
		timezone: "GMT +1",
		city: "San Fransisco, CA",
		tags: [
			{
				label: "MSC",
			},
			{
				label: "Postgraduate",
				bgColor: "bg-[#FEF6F6]",
				textColor: "text-[#F17171]",
				borderColor: "text-[#F17171]",
			},
		],
		isBookmarked: false,
		showTime: false,
	},
	{
		id: "5",
		title: "Advance Data Science",
		courseCompany: "Data Center Academy",
		isCourse: true,
		company: "The course cards include visual indicators for categor...",
		companyLogo: "/course-img1.png",
		workType: "Full-time",
		timezone: "GMT +1",
		city: "San Fransisco, CA",
		tags: [
			{
				label: "MSC",
			},
			{
				label: "Postgraduate",
				bgColor: "bg-[#FEF6F6]",
				textColor: "text-[#F17171]",
				borderColor: "text-[#F17171]",
			},
		],
		isBookmarked: false,
		showTime: false,
	},
	{
		id: "6",
		title: "Advance Data Science",
		courseCompany: "Data Center Academy",
		isCourse: true,
		company: "The course cards include visual indicators for categor...",
		companyLogo: "/course-img2.png",
		workType: "Full-time",
		timezone: "GMT +1",
		city: "San Fransisco, CA",
		tags: [
			{
				label: "MSC",
			},
			{
				label: "Undergraduate",
				bgColor: "bg-[#EBEFFF]",
				textColor: "text-[#335CFF]",
				borderColor: "text-[#335CFF]",
			},
		],
		isBookmarked: false,
		showTime: false,
	},
	{
		id: "8",
		title: "Advance Data Science",
		courseCompany: "Data Center Academy",
		isCourse: true,
		company: "The course cards include visual indicators for categor...",
		companyLogo: "/course-img2.png",
		workType: "Full-time",
		timezone: "GMT +1",
		city: "San Fransisco, CA",
		tags: [
			{
				label: "MSC",
			},
			{
				label: "Undergraduate",
				bgColor: "bg-[#EBEFFF]",
				textColor: "text-[#335CFF]",
				borderColor: "text-[#335CFF]",
			},
		],
		isBookmarked: false,
		showTime: false,
	},
	{
		id: "7",
		title: "Advance Data Science",
		courseCompany: "Data Center Academy",
		isCourse: true,
		company: "The course cards include visual indicators for categor...",
		companyLogo: "/course-img1.png",
		workType: "Full-time",
		timezone: "GMT +1",
		city: "San Fransisco, CA",
		tags: [
			{
				label: "MSC",
			},
			{
				label: "Postgraduate",
				bgColor: "bg-[#FEF6F6]",
				textColor: "text-[#F17171]",
				borderColor: "text-[#F17171]",
			},
		],
		isBookmarked: false,
		showTime: false,
	},
];

const scholarshipsData = [
	{
		id: "1",
		title: "African Leadership Academy Scholarship",
		company: "University of Lagos",
		companyLogo: "/scholarship-img1.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: [
			{
				label: "Undergraduate",
				bgColor: "bg-[#F4EBF8]",
				textColor: "text-[#8F34B4]",
				borderColor: "border-[#F4EBF8]",
			},
		],
		isBookmarked: false,
		showTime: false,
		showBudget: true,
		budget: "Amount: Full Tuition",
		isFulltime: false,
		showDate: true,
		date: "Deadline: Apr 2, 2025",
		isBookmarkIcon: false,
	},
	{
		id: "2",
		title: "African Leadership Academy Scholarship",
		company: "University of Lagos",
		companyLogo: "/scholarship-img1.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: [
			{
				label: "Undergraduate",
				bgColor: "bg-[#F4EBF8]",
				textColor: "text-[#8F34B4]",
				borderColor: "border-[#F4EBF8]",
			},
		],
		isBookmarked: false,
		showTime: false,
		showBudget: true,
		budget: "Amount: Full Tuition",
		isFulltime: false,
		showDate: true,
		date: "Deadline: Apr 2, 2025",
		isBookmarkIcon: false,
	},
	{
		id: "3",
		title: "African Leadership Academy Scholarship",
		company: "University of Lagos",
		companyLogo: "/scholarship-img1.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: [
			{
				label: "Undergraduate",
				bgColor: "bg-[#F4EBF8]",
				textColor: "text-[#8F34B4]",
				borderColor: "border-[#F4EBF8]",
			},
		],
		isBookmarked: false,
		showTime: false,
		showBudget: true,
		budget: "Amount: Full Tuition",
		isFulltime: false,
		showDate: true,
		date: "Deadline: Apr 2, 2025",
		isBookmarkIcon: false,
	},
	{
		id: "4",
		title: "African Leadership Academy Scholarship",
		company: "University of Lagos",
		companyLogo: "/scholarship-img1.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: [
			{
				label: "Undergraduate",
				bgColor: "bg-[#F4EBF8]",
				textColor: "text-[#8F34B4]",
				borderColor: "border-[#F4EBF8]",
			},
		],
		isBookmarked: false,
		showTime: false,
		showBudget: true,
		budget: "Amount: Full Tuition",
		isFulltime: false,
		showDate: true,
		date: "Deadline: Apr 2, 2025",
		isBookmarkIcon: false,
	},
	{
		id: "5",
		title: "African Leadership Academy Scholarship",
		company: "University of Lagos",
		companyLogo: "/scholarship-img1.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: [
			{
				label: "Undergraduate",
				bgColor: "bg-[#F4EBF8]",
				textColor: "text-[#8F34B4]",
				borderColor: "border-[#F4EBF8]",
			},
		],
		isBookmarked: false,
		showTime: false,
		showBudget: true,
		budget: "Amount: Full Tuition",
		isFulltime: false,
		showDate: true,
		date: "Deadline: Apr 2, 2025",
		isBookmarkIcon: false,
	},
	{
		id: "6",
		title: "African Leadership Academy Scholarship",
		company: "University of Lagos",
		companyLogo: "/scholarship-img1.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: [
			{
				label: "Undergraduate",
				bgColor: "bg-[#F4EBF8]",
				textColor: "text-[#8F34B4]",
				borderColor: "border-[#F4EBF8]",
			},
		],
		isBookmarked: false,
		showTime: false,
		showBudget: true,
		budget: "Amount: Full Tuition",
		isFulltime: false,
		showDate: true,
		date: "Deadline: Apr 2, 2025",
		isBookmarkIcon: false,
	},
];

export { courses, scholarshipsData };
