import toast from "react-hot-toast";

export type FeedbackType = "success" | "error" | "info" | "loading";

/**
 * Show a toast feedback message.
 * @param message The message to display
 * @param type The type of feedback ('success', 'error', 'info', 'loading')
 */
export function SendFeedback(message: string, type: FeedbackType = "info") {
	switch (type) {
		case "success":
			toast.success(message);
			break;
		case "error":
			toast.error(message);
			break;
		case "loading":
			toast.loading(message);
			break;
		default:
			toast(message);
	}
}

/**
 * Show a toast for caught errors. Handles Error objects, Axios errors, and strings.
 * @param error The error object or string
 */
export function SendCatchFeedback(error: unknown) {
	let message = "An unexpected error occurred.";
	if (typeof error === "string") {
		message = error;
	} else if (error && typeof error === "object") {
		// Axios error shape
		if (
			"response" in error &&
			error.response &&
			typeof error.response === "object" &&
			"data" in error.response
		) {
			// Try to get message from Axios error response
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			const data = (error as any).response.data;
			if (typeof data === "string") message = data;
			else if (data && typeof data.message === "string") message = data.message;
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
		} else if ("message" in error && typeof (error as any).message === "string") {
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			message = (error as any).message;
		}
	}
	toast.error(message);
}
