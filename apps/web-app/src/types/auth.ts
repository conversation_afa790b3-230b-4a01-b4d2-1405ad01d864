// User types
export interface User {
	id: string;
	email: string;
	firstName?: string;
	lastName?: string;
	avatar?: string;
	role: {
		name: string;
		description: string;
		isActive: boolean;
		isSystem: boolean;
		id: string;
	};
	status: string;
	preferredLanguage: string;
	jobInvitationsNotification: boolean;
	newJobsNotification: boolean;
	newsLetterNotification: boolean;
	scholarshipNotification: boolean;
	applicationStatusNotification: boolean;
	isPublic: boolean;
	organization?: string;
	emailVerified: boolean;
	createdAt: string;
	updatedAt: string;
	// Legacy fields for backward compatibility
	userType?: UserType;
	isEmailVerified?: boolean;
}

export type UserType = "job_seeker" | "recruiter" | "employer" | "facilitator";

// Company/Organization types
export interface Company {
	id: string;
	name: string;
	logo?: string;
	description?: string;
	website?: string;
	industry?: string;
	size?: string;
	location?: string;
	createdAt: string;
	updatedAt: string;
}

// Authentication request/response types
export interface RegisterRequest {
	email: string;
	password: string;
	firstName: string;
	lastName: string;
	organizationName: string;
	role: UserType;
}

export interface LoginRequest {
	email: string;
	password: string;
}

export interface AuthResponse {
	user: User;
	tokens: {
		accessToken: string;
		refreshToken: string;
		expiresIn: number;
	};
	company?: Company;
	// Legacy fields for backward compatibility
	token?: string;
	accessToken?: string;
	refreshToken?: string;
	expiresIn?: number;
}

export interface VerifyEmailRequest {
	email: string;
	verifyCode: string;
}

export interface ResendVerificationRequest {
	email: string;
}

export interface ForgotPasswordRequest {
	email: string;
}

export interface ResetPasswordRequest {
	token: string;
	password: string;
	confirmPassword: string;
}

// Auth state types
export interface AuthState {
	user: User | null;
	company: Company | null;
	token: string | null;
	accessToken: string | null;
	refreshToken: string | null;
	expiresIn: number | null;
	isAuthenticated: boolean;
	isLoading: boolean;
	error: string | null;
	isEmailVerified: boolean;
}

// API Error types
export interface ApiError {
	message: string;
	code?: string;
	field?: string;
	details?: unknown;
}

export interface ApiResponse<T = unknown> {
	success: boolean;
	data?: T;
	message?: string;
	error?: ApiError;
}

// Form data types for signup flows
export interface RecruiterSignupData {
	firstName: string;
	lastName: string;
	organizationName: string;
	email: string;
	password: string;
	confirmPassword: string;
	logo?: string;
}

export interface EmployerSignupData {
	firstName: string;
	lastName: string;
	organizationName: string;
	email: string;
	password: string;
	confirmPassword: string;
	logo?: string;
}

export interface FacilitatorSignupData {
	firstName: string;
	lastName: string;
	organizationName: string;
	email: string;
	password: string;
	confirmPassword: string;
	logo?: string;
}

export interface JobSeekerSignupData {
	firstName: string;
	lastName: string;
	email: string;
	password: string;
	confirmPassword: string;
	avatar?: string;
}
