// Utility functions to test login functionality
import { apiMethods, endpoints } from "@/lib/api";

// Test login with known credentials
export const testLogin = async (email: string, password: string) => {
	console.log("Testing login with credentials:", { email, password: "***" });

	try {
		const response = await apiMethods.post(endpoints.auth.login, {
			email,
			password,
		});
		console.log("Login success:", response.data);
		return { success: true, data: response.data };
	} catch (error: unknown) {
		const axiosError = error as {
			response?: { data?: unknown };
			message?: string;
		};
		console.log(
			"Login failed:",
			axiosError.response?.data || axiosError.message
		);
		return {
			success: false,
			error: axiosError.response?.data || axiosError.message,
		};
	}
};

// Create a test user for login testing
export const createTestUser = async () => {
	const testUser = {
		firstName: "Test",
		lastName: "Recruiter",
		organizationName: "Test Recruitment Company",
		email: `testrecruiter${Date.now()}@maildrop.cc`, // Use a temporary email
		password: "TestPassword123!",
		role: "recruiter",
	};

	try {
		const response = await apiMethods.post(endpoints.auth.register, testUser);
		console.log("Test user registration success:", response.data);
		return {
			success: true,
			email: testUser.email,
			password: testUser.password,
			data: response.data,
		};
	} catch (error: unknown) {
		const axiosError = error as {
			response?: { data?: unknown };
			message?: string;
		};
		console.log(
			"Test user registration failed:",
			axiosError.response?.data || axiosError.message
		);
		return {
			success: false,
			error: axiosError.response?.data || axiosError.message,
		};
	}
};

// Test the complete login flow
export const testCompleteLoginFlow = async () => {
	console.log("Starting complete login flow test...");

	// Step 1: Create a test user
	console.log("Step 1: Creating test user...");
	const registrationResult = await createTestUser();

	if (!registrationResult.success) {
		console.log("Registration failed, cannot proceed with login test");
		return {
			success: false,
			error: "Registration failed",
			details: registrationResult.error,
		};
	}

	console.log("Test user created successfully:", registrationResult.email);

	// Step 2: Attempt login with the test user credentials
	console.log("Step 2: Testing login...");

	if (!registrationResult.email || !registrationResult.password) {
		return {
			success: false,
			error: "Missing email or password from registration result",
		};
	}

	const loginResult = await testLogin(
		registrationResult.email,
		registrationResult.password
	);

	if (loginResult.success) {
		console.log("Complete login flow test successful!");
		return {
			success: true,
			message: "Login flow test completed successfully",
			testEmail: registrationResult.email,
			loginData: loginResult.data,
		};
	} else {
		console.log("Login test failed");
		return {
			success: false,
			error: "Login failed",
			details: loginResult.error,
			testEmail: registrationResult.email,
		};
	}
};
