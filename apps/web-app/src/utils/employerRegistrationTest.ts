// Utility functions to test employer registration functionality
import { apiMethods, endpoints } from "@/lib/api";

// Test employer registration with the correct format
export const testEmployerRegistration = async () => {
	const testEmployer = {
		firstName: "Test",
		lastName: "Employer",
		organizationName: "Test Employer Company",
		email: `testemployer${Date.now()}@maildrop.cc`, // Use a temporary email
		password: "TestPassword123!",
		role: "employer",
	};

	console.log("Testing employer registration with data:", {
		...testEmployer,
		password: "***",
	});

	try {
		const response = await apiMethods.post(endpoints.auth.register, testEmployer);
		console.log("Employer registration success:", response.data);
		return {
			success: true,
			email: testEmployer.email,
			password: testEmployer.password,
			data: response.data,
		};
	} catch (error: unknown) {
		const axiosError = error as {
			response?: { data?: unknown };
			message?: string;
		};
		console.log(
			"Employer registration failed:",
			axiosError.response?.data || axiosError.message
		);
		return {
			success: false,
			error: axiosError.response?.data || axiosError.message,
		};
	}
};

// Test email verification for employer
export const testEmployerEmailVerification = async (
	email: string,
	code: string
) => {
	console.log("Testing employer email verification...");

	try {
		const response = await apiMethods.post(endpoints.auth.verifyEmail, {
			email: email,
			verifyCode: code,
		});
		console.log("Employer verification success:", response.data);
		return { success: true, data: response.data };
	} catch (error: unknown) {
		const axiosError = error as {
			response?: { data?: unknown };
			message?: string;
		};
		console.log(
			"Employer verification failed:",
			axiosError.response?.data || axiosError.message
		);
		return {
			success: false,
			error: axiosError.response?.data || axiosError.message,
		};
	}
};

// Test the complete employer registration flow
export const testCompleteEmployerFlow = async () => {
	console.log("Starting complete employer registration flow test...");

	// Step 1: Register employer
	console.log("Step 1: Registering employer...");
	const registrationResult = await testEmployerRegistration();

	if (!registrationResult.success) {
		console.log("Employer registration failed, cannot proceed");
		return {
			success: false,
			error: "Employer registration failed",
			details: registrationResult.error,
		};
	}

	console.log("Employer registered successfully:", registrationResult.email);

	return {
		success: true,
		message: "Employer registration completed successfully",
		testEmail: registrationResult.email,
		registrationData: registrationResult.data,
		note: "Email verification would be the next step in the actual flow",
	};
};

// Test resend OTP for employer
export const testEmployerResendOtp = async (email: string) => {
	console.log("Testing employer OTP resend...");

	try {
		const response = await apiMethods.post(endpoints.auth.sendOtp, {
			email: email,
		});
		console.log("Employer OTP resend success:", response.data);
		return { success: true, data: response.data };
	} catch (error: unknown) {
		const axiosError = error as {
			response?: { data?: unknown };
			message?: string;
		};
		console.log(
			"Employer OTP resend failed:",
			axiosError.response?.data || axiosError.message
		);
		return {
			success: false,
			error: axiosError.response?.data || axiosError.message,
		};
	}
};
