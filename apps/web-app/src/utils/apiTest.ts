// Utility functions to test API endpoints
import { apiMethods, endpoints } from "@/lib/api";

// Test email verification with the correct format
export const testEmailVerification = async (email: string, code: string) => {
	console.log("Testing email verification with correct format...");

	try {
		console.log("Trying format: { email, verifyCode }");
		const response = await apiMethods.post(endpoints.auth.verifyEmail, {
			email: email,
			verifyCode: code,
		});
		console.log("Verification success:", response.data);
		return response.data;
	} catch (error: unknown) {
		const axiosError = error as {
			response?: { data?: unknown };
			message?: string;
		};
		console.log(
			"Verification failed:",
			axiosError.response?.data || axiosError.message
		);
		throw error;
	}
};

// Test registration to get a user to verify
export const testRegistration = async () => {
	const testUser = {
		firstName: "Test",
		lastName: "User",
		organizationName: "Test Company",
		email: `test${Date.now()}@maildrop.cc`, // Use a temporary email
		password: "Topetope@17",
		role: "recruiter",
	};

	try {
		const response = await apiMethods.post(endpoints.auth.register, testUser);
		console.log("Registration success:", response.data);
		return { success: true, email: testUser.email, data: response.data };
	} catch (error: unknown) {
		const axiosError = error as {
			response?: { data?: unknown };
			message?: string;
		};
		console.log(
			"Registration failed:",
			axiosError.response?.data || axiosError.message
		);
		return {
			success: false,
			error: axiosError.response?.data || axiosError.message,
		};
	}
};
