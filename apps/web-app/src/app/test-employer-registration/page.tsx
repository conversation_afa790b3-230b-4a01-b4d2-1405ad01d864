"use client";

import { useState } from "react";
import {
	testCompleteEmployerFlow,
	testEmployerRegistration,
	testEmployerEmailVerification,
	testEmployerResendOtp,
} from "@/utils/employerRegistrationTest";
import Button from "@ui/ui/Button";

export default function TestEmployerRegistrationPage() {
	const [email, setEmail] = useState("");
	const [code, setCode] = useState("");
	const [results, setResults] = useState<string[]>([]);
	const [isLoading, setIsLoading] = useState(false);

	const addResult = (message: string) => {
		setResults((prev) => [
			...prev,
			`${new Date().toLocaleTimeString()}: ${message}`,
		]);
	};

	const testCompleteFlow = async () => {
		setIsLoading(true);
		addResult("🚀 Starting complete employer registration flow test...");

		try {
			const result = await testCompleteEmployerFlow();
			if (result.success) {
				addResult(`✅ Complete flow test successful!`);
				addResult(`Test email: ${result.testEmail}`);
				addResult(`Registration data: ${JSON.stringify(result.registrationData, null, 2)}`);
				addResult(`Note: ${result.note}`);
				
				// Set the email for manual verification testing
				if (result.testEmail) {
					setEmail(result.testEmail);
				}
			} else {
				addResult(`❌ Complete flow test failed: ${JSON.stringify(result.error, null, 2)}`);
				if (result.details) {
					addResult(`Details: ${JSON.stringify(result.details, null, 2)}`);
				}
			}
		} catch (error) {
			addResult(`❌ Complete flow test error: ${error}`);
		}

		setIsLoading(false);
	};

	const testRegistrationOnly = async () => {
		setIsLoading(true);
		addResult("📝 Testing employer registration only...");

		try {
			const result = await testEmployerRegistration();
			if (result.success) {
				addResult(`✅ Registration successful!`);
				addResult(`Test email: ${result.email}`);
				addResult(`Registration data: ${JSON.stringify(result.data, null, 2)}`);
				
				// Set the email for manual verification testing
				setEmail(result.email);
			} else {
				addResult(`❌ Registration failed: ${JSON.stringify(result.error, null, 2)}`);
			}
		} catch (error) {
			addResult(`❌ Registration error: ${error}`);
		}

		setIsLoading(false);
	};

	const testEmailVerification = async () => {
		if (!email || !code) {
			addResult("❌ Please provide both email and verification code");
			return;
		}

		setIsLoading(true);
		addResult(`📧 Testing email verification for ${email}...`);

		try {
			const result = await testEmployerEmailVerification(email, code);
			if (result.success) {
				addResult(`✅ Email verification successful!`);
				addResult(`Verification data: ${JSON.stringify(result.data, null, 2)}`);
			} else {
				addResult(`❌ Email verification failed: ${JSON.stringify(result.error, null, 2)}`);
			}
		} catch (error) {
			addResult(`❌ Email verification error: ${error}`);
		}

		setIsLoading(false);
	};

	const testResendOtp = async () => {
		if (!email) {
			addResult("❌ Please provide email address");
			return;
		}

		setIsLoading(true);
		addResult(`🔄 Testing OTP resend for ${email}...`);

		try {
			const result = await testEmployerResendOtp(email);
			if (result.success) {
				addResult(`✅ OTP resend successful!`);
				addResult(`OTP data: ${JSON.stringify(result.data, null, 2)}`);
			} else {
				addResult(`❌ OTP resend failed: ${JSON.stringify(result.error, null, 2)}`);
			}
		} catch (error) {
			addResult(`❌ OTP resend error: ${error}`);
		}

		setIsLoading(false);
	};

	const clearResults = () => {
		setResults([]);
	};

	return (
		<div className="max-w-4xl mx-auto p-6">
			<h1 className="text-3xl font-bold mb-6">Employer Registration Test</h1>
			
			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				{/* Test Controls */}
				<div className="space-y-4">
					<h2 className="text-xl font-semibold">Test Controls</h2>
					
					<div className="space-y-3">
						<Button
							onClick={testCompleteFlow}
							disabled={isLoading}
							variant="primary"
							className="w-full">
							{isLoading ? "Testing..." : "Test Complete Flow"}
						</Button>
						
						<Button
							onClick={testRegistrationOnly}
							disabled={isLoading}
							variant="secondary"
							className="w-full">
							{isLoading ? "Testing..." : "Test Registration Only"}
						</Button>
					</div>

					<div className="space-y-3">
						<h3 className="text-lg font-medium">Manual Verification</h3>
						<input
							type="email"
							placeholder="Email address"
							value={email}
							onChange={(e) => setEmail(e.target.value)}
							className="w-full px-3 py-2 border border-gray-300 rounded-md"
						/>
						<input
							type="text"
							placeholder="Verification code"
							value={code}
							onChange={(e) => setCode(e.target.value)}
							className="w-full px-3 py-2 border border-gray-300 rounded-md"
						/>
						<div className="flex gap-2">
							<Button
								onClick={testEmailVerification}
								disabled={isLoading}
								variant="secondary"
								className="flex-1">
								Verify Email
							</Button>
							<Button
								onClick={testResendOtp}
								disabled={isLoading}
								variant="outline"
								className="flex-1">
								Resend OTP
							</Button>
						</div>
					</div>

					<Button
						onClick={clearResults}
						variant="outline"
						className="w-full">
						Clear Results
					</Button>
				</div>

				{/* Results */}
				<div className="space-y-4">
					<h2 className="text-xl font-semibold">Test Results</h2>
					<div className="bg-gray-100 p-4 rounded-lg h-96 overflow-y-auto">
						{results.length === 0 ? (
							<p className="text-gray-500">No test results yet. Run a test to see results.</p>
						) : (
							<div className="space-y-2">
								{results.map((result, index) => (
									<div key={index} className="text-sm font-mono whitespace-pre-wrap">
										{result}
									</div>
								))}
							</div>
						)}
					</div>
				</div>
			</div>

			<div className="mt-8 p-4 bg-blue-50 rounded-lg">
				<h3 className="text-lg font-semibold mb-2">Instructions</h3>
				<ol className="list-decimal list-inside space-y-1 text-sm">
					<li>Click "Test Complete Flow" to test the full employer registration process</li>
					<li>Or click "Test Registration Only" to test just the registration step</li>
					<li>If registration succeeds, check your email (or logs) for the verification code</li>
					<li>Enter the verification code and click "Verify Email" to complete the process</li>
					<li>Use "Resend OTP" if you need a new verification code</li>
				</ol>
			</div>
		</div>
	);
}
