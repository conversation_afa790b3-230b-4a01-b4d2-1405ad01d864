"use client";

import EmployerLoginForm from "@/components/auth/EmployerLoginForm";
import FacilitatorLoginForm from "@/components/auth/FacilitatorLoginForm";
import JobSeekerLoginForm from "@/components/auth/JobSeekerLoginForm";
import RecruiterLoginForm from "@/components/auth/RecruiterLoginForm";
import { useParams } from "next/navigation";

export default function UserTypeLoginPage() {
	const params = useParams();
	const userType = params.userType;

	switch (userType) {
		case "recruiter":
			return <RecruiterLoginForm />;
		case "employer":
			return <EmployerLoginForm />;
		case "facilitator":
			return <FacilitatorLoginForm />;
		case "job-seeker":
			return <JobSeekerLoginForm />;
		default:
			// Fallback to recruiter login for unknown user types
			return <RecruiterLoginForm />;
	}
}
