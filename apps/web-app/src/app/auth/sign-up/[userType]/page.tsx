"use client";
import EmployerSignUpForm from "@/components/sign-up/EmployerSignUpForm";
import FacilitatorSignUpForm from "@/components/sign-up/FacilitatorSignUpForm";
import JobSeekerSignUpForm from "@/components/sign-up/JobSeekerSignUpForm";
import RecruiterSignUpForm from "@/components/sign-up/RecruiterSignUpForm";
import { useParams } from "next/navigation";

export default function UserTypeSignUpPage() {
	const params = useParams();
	const userType = params.userType;

	if (userType === "recruiter") {
		return <RecruiterSignUpForm />;
	}
	if (userType === "employer") {
		return <EmployerSignUpForm />;
	}
	if (userType === "facilitator") {
		return <FacilitatorSignUpForm />;
	}
	if (userType === "job-seeker") {
		return <JobSeekerSignUpForm />;
	}

	return (
		<div>
			<h1>Sign Up - {userType?.toString().toUpperCase()}</h1>
			<p>This is the sign-up page for {userType}.</p>
		</div>
	);
}
