@import url("https://cdn.jsdelivr.net/gh/orioncactus/pretendard@v1.3.9/dist/web/static/pretendard.min.css");
@import "tailwindcss";



@theme {
	/* Brand Colors - Your specific brand blue #335CFF */
	--color-brand-50: #eff6ff;
	--color-brand-100: #dbeafe;
	--color-brand-200: #bfdbfe;
	--color-brand-300: #93c5fd;
	--color-brand-400: #60a5fa;
	--color-brand-500: #335cff;
	--color-brand-600: #2347e6;
	--color-brand-700: #1d4ed8;
	--color-brand-800: #1e40af;
	--color-brand-900: #1e3a8a;
	--color-brand-950: #172554;

	/* Primary Colors - Standard blues */
	--color-primary-50: #eff6ff;
	--color-primary-100: #dbeafe;
	--color-primary-200: #bfdbfe;
	--color-primary-300: #93c5fd;
	--color-primary-300-light: #7692ff;
	--color-primary-400: #60a5fa;
	--color-primary-500-default: #3b82f6;
	--color-primary-500-light: #335cff;
	--color-primary-600: #2563eb;
	--color-primary-700: #1d4ed8;
	--color-primary-800: #1e40af;
	--color-primary-900: #1e3a8a;
	--color-primary-950: #172554;

	/* Secondary Colors */
	--color-secondary-50: #f0f9ff;
	--color-secondary-100: #e0f2fe;
	--color-secondary-200: #bae6fd;
	--color-secondary-300: #7dd3fc;
	--color-secondary-400: #38bdf8;
	--color-secondary-500: #0ea5e9;
	--color-secondary-600: #0284c7;
	--color-secondary-700: #0369a1;
	--color-secondary-800: #075985;
	--color-secondary-900: #0c4a6e;
	--color-secondary-950: #082f49;

	/* Accent Colors */
	--color-accent-orange-50: #fff7ed;
	--color-accent-orange-100: #ffedd5;
	--color-accent-orange-200: #fed7aa;
	--color-accent-orange-300: #fdba74;
	--color-accent-orange-400: #fb923c;
	--color-accent-orange-500: #f59e0b;
	--color-accent-orange-600: #ea580c;
	--color-accent-orange-700: #c2410c;
	--color-accent-orange-800: #9a3412;
	--color-accent-orange-900: #7c2d12;

	--color-accent-green-50: #ecfdf5;
	--color-accent-green-100: #d1fae5;
	--color-accent-green-200: #a7f3d0;
	--color-accent-green-300: #6ee7b7;
	--color-accent-green-400: #34d399;
	--color-accent-green-500: #10b981;
	--color-accent-green-600: #059669;
	--color-accent-green-700: #047857;
	--color-accent-green-800: #065f46;
	--color-accent-green-900: #064e3b;

	/* Neutral Grays */
	--color-brandGray-50: #f9fafb;
	--color-brandGray-100: #f3f4f6;
	--color-brandGray-200: #e4e4e7;
	--color-brandGray-300: #a1a1aa;
	--color-brandGray-400: #71717a;
	--color-brandGray-500: #52525b;
	--color-brandGray-600: #27272a;
	--color-brandGray-700: #3f3f46;
	--color-brandGray-800: #27272a;
	--color-brandGray-900: #111827;
	--color-brandGray-950: #030712;

	/* Semantic Colors */
	--color-success-50: #f0fdf4;
	--color-success-100: #dcfce7;
	--color-success-200: #bbf7d0;
	--color-success-300: #86efac;
	--color-success-400: #4ade80;
	--color-success-500: #6fb680;
	--color-success-600: #16a34a;
	--color-success-700: #15803d;
	--color-success-800: #166534;
	--color-success-900: #14532d;

	--color-warning-50: #fffbeb;
	--color-warning-100: #fef3c7;
	--color-warning-200: #fde68a;
	--color-warning-300: #fcd34d;
	--color-warning-400: #fbbf24;
	--color-warning-500: #f59e0b;
	--color-warning-600: #d97706;
	--color-warning-700: #b45309;
	--color-warning-800: #92400e;
	--color-warning-900: #78350f;

	--color-error-50: #fef2f2;
	--color-error-100: #fee2e2;
	--color-error-200: #fecaca;
	--color-error-300: #fca5a5;
	--color-error-400: #f87171;
	--color-error-500: #ef4444;
	--color-error-600: #dc2626;
	--color-error-700: #b91c1c;
	--color-error-800: #991b1b;
	--color-error-900: #7f1d1d;

	/* Background & Surface */
	--color-background: #ffffff;
	--color-surface-50: #ffffff;
	--color-surface-100: #f9fafb;
	--color-surface-200: #f3f4f6;
	--color-surface-300: #e5e7eb;

	/* Typography */
	--font-sans: "Pretendard", -apple-system, BlinkMacSystemFont, system-ui,
		sans-serif;
	--font-mono: ui-monospace, SFMono-Regular, Monaco, Consolas, monospace;

	/* Font Sizes with Line Heights */
	--font-size-hero: 72px;
	--line-height-hero: 110%;
	--letter-spacing-hero: -1.5px;

	--font-size-6xl: 48px;
	--line-height-6xl: 120%;
	--letter-spacing-6xl: -1px;

	--font-size-5xl: 40px;
	--line-height-5xl: 120%;
	--letter-spacing-5xl: -0.8px;

	--font-size-4xl: 32px;
	--line-height-4xl: 125%;
	--letter-spacing-4xl: -0.5px;

	--font-size-3xl: 28px;
	--line-height-3xl: 130%;
	--letter-spacing-3xl: -0.3px;

	--font-size-2xl: 24px;
	--line-height-2xl: 130%;

	--font-size-xl: 20px;
	--line-height-xl: 135%;

	--font-size-lg: 18px;
	--line-height-lg: 140%;

	--font-size-base: 16px;
	--line-height-base: 150%;

	--font-size-sm: 14px;
	--line-height-sm: 150%;

	--font-size-xs: 12px;
	--line-height-xs: 150%;

	/* Border Radius */
	--radius-sm: 4px;
	--radius-default: 6px;
	--radius-md: 8px;
	--radius-lg: 10px;
	--radius-xl: 12px;
	--radius-2xl: 16px;
	--radius-3xl: 20px;
	--radius-4xl: 24px;

	/* Shadows */
	--shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
	--shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
	--shadow-default: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
		0 2px 4px -2px rgba(0, 0, 0, 0.1);
	--shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
		0 4px 6px -4px rgba(0, 0, 0, 0.1);
	--shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
		0 8px 10px -6px rgba(0, 0, 0, 0.1);
	--shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
	--shadow-card: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
		0 2px 4px -2px rgba(0, 0, 0, 0.1);
	--shadow-card-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
		0 4px 6px -4px rgba(0, 0, 0, 0.1);

	/* Container Sizes */
	--max-width-container: 1440px;
	--max-width-content: 1200px;
	--max-width-prose: 768px;

	/* Z-Index Scale */
	--z-dropdown: 1000;
	--z-sticky: 1020;
	--z-fixed: 1030;
	--z-modal-backdrop: 1040;
	--z-modal: 1050;
	--z-popover: 1060;
	--z-tooltip: 1070;
	--z-toast: 1080;

	/* Animation Variables */
	--logo-scroll-duration: 20s;
}

@media (prefers-color-scheme: dark) {
	:root {
		--background: #0a0a0a;
		--foreground: #ededed;
	}
}

body {
	background: var(--background);
	color: var(--foreground);
	font-family: "Pretendard", -apple-system, BlinkMacSystemFont, system-ui,
		Roboto, "Helvetica Neue", "Segoe UI", sans-serif;
	font-feature-settings: "cv01", "cv02", "cv03", "cv04";
}

/* Typography styles using design system variables */
.heading-hero {
	font-family: var(--font-sans);
	font-weight: 500;
	font-size: var(--font-size-hero);
	line-height: var(--line-height-hero);
	letter-spacing: var(--letter-spacing-hero);
}

.heading-6xl {
	font-family: var(--font-sans);
	font-weight: 600;
	font-size: var(--font-size-6xl);
	line-height: var(--line-height-6xl);
	letter-spacing: var(--letter-spacing-6xl);
}

.heading-5xl {
	font-family: var(--font-sans);
	font-weight: 600;
	font-size: var(--font-size-5xl);
	line-height: var(--line-height-5xl);
	letter-spacing: var(--letter-spacing-5xl);
}

.heading-4xl {
	font-family: var(--font-sans);
	font-weight: 600;
	font-size: var(--font-size-4xl);
	line-height: var(--line-height-4xl);
	letter-spacing: var(--letter-spacing-4xl);
}

.heading-3xl {
	font-family: var(--font-sans);
	font-weight: 600;
	font-size: var(--font-size-3xl);
	line-height: var(--line-height-3xl);
	letter-spacing: var(--letter-spacing-3xl);
}

.heading-2xl {
	font-family: var(--font-sans);
	font-weight: 600;
	font-size: var(--font-size-2xl);
	line-height: var(--line-height-2xl);
}

.heading-xl {
	font-family: var(--font-sans);
	font-weight: 600;
	font-size: var(--font-size-xl);
	line-height: var(--line-height-xl);
}

.heading-lg {
	font-family: var(--font-sans);
	font-weight: 600;
	font-size: var(--font-size-lg);
	line-height: var(--line-height-lg);
}

/* Component styles using design system variables */
.btn-primary {
	background: var(--color-brand-500);
	color: white;
	font-weight: 500;
	padding: 6px 18.5px;
	border-radius: var(--radius-lg);
	transition: all 0.2s ease;
	border: 1px solid var(--color-brand-500);
	cursor: pointer;
	font-size: var(--font-size-sm);
}

.btn-primary:hover {
	background: var(--color-brand-600);
	border-color: var(--color-brand-600);
}

.btn-secondary {
	background: white;
	color: var(--color-brandGray-900);
	font-weight: 500;
	padding: 12px 24px;
	border-radius: var(--radius-md);
	border: 1px solid var(--color-brandGray-300);
	transition: all 0.2s ease;
	cursor: pointer;
}

.btn-secondary:hover {
	background: var(--color-brandGray-50);
	border-color: var(--color-brandGray-400);
}

/* Logo Animation Keyframes */
@keyframes fadeScroll {
	0% {
		transform: translateX(0);
	}
	100% {
		transform: translateX(-100%);
	}
}

@keyframes infiniteScroll {
	0% {
		transform: translateX(0);
	}
	100% {
		transform: translateX(calc(-100% / 2));
	}
}

@keyframes pulseGlow {
	0%,
	100% {
		opacity: 0.7;
		filter: brightness(1);
	}
	50% {
		opacity: 1;
		filter: brightness(1.1);
	}
}

/* Animation Classes */
.animate-fade-scroll {
	animation: fadeScroll var(--logo-scroll-duration) linear infinite;
}

.animate-pulse-glow {
	animation: pulseGlow 3s ease-in-out infinite;
}

.animate-infinite-scroll {
	animation: infiniteScroll var(--logo-scroll-duration) linear infinite;
}

.btn-outline {
	background: transparent;
	color: var(--color-brand-500);
	font-weight: 500;
	padding: 12px 24px;
	border-radius: var(--radius-md);
	border: 1px solid var(--color-brand-500);
	transition: all 0.2s ease;
	cursor: pointer;
}

.btn-outline:hover {
	background: var(--color-brand-500);
	color: white;
}

.card {
	background: white;
	border-radius: var(--radius-xl);
	box-shadow: var(--shadow-card);
	border: 1px solid var(--color-brandGray-200);
	padding: 24px;
	transition: all 0.2s ease;
}

.card:hover {
	box-shadow: var(--shadow-card-hover);
}

.input-field {
	width: 100%;
	padding: 12px 16px;
	border: 1px solid var(--color-brandGray-300);
	border-radius: var(--radius-md);
	font-family: var(--font-sans);
	font-size: var(--font-size-base);
	transition: all 0.2s ease;
}

.input-field:focus {
	outline: none;
	border-color: var(--color-brand-500);
	box-shadow: 0 0 0 3px rgba(51, 92, 255, 0.1);
}

.container-custom {
	max-width: var(--max-width-container);
	padding: 38px 8px 8px;
	margin: 0 auto;
}

/* Utility classes for consistent spacing */
.section-padding {
	padding: 80px 0;
}

.section-padding-sm {
	padding: 60px 0;
}

.section-padding-lg {
	padding: 120px 0;
}

/* Text utilities */
.text-balance {
	text-wrap: balance;
}

.text-pretty {
	text-wrap: pretty;
}

/* Scrolling text animation */
@keyframes scroll-text {
	0% {
		transform: translateX(0);
	}
	100% {
		transform: translateX(-50%);
	}
}

.animate-scroll-text {
	animation: scroll-text 20s linear infinite;
}

/* Twinkling stars animation */
@keyframes twinkle {
	0%,
	100% {
		opacity: 1;
		transform: scale(1);
	}
	25% {
		opacity: 0.3;
		transform: scale(0.8);
	}
	50% {
		opacity: 1;
		transform: scale(1.2);
	}
	75% {
		opacity: 0.6;
		transform: scale(0.9);
	}
}

.animate-twinkle {
	animation: twinkle 2s ease-in-out infinite;
}

.animate-twinkle:nth-child(2) {
	animation-delay: 0.5s;
}

.animate-twinkle:nth-child(4) {
	animation-delay: 1s;
}

.animate-twinkle:nth-child(6) {
	animation-delay: 1.5s;
}
