"use client";
import {
	DashboardStatCard,
	JobPostCard,
} from "../../../components/ui/DashboardStatCard";
import { ProfileIcon1, SuitcaseIcon1 } from "@/components/common/icons";
import { Button } from "@ui/ui";
import { LabelInput } from "@/components/ui";
import React, { useState } from "react";
import Image from "next/image";

const dashboardStats = [
	{ title: "Total Jobs", value: 0, subtext: "0% This week" },
	{ title: "Jobs Posted", value: 0, subtext: "0% This month" },
	{ title: "Total Applicants", value: 0, subtext: "0% Across all jobs" },
	{ title: "Shortlisted", value: 0, subtext: "0% of applicants" },
];
const jobPostCardStats = [
	{
		title: "Applicant Pipeline",
		icon: (
			<svg
				width="20"
				height="20"
				viewBox="0 0 20 20"
				fill="none"
				xmlns="http://www.w3.org/2000/svg">
				<path
					d="M19.1252 11.7498C19.0596 11.7991 18.9849 11.8349 18.9053 11.8553C18.8258 11.8756 18.7431 11.8801 18.6618 11.8685C18.5806 11.8569 18.5024 11.8294 18.4318 11.7876C18.3612 11.7458 18.2995 11.6905 18.2502 11.6248C17.8735 11.1185 17.3832 10.7076 16.8187 10.4254C16.2542 10.1431 15.6313 9.99737 15.0002 9.99981C14.8773 9.9998 14.7572 9.96356 14.6548 9.89561C14.5523 9.82766 14.4722 9.73102 14.4245 9.61778C14.392 9.54089 14.3753 9.45827 14.3753 9.37481C14.3753 9.29135 14.392 9.20874 14.4245 9.13185C14.4722 9.01861 14.5523 8.92197 14.6548 8.85402C14.7572 8.78607 14.8773 8.74983 15.0002 8.74981C15.3509 8.74978 15.6946 8.65141 15.9921 8.46586C16.2897 8.28032 16.5293 8.01504 16.6837 7.70016C16.838 7.38528 16.901 7.03342 16.8654 6.68455C16.8298 6.33568 16.6971 6.00378 16.4823 5.72655C16.2676 5.44932 15.9794 5.23788 15.6505 5.11623C15.3216 4.99459 14.9651 4.96762 14.6217 5.03839C14.2782 5.10917 13.9615 5.27484 13.7074 5.5166C13.4534 5.75836 13.2723 6.06652 13.1846 6.40606C13.1641 6.48558 13.1281 6.56027 13.0787 6.62587C13.0293 6.69148 12.9675 6.74672 12.8968 6.78843C12.8261 6.83014 12.7478 6.85752 12.6665 6.86899C12.5852 6.88046 12.5024 6.8758 12.4229 6.85528C12.3434 6.83476 12.2687 6.79878 12.2031 6.7494C12.1375 6.70001 12.0822 6.63819 12.0405 6.56746C11.9988 6.49673 11.9714 6.41847 11.96 6.33716C11.9485 6.25585 11.9532 6.17308 11.9737 6.09356C12.0954 5.62272 12.3253 5.18677 12.6451 4.8204C12.9649 4.45404 13.3658 4.16737 13.8159 3.98321C14.266 3.79905 14.7529 3.72249 15.2378 3.7596C15.7227 3.79672 16.1922 3.94649 16.609 4.19701C17.0259 4.44753 17.3785 4.79187 17.6388 5.20264C17.8991 5.61341 18.06 6.07927 18.1086 6.56316C18.1572 7.04704 18.0922 7.53559 17.9188 7.98993C17.7454 8.44427 17.4683 8.85187 17.1096 9.18028C17.9595 9.54825 18.6982 10.1322 19.2526 10.874C19.3018 10.9399 19.3376 11.0148 19.3579 11.0944C19.3781 11.1741 19.3824 11.257 19.3706 11.3383C19.3588 11.4197 19.331 11.4979 19.2889 11.5685C19.2468 11.6391 19.1912 11.7007 19.1252 11.7498ZM14.9159 16.5623C14.9611 16.6334 14.9914 16.713 15.0051 16.7962C15.0188 16.8793 15.0155 16.9644 14.9955 17.0463C14.9754 17.1282 14.939 17.2051 14.8884 17.2726C14.8379 17.34 14.7742 17.3965 14.7012 17.4387C14.6282 17.4809 14.5475 17.5079 14.4638 17.5181C14.3801 17.5282 14.2952 17.5213 14.2143 17.4979C14.1333 17.4744 14.058 17.4348 13.9927 17.3814C13.9275 17.328 13.8737 17.262 13.8346 17.1873C13.4409 16.5207 12.8802 15.9682 12.2078 15.5843C11.5353 15.2005 10.7745 14.9987 10.0002 14.9987C9.226 14.9987 8.46513 15.2005 7.79272 15.5843C7.1203 15.9682 6.55958 16.5207 6.16586 17.1873C6.12679 17.262 6.07301 17.328 6.00777 17.3814C5.94252 17.4348 5.86715 17.4744 5.7862 17.4979C5.70524 17.5213 5.62037 17.5282 5.5367 17.5181C5.45302 17.5079 5.37226 17.4809 5.29928 17.4387C5.2263 17.3965 5.16261 17.34 5.11204 17.2726C5.06147 17.2051 5.02506 17.1282 5.00501 17.0463C4.98495 16.9644 4.98167 16.8793 4.99535 16.7962C5.00903 16.713 5.03939 16.6334 5.08461 16.5623C5.69055 15.5212 6.61445 14.7019 7.72055 14.2248C7.09815 13.7483 6.64073 13.0887 6.41258 12.3387C6.18443 11.5888 6.19703 10.7862 6.44861 10.0438C6.70018 9.30139 7.17808 8.65648 7.81514 8.19972C8.45219 7.74296 9.21636 7.49731 10.0002 7.49731C10.7841 7.49731 11.5483 7.74296 12.1853 8.19972C12.8224 8.65648 13.3003 9.30139 13.5519 10.0438C13.8034 10.7862 13.816 11.5888 13.5879 12.3387C13.3597 13.0887 12.9023 13.7483 12.2799 14.2248C13.386 14.7019 14.3099 15.5212 14.9159 16.5623ZM10.0002 13.7498C10.4947 13.7498 10.978 13.6032 11.3892 13.3285C11.8003 13.0538 12.1207 12.6633 12.3099 12.2065C12.4992 11.7497 12.5487 11.247 12.4522 10.7621C12.3557 10.2771 12.1176 9.83168 11.768 9.48205C11.4184 9.13242 10.9729 8.89431 10.488 8.79785C10.003 8.70139 9.50034 8.7509 9.04353 8.94011C8.58671 9.12933 8.19627 9.44977 7.92156 9.86089C7.64686 10.272 7.50024 10.7554 7.50024 11.2498C7.50024 11.9129 7.76363 12.5487 8.23247 13.0176C8.70131 13.4864 9.3372 13.7498 10.0002 13.7498ZM5.62524 9.37481C5.62524 9.20905 5.55939 9.05008 5.44218 8.93287C5.32497 8.81566 5.166 8.74981 5.00024 8.74981C4.64956 8.74978 4.30591 8.65141 4.00834 8.46586C3.71076 8.28032 3.47119 8.01504 3.31682 7.70016C3.16246 7.38528 3.09949 7.03342 3.13508 6.68455C3.17066 6.33568 3.30337 6.00378 3.51813 5.72655C3.73289 5.44932 4.02109 5.23788 4.35 5.11623C4.67891 4.99459 5.03534 4.96762 5.3788 5.03839C5.72227 5.10917 6.039 5.27484 6.29303 5.5166C6.54706 5.75836 6.72819 6.06652 6.81586 6.40606C6.8573 6.56664 6.96083 6.70419 7.10369 6.78843C7.24654 6.87268 7.417 6.89672 7.57758 6.85528C7.73816 6.81384 7.8757 6.71031 7.95995 6.56746C8.04419 6.42461 8.06824 6.25414 8.0268 6.09356C7.90512 5.62272 7.67522 5.18677 7.3554 4.8204C7.03559 4.45404 6.63468 4.16737 6.18458 3.98321C5.73447 3.79905 5.2476 3.72249 4.7627 3.7596C4.2778 3.79672 3.80826 3.94649 3.39143 4.19701C2.9746 4.44753 2.62199 4.79187 2.36166 5.20264C2.10133 5.61341 1.94046 6.07927 1.89185 6.56316C1.84325 7.04704 1.90825 7.53559 2.08168 7.98993C2.25511 8.44427 2.53219 8.85187 2.89086 9.18028C2.04186 9.5486 1.30392 10.1325 0.750237 10.874C0.650677 11.0066 0.607874 11.1734 0.631242 11.3375C0.654611 11.5017 0.742238 11.6499 0.874846 11.7494C1.00745 11.849 1.17418 11.8918 1.33835 11.8684C1.50252 11.845 1.65068 11.7574 1.75024 11.6248C2.12695 11.1185 2.61728 10.7076 3.18177 10.4254C3.74625 10.1431 4.36913 9.99737 5.00024 9.99981C5.166 9.99981 5.32497 9.93397 5.44218 9.81676C5.55939 9.69955 5.62524 9.54057 5.62524 9.37481Z"
					fill="#71717A"
				/>
			</svg>
		),
	},
	{
		title: "Job Posted Overtime",
		icon: <SuitcaseIcon1 color="#71717A" className="w-5 h-5" />,
	},
	{
		title: "Applicants Overtime",
		icon: <ProfileIcon1 color="#71717A" className="w-5 h-5" />,
	},
];

export default function DashboardPage() {
	const [activeTab, setActiveTab] = useState("All");
	const tabs = ["All", "Active", "Drafts", "Outdated"];

	return (
		<main className="bg-white h-screen">
			<div className="flex gap-6 flex-wrap bg-white px-8 py-5 border-b border-[#E4E4E7]">
				{dashboardStats.map((stat) => (
					<DashboardStatCard
						key={stat.title}
						title={stat.title}
						value={stat.value}
						subtext={stat.subtext}
					/>
				))}
			</div>
			<div className="flex gap-4 flex-wrap bg-white px-8 py-5 border-b border-[#E4E4E7]">
				{jobPostCardStats.map((job) => (
					<JobPostCard key={job.title} title={job.title} icon={job.icon} />
				))}
			</div>
			<div className="flex gap-4 flex-wrap bg-white px-8 py-5 border-b border-[#E4E4E7]">
				<div className="w-full flex items-center justify-between">
					<div>
						<p className=" font-medium text-sm text-[#18181B] w-fit ">
							Job Post History
						</p>
						<p className=" font-normal text-xs text-[#71717A] w-fit  ">
							View and manage your job postings.
						</p>
					</div>

					<Button variant="outline" size="xs">
						View all
					</Button>
				</div>

				{/* tabs */}

				<div className="py-5 w-full border-y border-[#E4E4E7] flex items-center justify-between">
					<div className="p-1 rounded-[8px] bg-[#F4F4F5] flex items-center gap-1 w-fit">
						{tabs.map((tab) => (
							<button
								key={tab}
								onClick={() => setActiveTab(tab)}
								style={
									activeTab === tab
										? {
												boxShadow:
													"0px 2px 4px 0px #0E121B08, 0px 6px 10px 0px #0E121B0F",
										  }
										: undefined
								}
								className={`rounded-[6px] p-1 w-[132px] text-sm leading-[22px] cursor-pointer font-medium transition-colors duration-150 ${
									activeTab === tab
										? "bg-[#FFFFFF] text-[#27272A]"
										: "bg-transparent text-[#A1A1AA]"
								}`}>
								{tab}
							</button>
						))}
					</div>
					<LabelInput inputType="search" placeholder="Search for Jobs..." />
				</div>

				<div className="rounded-[16px] h-[402px] w-[308px] border border-[#E4E4E7] m-auto relative py-[46px] px-[36px]">
					<Image
						src="/clock-image.png"
						width={236}
						height={164}
						priority
						alt="empty state job"
						className="object-contain"
					/>
				</div>
			</div>
		</main>
	);
}
