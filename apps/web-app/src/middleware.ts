import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Get auth tokens from cookies or headers
  const authToken = request.cookies.get('authToken')?.value || 
                   request.cookies.get('accessToken')?.value;
  
  // Check if user data exists in localStorage (we'll need to handle this differently)
  // Since middleware runs on server, we can't access localStorage directly
  // We'll check for the presence of auth tokens as a proxy for authentication
  
  const isAuthenticated = !!authToken;
  
  // Define protected and public routes
  const authRoutes = [
    '/auth/login',
    '/auth/login/recruiter', 
    '/auth/login/employer',
    '/auth/login/facilitator',
    '/auth/login/job-seeker',
    '/auth/sign-up'
  ];
  
  const protectedRoutes = [
    '/dashboard'
  ];
  
  // If user is authenticated and trying to access auth routes, redirect to dashboard
  if (isAuthenticated && authRoutes.some(route => pathname.startsWith(route))) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }
  
  // If user is not authenticated and trying to access protected routes, redirect to home
  if (!isAuthenticated && protectedRoutes.some(route => pathname.startsWith(route))) {
    return NextResponse.redirect(new URL('/', request.url));
  }
  
  // Allow the request to continue
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.png$|.*\\.jpg$|.*\\.jpeg$|.*\\.gif$|.*\\.svg$).*)',
  ],
};
