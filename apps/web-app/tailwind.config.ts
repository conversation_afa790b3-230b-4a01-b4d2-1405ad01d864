import type { Config } from "tailwindcss";

const config: Config = {
	content: [
		"./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
		"./src/components/**/*.{js,ts,jsx,tsx,mdx}",
		"./src/app/**/*.{js,ts,jsx,tsx,mdx}",
	],
	// theme: {
	// 	extend: {
	// 		// Colors from Figma Design System - Exact matches
	// 		colors: {
	// 			// Primary Brand Colors (Blues from Figma)
	// 			primary: {
	// 				50: "#eff6ff",
	// 				100: "#dbeafe",
	// 				200: "#bfdbfe",
	// 				300: {
	// 					DEFAULT: "#93c5fd",
	// 					light: "#7692FF",
	// 				},
	// 				400: "#60a5fa",
	// 				500: {
	// 					DEFAULT: "#3b82f6",
	// 					light: "#335CFF",
	// 				}, // Standard blue from Figma
	// 				600: "#2563eb",
	// 				700: "#1d4ed8",
	// 				800: "#1e40af",
	// 				900: "#1e3a8a",
	// 				950: "#172554",
	// 			},

	// 			// Brand Specific Blue (Your main brand color)
	// 			brand: {
	// 				50: "#eff6ff",
	// 				100: "#dbeafe",
	// 				200: "#bfdbfe",
	// 				300: "#93c5fd",
	// 				400: "#60a5fa",
	// 				500: "#335CFF", // Your specific brand blue
	// 				600: "#2347E6",
	// 				700: "#1d4ed8",
	// 				800: "#1e40af",
	// 				900: "#1e3a8a",
	// 				950: "#172554",
	// 			},

	// 			// Secondary Colors
	// 			secondary: {
	// 				50: "#f0f9ff",
	// 				100: "#e0f2fe",
	// 				200: "#bae6fd",
	// 				300: "#7dd3fc",
	// 				400: "#38bdf8",
	// 				500: "#0ea5e9",
	// 				600: "#0284c7",
	// 				700: "#0369a1",
	// 				800: "#075985",
	// 				900: "#0c4a6e",
	// 				950: "#082f49",
	// 			},

	// 			// Accent Colors
	// 			accent: {
	// 				orange: {
	// 					50: "#fff7ed",
	// 					100: "#ffedd5",
	// 					200: "#fed7aa",
	// 					300: "#fdba74",
	// 					400: "#fb923c",
	// 					500: "#f59e0b", // Main accent orange
	// 					600: "#ea580c",
	// 					700: "#c2410c",
	// 					800: "#9a3412",
	// 					900: "#7c2d12",
	// 				},
	// 				green: {
	// 					50: "#ecfdf5",
	// 					100: "#d1fae5",
	// 					200: "#a7f3d0",
	// 					300: "#6ee7b7",
	// 					400: "#34d399",
	// 					500: "#10b981", // Main accent green
	// 					600: "#059669",
	// 					700: "#047857",
	// 					800: "#065f46",
	// 					900: "#064e3b",
	// 				},
	// 			},

	// 			// Neutral Grays
	// 			brandGray: {
	// 				50: "#f9fafb",
	// 				100: "#f3f4f6",
	// 				200: "#E4E4E7",
	// 				300: "#d1d5db",
	// 				400: "#A1A1AA",
	// 				500: "#71717A",
	// 				600: "#52525B",
	// 				700: "#3F3F46",
	// 				800: "#27272A",
	// 				900: "#111827",
	// 				950: "#030712",
	// 			},

	// 			// Semantic Colors
	// 			success: {
	// 				50: "#f0fdf4",
	// 				100: "#dcfce7",
	// 				200: "#bbf7d0",
	// 				300: "#86efac",
	// 				400: "#4ade80",
	// 				500: "#6FB680",
	// 				600: "#16a34a",
	// 				700: "#15803d",
	// 				800: "#166534",
	// 				900: "#14532d",
	// 			},

	// 			warning: {
	// 				50: "#fffbeb",
	// 				100: "#fef3c7",
	// 				200: "#fde68a",
	// 				300: "#fcd34d",
	// 				400: "#fbbf24",
	// 				500: "#f59e0b",
	// 				600: "#d97706",
	// 				700: "#b45309",
	// 				800: "#92400e",
	// 				900: "#78350f",
	// 			},

	// 			error: {
	// 				50: "#fef2f2",
	// 				100: "#fee2e2",
	// 				200: "#fecaca",
	// 				300: "#fca5a5",
	// 				400: "#f87171",
	// 				500: "#ef4444",
	// 				600: "#dc2626",
	// 				700: "#b91c1c",
	// 				800: "#991b1b",
	// 				900: "#7f1d1d",
	// 			},

	// 			// Background & Surface Colors
	// 			background: "#ffffff",
	// 			surface: {
	// 				50: "#ffffff",
	// 				100: "#f9fafb",
	// 				200: "#f3f4f6",
	// 				300: "#e5e7eb",
	// 			},
	// 		},

	// 		// Typography
	// 		fontFamily: {
	// 			sans: [
	// 				"Pretendard",
	// 				"-apple-system",
	// 				"BlinkMacSystemFont",
	// 				"system-ui",
	// 				"sans-serif",
	// 			],
	// 			mono: [
	// 				"ui-monospace",
	// 				"SFMono-Regular",
	// 				"Monaco",
	// 				"Consolas",
	// 				"monospace",
	// 			],
	// 		},

	// 		fontSize: {
	// 			// Hero Typography
	// 			hero: [
	// 				"72px",
	// 				{ lineHeight: "110%", letterSpacing: "-1.5px", fontWeight: "500" },
	// 			],

	// 			// Heading Sizes
	// 			"6xl": [
	// 				"48px",
	// 				{ lineHeight: "120%", letterSpacing: "-1px", fontWeight: "600" },
	// 			],
	// 			"5xl": [
	// 				"40px",
	// 				{ lineHeight: "120%", letterSpacing: "-0.8px", fontWeight: "600" },
	// 			],
	// 			"4xl": [
	// 				"32px",
	// 				{ lineHeight: "125%", letterSpacing: "-0.5px", fontWeight: "600" },
	// 			],
	// 			"3xl": [
	// 				"28px",
	// 				{ lineHeight: "130%", letterSpacing: "-0.3px", fontWeight: "600" },
	// 			],
	// 			"2xl": ["24px", { lineHeight: "130%", fontWeight: "600" }],
	// 			xl: ["20px", { lineHeight: "135%", fontWeight: "600" }],
	// 			lg: ["18px", { lineHeight: "140%", fontWeight: "600" }],

	// 			// Body Text
	// 			base: ["16px", { lineHeight: "150%", fontWeight: "400" }],
	// 			sm: ["14px", { lineHeight: "150%", fontWeight: "400" }],
	// 			xs: ["12px", { lineHeight: "150%", fontWeight: "400" }],

	// 			// Button Text
	// 			"btn-lg": ["16px", { lineHeight: "150%", fontWeight: "500" }],
	// 			btn: ["14px", { lineHeight: "150%", fontWeight: "500" }],
	// 			"btn-sm": ["12px", { lineHeight: "150%", fontWeight: "500" }],
	// 		},

	// 		// Spacing Scale
	// 		spacing: {
	// 			"18": "4.5rem", // 72px
	// 			"22": "5.5rem", // 88px
	// 			"26": "6.5rem", // 104px
	// 			"30": "7.5rem", // 120px
	// 			"34": "8.5rem", // 136px
	// 			"38": "9.5rem", // 152px
	// 			"42": "10.5rem", // 168px
	// 			"46": "11.5rem", // 184px
	// 			"50": "12.5rem", // 200px
	// 		},

	// 		// Border Radius
	// 		borderRadius: {
	// 			none: "0",
	// 			sm: "4px",
	// 			DEFAULT: "6px",
	// 			md: "8px",
	// 			lg: "10px",
	// 			xl: "12px",
	// 			"2xl": "16px",
	// 			"3xl": "20px",
	// 			"4xl": "24px",
	// 			full: "9999px",
	// 		},

	// 		// Box Shadows
	// 		boxShadow: {
	// 			xs: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
	// 			sm: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)",
	// 			DEFAULT:
	// 				"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)",
	// 			md: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)",
	// 			lg: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)",
	// 			xl: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
	// 			"2xl": "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
	// 			inner: "inset 0 2px 4px 0 rgba(0, 0, 0, 0.05)",
	// 			card: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)",
	// 			"card-hover":
	// 				"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)",
	// 			"tab-active": "0px 2px 4px 0px #0E121B08",
	// 			"tab-active-hover": "0px 6px 10px 0px #0E121B0F",
	// 		},

	// 		// Animation & Transitions
	// 		transitionDuration: {
	// 			"150": "150ms",
	// 			"200": "200ms",
	// 			"250": "250ms",
	// 			"300": "300ms",
	// 			"400": "400ms",
	// 			"500": "500ms",
	// 		},

	// 		// Custom Animations
	// 		animation: {
	// 			"fade-scroll":
	// 				"fadeScroll var(--logo-scroll-duration, 20s) linear infinite",
	// 			"infinite-scroll":
	// 				"infiniteScroll var(--logo-scroll-duration, 20s) linear infinite",
	// 			"pulse-glow": "pulseGlow 3s ease-in-out infinite",
	// 			"fade-in": "fadeIn 0.5s ease-in-out",
	// 			"slide-up": "slideUp 0.3s ease-out",
	// 		},

	// 		// Custom Keyframes
	// 		keyframes: {
	// 			fadeScroll: {
	// 				"0%": { transform: "translateX(0)" },
	// 				"100%": { transform: "translateX(-100%)" },
	// 			},
	// 			infiniteScroll: {
	// 				"0%": { transform: "translateX(0)" },
	// 				"100%": { transform: "translateX(calc(-100% / 2))" },
	// 			},
	// 			pulseGlow: {
	// 				"0%, 100%": { opacity: "0.7", filter: "brightness(1)" },
	// 				"50%": { opacity: "1", filter: "brightness(1.1)" },
	// 			},
	// 			fadeIn: {
	// 				"0%": { opacity: "0", transform: "translateY(10px)" },
	// 				"100%": { opacity: "1", transform: "translateY(0)" },
	// 			},
	// 			slideUp: {
	// 				"0%": { transform: "translateY(100%)" },
	// 				"100%": { transform: "translateY(0)" },
	// 			},
	// 		},

	// 		// Container Sizes
	// 		maxWidth: {
	// 			container: "1440px",
	// 			content: "1200px",
	// 			prose: "768px",
	// 		},

	// 		// Z-Index Scale
	// 		zIndex: {
	// 			"1": "1",
	// 			"10": "10",
	// 			"20": "20",
	// 			"30": "30",
	// 			"40": "40",
	// 			"50": "50",
	// 			dropdown: "1000",
	// 			sticky: "1020",
	// 			fixed: "1030",
	// 			"modal-backdrop": "1040",
	// 			modal: "1050",
	// 			popover: "1060",
	// 			tooltip: "1070",
	// 			toast: "1080",
	// 		},
	// 	},
	// },
	plugins: [],
};

export default config;
