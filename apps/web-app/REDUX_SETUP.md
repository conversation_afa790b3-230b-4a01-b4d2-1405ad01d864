# Redux Setup for Authentication

This document explains the Redux setup for authentication in the web-app.

## Files Created

### 1. Store Configuration

- `src/store/index.ts` - Main store configuration with typed hooks
- `src/store/slices/authSlice.ts` - Authentication slice with async thunks
- `src/components/providers/ReduxProvider.tsx` - Redux provider wrapper

### 2. API Configuration

- `src/lib/api.ts` - Axios configuration with interceptors
- `src/types/auth.ts` - TypeScript types for authentication

### 3. Custom Hooks

- `src/hooks/useAuth.ts` - Custom hook for authentication actions

## Usage Examples

### 1. Using the useAuth Hook

```tsx
import { useAuth } from "@/hooks/useAuth";

function MyComponent() {
	const { user, isAuthenticated, isLoading, error, register, login, logout } =
		useAuth();

	const handleRegister = async () => {
		const result = await register({
			email: "<EMAIL>",
			password: "password123",
			firstName: "<PERSON>",
			lastName: "Do<PERSON>",
			organizationName: "My Company",
			role: "recruiter",
		});

		if (result.meta.requestStatus === "fulfilled") {
			// Registration successful
		}
	};

	return (
		<div>
			{isLoading && <p>Loading...</p>}
			{error && <p>Error: {error}</p>}
			{isAuthenticated ? (
				<p>Welcome, {user?.email}</p>
			) : (
				<button onClick={handleRegister}>Register</button>
			)}
		</div>
	);
}
```

### 2. API Endpoints Configuration

The API endpoints are configured in `src/lib/api.ts`. Update the `NEXT_PUBLIC_API_BASE_URL` environment variable to point to your backend API.

```env
NEXT_PUBLIC_API_BASE_URL=https://test-render-845v.onrender.com/api/v1
```

### 3. Available API Endpoints

```typescript
// Authentication
POST /auth/register - User registration
POST /auth/login - User login
POST /auth/logout - User logout
POST /auth/verify-email - Email verification
POST /auth/resend-verification - Resend verification code

// User management
GET /user/profile - Get user profile
PUT /user/profile - Update user profile
POST /user/avatar - Upload user avatar

// Company management
POST /company - Create company
PUT /company - Update company
POST /company/logo - Upload company logo
```

## Updated Components

### RecruiterSignUpForm

The `RecruiterSignUpForm` has been updated to demonstrate Redux usage:

- **Step 2**: Uses `handleRegister` function to register user
- **Step 3**: Uses `handleVerifyEmail` and `handleResendCode` for email verification
- Shows loading states and error handling
- Automatically progresses through steps on successful API calls

## Next Steps

1. **Update API Base URL**: Set the correct API endpoint in your environment variables
2. **Implement Similar Updates**: Apply the same pattern to `EmployerSignUpForm` and `FacilitatorSignUpForm`
3. **Add Error Display**: Consider adding error display components for better UX
4. **Add Form Validation**: Implement client-side validation before API calls
5. **Add Loading Indicators**: Enhance loading states throughout the application

## Environment Variables

Create a `.env.local` file in the web-app directory:

```env
NEXT_PUBLIC_API_BASE_URL=https://test-render-845v.onrender.com/api/v1
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## API Request Structure

Based on your API, the registration request should be structured as:

```json
{
	"firstName": "John",
	"lastName": "Doe",
	"organizationName": "Dan Comp.",
	"email": "<EMAIL>",
	"password": "Topetope@17",
	"role": "recruiter"
}
```

Note: The `confirmPassword` field is handled on the frontend for validation but not sent to the API.

## Email Verification

The email verification endpoint expects:

```json
{
	"email": "<EMAIL>",
	"verifyCode": "123456"
}
```

**For Development/Testing**: The API currently accepts `123456` as a dummy verification code for testing purposes.

## Testing

To test the Redux setup:

1. Start your backend API server
2. Update the API base URL in environment variables
3. Try the registration flow in the RecruiterSignUpForm
4. Check the Redux DevTools to see state changes
5. Verify API calls in the Network tab

The setup includes proper error handling, loading states, and automatic token management through localStorage and axios interceptors.
