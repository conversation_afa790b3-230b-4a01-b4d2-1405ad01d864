{"name": "web-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@africaskillz/ui": "*", "@hookform/resolvers": "^5.1.1", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/oxide": "^4.1.11", "@tailwindcss/postcss": "^4.1.11", "axios": "^1.11.0", "clsx": "^2.1.1", "lightningcss": "^1.30.1", "lucide-react": "^0.525.0", "next": "15.4.2", "postcss-loader": "^8.1.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "react-hot-toast": "^2.5.2", "react-redux": "^9.2.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "zod": "^4.0.9"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.2", "postcss": "^8.4.31", "typescript": "^5"}}