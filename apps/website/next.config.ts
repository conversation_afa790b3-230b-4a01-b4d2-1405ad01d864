import type { NextConfig } from "next";

console.log("WEBAPP_DOMAIN:", process.env.WEBAPP_DOMAIN);

const nextConfig: NextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  async rewrites() {
    // Ensure WEBAPP_DOMAIN is defined and includes the protocol (e.g., https://yourdomain.com)
    // This environment variable must be set in your Vercel project settings.
    if (!process.env.WEBAPP_DOMAIN) {
      throw new Error("WEBAPP_DOMAIN environment variable is not set. Please configure it in Vercel project settings.");
    }

    return [
      {
        source: "/onboarding/:path*",
        destination: `${process.env.WEBAPP_DOMAIN}/onboarding/:path*`,
        basePath: false,
      },
    ];
  },
  /* config options here */
};

export default nextConfig;
