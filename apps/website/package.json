{"name": "website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.1.1", "@tailwindcss/oxide": "^4.1.11", "clsx": "^2.1.1", "framer-motion": "^12.19.2", "lightningcss": "^1.30.1", "lucide-react": "^0.525.0", "next": "^15.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "tailwind-merge": "^3.3.1", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "^15.4.2", "tailwindcss": "^4", "typescript": "^5"}}