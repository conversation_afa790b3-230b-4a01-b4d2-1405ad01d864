import Link from "next/link";
import { Facebook, Twitter, Instagram } from "lucide-react";
import { Button, LabelInput } from "../ui";
import { AfricaSkillzLogoIcon } from "../common/icons";

const footerLinks = {
	"For Job Seekers": [
		{ name: "Browse Jobs", href: "/jobs" },
		{ name: "Career Resources", href: "/resources" },
		{ name: "Skills Training", href: "/training" },
		{ name: "Success Stories", href: "/stories" },
	],
	"For Employers": [
		{ name: "Post a Job", href: "/post-job" },
		{ name: "Find Talent", href: "/talent" },
		{ name: "Pricing Plans", href: "/pricing" },
		{ name: "Industry Insights", href: "/insights" },
	],
	Company: [
		{ name: "About Us", href: "/about" },
		{ name: "Contact", href: "/contact" },
		{ name: "Privacy Policy", href: "/privacy" },
		{ name: "Terms of Service", href: "/terms" },
	],
};

const socialLinks = [
	{ name: "Twitter", icon: Twitter, href: "#" },
	{ name: "Facebook", icon: Facebook, href: "#" },
	{ name: "Instagram", icon: Instagram, href: "#" },
];

export default function Footer() {
	return (
		<footer className="w-full bg-neutral-800">
			<div className="bg-neutral-800 text-white px-4 py-6 sm:px-[87px] sm:py-[60px] w-full mx-auto max-w-[1440px]">
				{/* Newsletter Section */}
				<div className="mb-6 sm:mb-[32px]">
					<div className="w-full mx-auto">
						<div className="bg-[#C0CCFF] rounded-[12px] p-4 sm:pl-8 sm:py-6 sm:pr-[18px] flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-0 justify-between">
							<h3 className="text-neutral-700 text-lg sm:text-[24px] sm:leading-[32px] font-normal mb-2 sm:mb-0">
								Stay connected by signing to <br className="hidden sm:block" />
								our newsletter
							</h3>
							<div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 w-full sm:w-auto">
								<div className="relative w-full sm:w-auto">
									<LabelInput
										inputType="email"
										placeholder="Enter your email"
										className="w-full sm:w-[473px]"
									/>
								</div>
								<Button
									variant="primary"
									size="md"
									className="bg-[#335CFF] hover:bg-[#2347E6] w-full sm:w-auto px-6 hidden md:block">
									Subscribe
								</Button>
								<Button
									variant="primary"
									size="xs"
									className="bg-[#335CFF] hover:bg-[#2347E6] md:w-full sm:w-auto px-6 md:hidden w-fit">
									Subscribe
								</Button>
							</div>
						</div>
					</div>
				</div>

				{/* Main Footer Content */}
				<div className="mb-6 sm:mb-[32px]">
					<div className="max-w-7xl mx-auto">
						<div className="flex flex-col sm:grid sm:grid-cols-[50%_50%] gap-6 sm:gap-16">
							{/* Left Side - Description */}
							<div className="mb-4 sm:mb-0">
								<p className="text-neutral-100 text-[14px] leading-[220x] md:text-[36px] md:leading-[44px]">
									The platform connecting <br className="hidden md:block" />{" "}
									professionals, employers, and <br /> educators across Africa
								</p>
							</div>

							{/* Right Side - Footer Links */}
							<div className="grid grid-cols-2 md:grid-cols-3 gap-4 md:gap-[92px]">
								{Object.entries(footerLinks).map(([title, links]) => (
									<div key={title} className="text-left">
										<h4 className="font-bold text-base sm:text-[16px] sm:leading-[26px] tracking-[-0.4px] text-neutral-50 mb-2 sm:mb-4 whitespace-nowrap">
											{title}
										</h4>
										<ul className="space-y-2 sm:space-y-4">
											{links.map((link) => (
												<li key={link.name}>
													<a
														href={link.href}
														className="font-normal text-base sm:text-[16px] sm:leading-[26px] tracking-[-0.4px] text-neutral-50 hover:text-white transition-colors duration-200 text-left whitespace-nowrap">
														{link.name}
													</a>
												</li>
											))}
										</ul>
									</div>
								))}
							</div>
						</div>
					</div>
				</div>

				{/* Bottom Bar */}
				<div className="border-t border-[#3E4550] pt-4 sm:pt-[36px]">
					<div className="flex flex-col sm:flex-row items-center justify-between gap-4 sm:gap-0">
						<div className="flex flex-col sm:flex-row items-center gap-2 sm:gap-[29px]">
							{/* Logo */}
							<div className="flex items-center">
								<AfricaSkillzLogoIcon className="h-8 sm:h-[38px]" />
							</div>
							<div className="h-8 sm:h-[38px] bg-[#52525B] w-[1.5px] hidden sm:block"></div>
							{/* Copyright */}
							<p className="text-white text-xs sm:text-sm leading-5 font-normal">
								© Copyright 2024, All Rights Reserved
							</p>
						</div>
						{/* Social Links */}
						<div className="flex items-center space-x-2 sm:space-x-4">
							{socialLinks.map((social) => {
								const Icon = social.icon;
								return (
									<Link
										key={social.name}
										href={social.href}
										className="w-8 h-8 bg-neutral-700 rounded-full flex items-center justify-center hover:bg-[#335CFF] transition-colors duration-200">
										<Icon className="w-4 h-4 text-white" />
									</Link>
								);
							})}
						</div>
					</div>
				</div>
			</div>
		</footer>
	);
}
