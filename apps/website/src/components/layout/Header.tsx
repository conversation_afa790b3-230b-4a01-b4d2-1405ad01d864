"use client";

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { Menu, X } from "lucide-react";
import {
	ChevronDown,
	AfricaSkillzLogoColoredIcon,
} from "@/components/common/icons";
import { cn } from "@/lib/utils";
import { useRouter } from "next/navigation";

// Add types for navigation items
type OpportunitySection = {
	section: string;
	items: { name: string; href: string }[];
};

type CardLink = {
	name: string;
	description: string;
	href: string;
};

type NavigationItem =
	| {
			name: "Opportunities";
			href: string;
			submenu: OpportunitySection[];
	  }
	| {
			name: "Resources" | "Directories";
			href: string;
			submenu: CardLink[];
	  }
	| {
			name: string;
			href: string;
			submenu?: undefined;
	  };

const navigationItems: NavigationItem[] = [
	{
		name: "Opportunities",
		href: "#",
		submenu: [
			{
				section: "Job Opportunities",
				items: [
					{ name: "Africaskillz Recruiter", href: "/job-opportunities" },
					{ name: "List All Jobs", href: "/jobs" },
					{ name: "Jobs by Recruiter", href: "/jobs/recruiter" },
					{ name: "Jobs by African Country", href: "/jobs/country" },
					{ name: "Jobs by Industry", href: "/jobs/industry" },
					{ name: "Jobs by Company", href: "/jobs/company" },
					{ name: "Jobs by African DFI", href: "/jobs/dfi" },
					{ name: "Legal Jobs", href: "/jobs/legal" },
					{ name: "International Jobs", href: "/jobs/international" },
				],
			},
			{
				section: "Procurement Opportunities",
				items: [
					{ name: "Procurements", href: "/procurement-opportunities" },
					{ name: "Africa PPP", href: "/procurement/ppp" },
					{ name: "BID Notices", href: "/procurement/bids" },
					{ name: "Corporate Procurement", href: "/procurement/corporate" },
					{ name: "Expressions of Interest (EOI)", href: "/procurement/eoi" },
					{ name: "Procurement Notice", href: "/procurement/notices" },
					{ name: "Request for Proposals (RFP)", href: "/procurement/rfp" },
					{ name: "Tenders", href: "/procurement/tenders" },
					{ name: "Terms of Reference", href: "/procurement/terms" },
				],
			},
			{
				section: "Educational Resources",
				items: [
					{ name: "Courses", href: "/courses" },
					{ name: "African Scholarships", href: "/courses/scholarships" },
					{
						name: "International Scholarships",
						href: "/scholarships/international",
					},
				],
			},
		],
	},
	{
		name: "Resources",
		href: "#",
		submenu: [
			{
				name: "Insights",
				description: "Articles, news & Industry trends",
				href: "/insights-resources",
			},
			{
				name: "Events & Media",
				description: "Conferences, workshops and webinars",
				href: "/events",
			},
			{
				name: "Corporate Notices",
				description: "Important announcements and notices",
				href: "/corporate-notices",
			},
		],
	},
	{
		name: "Directories",
		href: "#",
		submenu: [
			{
				name: "Recruiter Directory",
				description: "Easily access all partner recruiters",
				href: "/directory/recruiters",
			},
			{
				name: "Employer Directory",
				description: "A directory of African partner companies",
				href: "/directory/employers",
			},
			{
				name: "University Directory",
				description: "A list of all Partner universities",
				href: "/directory/universities",
			},
		],
	},
	{ name: "AfricaSkillz Recruiters", href: "/recruiters" },
	{ name: "Pricing", href: "/pricing" },
];

// const languages = [
// 	{ code: "en", name: "English", flag: "🇺🇸" },
// 	{ code: "fr", name: "Français", flag: "🇫🇷" },
// 	{ code: "ar", name: "العربية", flag: "🇸🇦" },
// 	{ code: "pt", name: "Português", flag: "🇵🇹" },
// 	{ code: "es", name: "Español", flag: "🇪🇸" },
// 	{ code: "sw", name: "Kiswahili", flag: "🇰🇪" },
// 	{ code: "am", name: "አማርኛ", flag: "🇪🇹" },
// 	{ code: "ha", name: "Hausa", flag: "🇳🇬" },
// 	{ code: "yo", name: "Yorùbá", flag: "🇳🇬" },
// 	{ code: "ig", name: "Igbo", flag: "🇳🇬" },
// 	{ code: "zu", name: "isiZulu", flag: "🇿🇦" },
// 	{ code: "af", name: "Afrikaans", flag: "🇿🇦" },
// ];

export default function Header() {
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
	const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
	const [activeOpportunitySection, setActiveOpportunitySection] = useState<
		string | null
	>(null);
	// const [selectedLanguage, setSelectedLanguage] = useState(languages[0]);
	// const [isLanguageDropdownOpen, setIsLanguageDropdownOpen] = useState(false);
	const languageDropdownRef = useRef<HTMLDivElement>(null);
	const router = useRouter();

	const toggleMobileMenu = () => {
		setIsMobileMenuOpen(!isMobileMenuOpen);
		// Reset dropdown state when closing mobile menu
		if (isMobileMenuOpen) {
			setActiveDropdown(null);
			setActiveOpportunitySection(null);
		}
	};

	const handleDropdownToggle = (itemName: string) => {
		setActiveDropdown(activeDropdown === itemName ? null : itemName);
	};

	const handleOpportunitySectionToggle = (sectionName: string) => {
		setActiveOpportunitySection(
			activeOpportunitySection === sectionName ? null : sectionName
		);
	};

	const handleMobileLinkClick = (href: string) => {
		console.log("Mobile link clicked:", href);
		setIsMobileMenuOpen(false);
		setActiveDropdown(null);
		setActiveOpportunitySection(null);
		router.push(href);
	};

	// const handleLanguageSelect = (language: (typeof languages)[0]) => {
	// 	setSelectedLanguage(language);
	// 	setIsLanguageDropdownOpen(false);
	// };

	// const toggleLanguageDropdown = () => {
	// 	setIsLanguageDropdownOpen(!isLanguageDropdownOpen);
	// };

	// Handle click outside to close dropdowns
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			// Close language dropdown
			if (
				languageDropdownRef.current &&
				!languageDropdownRef.current.contains(event.target as Node)
			) {
				// setIsLanguageDropdownOpen(false);
			}
			// Close navigation dropdowns
			const target = event.target as HTMLElement;
			if (!target.closest(".dropdown-container")) {
				setActiveDropdown(null);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, []);

	return (
		<header className="w-full bg-white" id="header">
			<div className="bg-white sticky top-0 z-50 w-full mx-auto max-w-[1440px]">
				<div className="w-full sm:px-4 sm:pt-[36px] px-4 py-2.5">
					<div className="flex items-center justify-between w-full">
						{/* Logo */}
						<div className="flex items-center gap-6">
							<Link href="/" className="flex items-center space-x-2">
								<div className="w-full">
									<AfricaSkillzLogoColoredIcon className="w-[97px] h-[41px] md:w-[146px] md:h-[63px]" />
								</div>
							</Link>
							<div className="hidden sm:block w-[1.5px] h-[50px] bg-[#E4E4E7]"></div>

							{/* Desktop Navigation */}
							<nav className="hidden lg:flex items-center space-x-8">
								{navigationItems.map((item) => (
									<div
										key={item.name}
										className="relative group dropdown-container">
										{item.submenu ? (
											<button
												className="flex items-center cursor-pointer space-x-1 text-gray-600 font-semibold text-base hover:text-blue-600 transition-colors duration-200"
												onClick={() => handleDropdownToggle(item.name)}>
												<span>{item.name}</span>
												<ChevronDown className="w-4 h-4" />
											</button>
										) : (
											<Link
												href={item.href}
												className="text-gray-600 font-semibold text-base hover:text-blue-600 transition-colors duration-200">
												{item.name}
											</Link>
										)}

										{/* Dropdown Menu */}
										{item.submenu && activeDropdown === item.name && (
											<div
												className={
													item.name === "Opportunities"
														? "absolute top-full left-0 mt-2 bg-white rounded-[16px] shadow-lg border border-gray-200 z-50 flex gap-4 p-[20px_24px] w-fit h-fit"
														: "absolute top-full left-0 mt-2 bg-white rounded-[16px] shadow-lg border border-gray-200 z-50 grid grid-cols-2 gap-4 p-[12px_9px] w-[426px] h-[216px]"
												}
												style={{
													gap: item.name === "Opportunities" ? 4 : 16,
													paddingTop: item.name === "Opportunities" ? 20 : 12,
													paddingRight: item.name === "Opportunities" ? 24 : 9,
													paddingBottom:
														item.name === "Opportunities" ? 20 : 12,
													paddingLeft: item.name === "Opportunities" ? 24 : 9,
												}}>
												{item.name === "Opportunities" ? (
													// Render three columns for Opportunities
													(item.submenu as OpportunitySection[]).map(
														(section) => {
															if (
																"section" in section &&
																Array.isArray(section.items)
															) {
																return (
																	<div
																		key={section.section}
																		className="flex flex-col w-fit gap-2 p-4 rounded-2xl bg-[#FAFAFA] whitespace-nowrap">
																		<div className="text-[#52525B] text-base leading-[26px] font-semibold cursor-default">
																			{section.section}
																		</div>
																		{section.items.map((subItem) => (
																			<Link
																				key={subItem.name}
																				href={subItem.href}
																				className="block px-0 py-0.5 text-sm whitespace-nowrap text-[#52525B] leading-[22px] hover:bg-gray-50 hover:text-blue-600 rounded transition-colors duration-200">
																				{subItem.name}
																			</Link>
																		))}
																	</div>
																);
															}
															return null;
														}
													)
												) : (
													// Render card-style links for Resources and Directories
													<>
														{(item.submenu as CardLink[]).map((subItem) => {
															if (
																"name" in subItem &&
																"description" in subItem &&
																"href" in subItem
															) {
																return (
																	<Link
																		key={subItem.name}
																		href={subItem.href}
																		className="flex flex-col gap-[2px] items-start bg-gray-50 hover:bg-blue-50 transition-colors duration-200 p-2 rounded-[8px] min-w-[180px] max-w-[200px] h-fit">
																		<span className="text-[#52525B] text-base leading-[26px] font-semibold">
																			{subItem.name}
																		</span>
																		<span className="text-sm text-[#52525B] leading-[22px]">
																			{subItem.description}
																		</span>
																	</Link>
																);
															}
															return null;
														})}
													</>
												)}
											</div>
										)}
									</div>
								))}
							</nav>
						</div>

						{/* Language selector */}
						{/* <div
						ref={languageDropdownRef}
						className="hidden md:block relative ml-8">
						<button
							onClick={toggleLanguageDropdown}
							className="flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors duration-200">
							<GlobeIcon />
							<span className="text-gray-600 font-semibold text-base flex items-center space-x-1">
								<span>{selectedLanguage.name}</span>
							</span>
							<ChevronDown
								className={cn(
									"w-3 h-3 transition-transform duration-200",
									isLanguageDropdownOpen && "rotate-180"
								)}
							/>
						</button>

						{/* Language Dropdown */}
						{/* {isLanguageDropdownOpen && (
							<div className="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 max-h-64 overflow-y-auto">
								{languages.map((language) => (
									<button
										key={language.code}
										onClick={() => handleLanguageSelect(language)}
										className={cn(
											"w-full flex items-center space-x-3 px-4 py-2 text-sm hover:bg-gray-50 transition-colors duration-200",
											selectedLanguage.code === language.code &&
												"bg-blue-50 text-blue-600"
										)}>
										<span className="text-lg">{language.flag}</span>
										<span>{language.name}</span>
									</button>
								))}
							</div>
						)}
					</div>  */}

						{/* Right side actions */}
						<div className="flex items-center space-x-4">
							{/* Auth buttons */}
							<div className="hidden md:flex items-center space-x-3">
								<Link
									href="/auth/login"
									className="text-[#335CFF] hover:text-blue-600 transition-colors duration-200 border border-[#335CFF] rounded-[10px] px-[18.5px] py-[6px] text-sm font-medium">
									Login
								</Link>
								<a
									href={
										process.env.NEXT_PUBLIC_WEBAPP_URL ||
										"http://localhost:3001"
									}
									className="btn-primary text-sm">
									Sign Up
								</a>
							</div>

							{/* Mobile menu button */}
							<button
								onClick={toggleMobileMenu}
								className="lg:hidden p-2 text-gray-700 hover:text-blue-600 transition-colors duration-200"
								aria-label="Open mobile menu">
								{isMobileMenuOpen ? (
									<X className="w-6 h-6" />
								) : (
									<Menu className="w-6 h-6" />
								)}
							</button>
						</div>
					</div>

					{/* Mobile Navigation */}
					{isMobileMenuOpen && (
						<>
							{/* Background Overlay */}
							<div
								className="fixed inset-0 bg-black bg-opacity-30 z-modal-backdrop"
								onClick={toggleMobileMenu}></div>

							{/* Mobile Menu Panel */}
							<div className="lg:hidden absolute top-full left-0 right-0 rounded-[16px] bg-white border-gray-200 shadow-lg z-modal max-h-[80vh] overflow-y-auto p-5 dropdown-container">
								{/* Close Button */}
								<button
									className="mx-auto mb-8 flex items-center justify-center p-2 rounded-full bg-[#F4F4F5] hover:bg-gray-200 text-[#52525B]"
									onClick={toggleMobileMenu}
									aria-label="Close mobile menu">
									<X className="w-8 h-8" />
								</button>
								{/* Mobile Navigation */}
								<nav className="flex-1 space-y-4">
									{navigationItems.map((item) => (
										<div key={item.name} className="bg-[#F4F4F5] rounded-[4px]">
											{item.submenu ? (
												<div className="px-3 py-2.5">
													<button
														onClick={(e) => {
															e.stopPropagation();
															handleDropdownToggle(item.name);
														}}
														className="flex items-center  justify-between w-full text-left text-[#3F3F46] font-medium text-sm leading-[22px]">
														<span>{item.name}</span>
														<ChevronDown
															className={cn(
																"w-5 h-5 text-[#3F3F46] transition-transform duration-200",
																activeDropdown === item.name && "rotate-180"
															)}
														/>
													</button>
													{activeDropdown === item.name && (
														<div className="bg-[#F4F4F5] flex flex-col gap-3 h-full mt-4 relative z-popover">
															{item.name === "Opportunities"
																? (item.submenu as OpportunitySection[]).map(
																		(section) => (
																			<div
																				key={section.section}
																				className="mb-2 bg-[#E4E4E7] rounded-[8px]">
																				<button
																					onClick={() =>
																						handleOpportunitySectionToggle(
																							section.section
																						)
																					}
																					className="flex items-center justify-between w-full px-4 py-2 text-left text-[#3F3F46] text-sm font-medium">
																					<span>{section.section}</span>
																					<ChevronDown
																						className={cn(
																							"w-4 h-4 transition-transform duration-200",
																							activeOpportunitySection ===
																								section.section && "rotate-180"
																						)}
																					/>
																				</button>
																				{activeOpportunitySection ===
																					section.section && (
																					<div className="px-4 pb-2 space-y-1">
																						{section.items.map((subItem) => (
																							<button
																								key={subItem.name}
																								onClick={() =>
																									handleMobileLinkClick(
																										subItem.href
																									)
																								}
																								className="block text-[#52525B] text-xs font-normal py-1 px-2 rounded hover:bg-blue-50 hover:text-blue-600 w-full text-left cursor-pointer">
																								{subItem.name}
																							</button>
																						))}
																					</div>
																				)}
																			</div>
																		)
																  )
																: (item.submenu as CardLink[]).map(
																		(subItem) => (
																			<div key={subItem.name}>
																				<button
																					onClick={() =>
																						handleMobileLinkClick(subItem.href)
																					}
																					className="block text-gray-700 text-sm py-2 px-2 rounded hover:bg-blue-50 hover:text-blue-600 w-full text-left cursor-pointer">
																					<span className="font-medium text-sm leading-[22px] text-[#3F3F46]">
																						{subItem.name}
																					</span>
																					<span className="block text-xs text-[#52525B] font-regular leading-[16px]">
																						{subItem.description}
																					</span>
																				</button>
																			</div>
																		)
																  )}
														</div>
													)}
												</div>
											) : (
												<button
													onClick={() => handleMobileLinkClick(item.href)}
													className="w-full text-left px-4 py-3 text-[#3F3F46] font-medium text-sm leading-[22px] block cursor-pointer">
													{item.name}
												</button>
											)}
										</div>
									))}
								</nav>

								{/* Language Selector Accordion (outside nav) */}
								{/* <div className="bg-gray-100 rounded-xl mt-3">
									<button
										onClick={() => handleDropdownToggle("language")}
										className="flex items-center justify-between w-full px-4 py-3 text-left text-gray-800 font-semibold text-base">
										<span className="flex items-center gap-2">
											<GlobeIcon className="w-5 h-5" /> {selectedLanguage.name}
										</span>
										<ChevronDown
											className={cn(
												"w-5 h-5 transition-transform duration-200",
												activeDropdown === "language" && "rotate-180"
											)}
										/>
									</button>
									{activeDropdown === "language" && (
										<div className="mt-1 ml-2 bg-gray-200 rounded-lg px-3 py-2 space-y-1 max-h-40 overflow-y-auto">
											{languages.map((language) => (
												<button
													key={language.code}
													onClick={() => {
														handleLanguageSelect(language);
														setActiveDropdown(null);
													}}
													className={cn(
														"w-full flex items-center space-x-3 px-2 py-2 text-sm rounded transition-colors duration-200",
														selectedLanguage.code === language.code &&
															"bg-blue-50 text-blue-600"
													)}>
													<span className="text-lg">{language.flag}</span>
													<span>{language.name}</span>
												</button>
											))}
										</div>
									)}
								</div> */}

								{/* Auth Buttons */}
								<div className="flex gap-2 mt-6">
									<button
										className="flex-1 border border-[#335CFF] text-[#335CFF] rounded-[10px] py-2 text-center font-medium hover:bg-blue-50 cursor-pointer"
										onClick={() => handleMobileLinkClick("/auth/login")}>
										Login
									</button>
									<a
										href={
											process.env.NEXT_PUBLIC_WEBAPP_URL ||
											"http://localhost:3001"
										}
										className="flex-1 bg-[#335CFF] text-white rounded-[10px] py-2 text-center font-medium hover:bg-blue-600 cursor-pointer">
										Sign Up
									</a>
								</div>
							</div>
						</>
					)}
				</div>
			</div>
		</header>
	);
}
