"use client";

import { useState } from "react";

import { Button, LabelInput } from "@/components/ui";
import Image from "next/image";
import {
	SuitcaseIcon,
	GraduationHatIcon,
	CheckMarkIcon,
	LightningIcon,
} from "@/components/common/icons";

export default function HeroSection() {
	const [searchQuery, setSearchQuery] = useState("");
	const [location, setLocation] = useState("");
	const [categories, setCategories] = useState("");
	const [jobType, setJobType] = useState("");

	const handleSearch = (e: React.FormEvent) => {
		e.preventDefault();
		console.log("Search:", { searchQuery, location, categories, jobType });
	};

	return (
		<section className=" w-full bg-white" >
			<div className="relative h-fit bg-white overflow-hidden px-4 md:pb-4 py-8 flex flex-col md:grid grid-cols-2 w-full mx-auto max-w-[1440px]">
				{/* Content */}
				<div className=" md:pl-[56px] md:pb-[80px] md:pt-[72px]">
					<div className="flex flex-col gap-5 items-start">
						<div className="p-2    flex items-center sm:pl-2 sm:pr-8 sm:py-2 space-x-2 rounded-4xl border border-brandGray-200  ">
							<div className="flex -space-x-2">
								<div className="w-5 h-5 md:w-7 md:h-7 rounded-full  border-2 border-white">
									<Image
										src="/trusted-img-1.jpg"
										alt="avatar image"
										width={28}
										height={28}
										className="rounded-full "
									/>
								</div>
								<div className="w-5 h-5 md:w-7 md:h-7 rounded-full  border-2 border-white">
									<Image
										src="/trusted-img-2.jpg"
										alt="avatar image"
										width={28}
										height={28}
										className="rounded-full "
									/>
								</div>
								<div className="w-5 h-5 md:w-7 md:h-7 rounded-full  border-2 border-white">
									<Image
										src="/trusted-img-3.jpg"
										alt="avatar image"
										width={28}
										height={28}
										className="rounded-full "
									/>
								</div>
								<div className="w-5 h-5 md:w-7 md:h-7 rounded-full  border-2 border-white">
									<Image
										src="/trusted-img-4.jpg"
										alt="avatar image"
										width={28}
										height={28}
										className="rounded-full "
									/>
								</div>
							</div>
							<span className="text-brandGray-500 text-[12px] md:text-sm font-medium">
								Trusted by professionals across Africa.
							</span>
						</div>

						{/* Trust Indicators for desktop */}
						<h1 className="text-4xl md:text-7xl font-medium text-brandGray-800 leading-[110%] hidden md:flex flex-col">
							<span className="flex items-baseline gap-2">
								Career.
								<span className="flex -space-x-2 ">
									<span className="w-12 h-12 rounded-2xl bg-primary-500-light flex items-center justify-center border-4 border-white">
										<SuitcaseIcon />
									</span>
									<span className="w-12 h-12 rounded-2xl bg-[#8F34B4] flex items-center justify-center border-4 border-white">
										<GraduationHatIcon color="#FAFAFA" />
									</span>
									<span className="w-12 h-12 rounded-2xl bg-[#D25625] flex items-center justify-center border-4 border-white">
										<CheckMarkIcon />
									</span>
								</span>
							</span>
							<span>Procurement.</span>
							<span>Education.</span>
						</h1>

						<h1 className="text-4xl md:text-7xl font-medium text-brandGray-800 leading-[110%] flex flex-col md:hidden">
							<span className="flex items-baseline gap-2">Career.</span>
							<span>Talent.</span>
							<span className="flex items-baseline gap-2">
								Recruitment.
								<span className="flex -space-x-2 ">
									<span className="w-[27px] h-[27px] rounded-[8px] bg-primary-500-light flex items-center justify-center border-4 border-white">
										<SuitcaseIcon className="h-[13.5px] w-[13.5px]" />
									</span>
									<span className="w-[27px] h-[27px] rounded-[8px] bg-[#8F34B4] flex items-center justify-center border-4 border-white">
										<GraduationHatIcon
											color="#FAFAFA"
											className="h-[13.5px] w-[13.5px]"
										/>
									</span>
									<span className="w-[27px] h-[27px] rounded-[8px] bg-[#D25625] flex items-center justify-center border-4 border-white">
										<CheckMarkIcon className="h-[13.5px] w-[13.5px]" />
									</span>
								</span>
							</span>
						</h1>
						<p className="text-base leading-[26px]  md:text-2xl text-brandGray-400 font-semibold md:leading-8">
							The platform connecting professionals, <br /> employers, and
							educators across Africa
						</p>
					</div>

					{/* logo area */}
					<div className="md:mt-2 mt-8">
						<p className="hidden md:block text-base text-brandGray-600 font-bold mb-4">
							Supported development agencies in Africa
						</p>
						<div className="hidden md:block relative w-[50%] overflow-hidden rounded-lg">
							{/* Animated logo container - true infinite scroll */}
							<div className="flex items-center animate-infinite-scroll">
								{/* First complete set */}
								<div className="flex items-center space-x-6 flex-shrink-0">
									<Image
										src="/brand1.png"
										alt="brand Logo"
										width={51}
										height={51}
										className="opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
									<Image
										src="/brand2.png"
										alt="brand Logo"
										width={51}
										height={51}
										className="opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
									<Image
										src="/brand3.png"
										alt="brand Logo"
										width={51}
										height={51}
										className="opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
									<Image
										src="/brand4.png"
										alt="brand Logo"
										width={51}
										height={51}
										className="opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
									<Image
										src="/brand5.png"
										alt="brand Logo"
										width={51}
										height={51}
										className="opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
								</div>

								{/* Spacer */}
								<div className="w-6 flex-shrink-0"></div>

								{/* Duplicate set for seamless loop */}
								<div className="flex items-center space-x-6 flex-shrink-0">
									<Image
										src="/brand1.png"
										alt="brand Logo"
										width={51}
										height={51}
										className="opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
									<Image
										src="/brand2.png"
										alt="brand Logo"
										width={51}
										height={51}
										className="opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
									<Image
										src="/brand3.png"
										alt="brand Logo"
										width={51}
										height={51}
										className="opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
									<Image
										src="/brand4.png"
										alt="brand Logo"
										width={51}
										height={51}
										className="opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
									<Image
										src="/brand5.png"
										alt="brand Logo"
										width={51}
										height={51}
										className="opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
								</div>
							</div>

							{/* Left fade gradient - extended to cover edge completely */}
							<div className="absolute -left-1 top-0 w-24 h-full bg-gradient-to-r from-white via-white/70 to-transparent pointer-events-none z-10"></div>

							{/* Right fade gradient - extended to cover edge completely */}
							<div className="absolute -right-1 top-0 w-24 h-full bg-gradient-to-l from-white via-white/70 to-transparent pointer-events-none z-10"></div>
						</div>
					</div>
				</div>

				<div className="flex justify-center md:h-[655px] md:p-8 w-full py-4 px-3 relative shadow-xl md:shadow-none rounded-2xl md:rounded-none">
					<div
						className="absolute top-0 left-0 w-full h-full rounded-4xl p-8 overflow-hidden hidden md:block"
						style={{
							backgroundImage: "url(/hero-img.png)",
							backgroundSize: "cover",
							backgroundPosition: "center",
							backgroundRepeat: "no-repeat",
						}}>
						{/* Gradient Overlay */}
						<div className="absolute inset-0 bg-gradient-to-t from-primary-500-light/90 via-primary-500-light/20 to-primary-500-light/30 rounded-[24px]"></div>
					</div>
					{/* Search Card with Background Image */}
					<div className="w-full h-fit bg-white rounded-3xl md:py-8 md:px-10 relative z-20 mt-auto">
						{/* Content */}
						<div className="relative z-10 flex flex-col gap-8">
							<div className="flex items-center py-2.5 space-x-2">
								<div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded flex items-center justify-center">
									<LightningIcon className="w-6 h-6 md:w-auto md:h-auto" />
								</div>
								<h2 className="md:text-[36px] text-[20px] font-semibold text-brandGray-700">
									Find Opportunities
								</h2>
							</div>

							<form onSubmit={handleSearch} className="space-y-6 z-20">
								<div className="grid md:grid-cols-2 gap-5">
									{/* Keywords */}
									<LabelInput
										label="Keywords"
										inputType="search"
										placeholder="Job title, skill, or company"
										value={searchQuery}
										onChange={(e) => setSearchQuery(e.target.value)}
										labelClassName="text-neutral-700 z-20"
									/>

									{/* Location */}
									<LabelInput
										label="Location"
										inputType="dropdown"
										placeholder="All locations"
										value={location}
										onSelect={(value) => setLocation(value)}
										data={[
											{ value: "", label: "All locations" },
											{ value: "nigeria", label: "Nigeria" },
											{ value: "kenya", label: "Kenya" },
											{ value: "south-africa", label: "South Africa" },
											{ value: "ghana", label: "Ghana" },
											{ value: "egypt", label: "Egypt" },
										]}
										labelClassName="text-neutral-700 z-20"
									/>

									{/* Categories */}
									<LabelInput
										label="Categories"
										inputType="dropdown"
										placeholder="All Categories"
										value={categories}
										onSelect={(value) => setCategories(value)}
										data={[
											{ value: "", label: "All Categories" },
											{ value: "technology", label: "Technology" },
											{ value: "finance", label: "Finance" },
											{ value: "healthcare", label: "Healthcare" },
											{ value: "education", label: "Education" },
											{ value: "marketing", label: "Marketing" },
										]}
										labelClassName="text-neutral-700 z-20"
									/>

									{/* Job Type */}
									<LabelInput
										label="Job Type"
										inputType="dropdown"
										placeholder="All Types"
										value={jobType}
										onSelect={(value) => setJobType(value)}
										data={[
											{ value: "", label: "All Types" },
											{ value: "full-time", label: "Full-time" },
											{ value: "part-time", label: "Part-time" },
											{ value: "contract", label: "Contract" },
											{ value: "internship", label: "Internship" },
											{ value: "remote", label: "Remote" },
										]}
										labelClassName="text-neutral-700 z-50"
									/>
								</div>

								<Button
									type="submit"
									variant="primary"
									size="lg"
									fullWidth
									className="text-lg font-medium hidden md:block">
									Search Opportunities
								</Button>
								<Button
									type="submit"
									variant="primary"
									size="sm"
									fullWidth
									className="text-md font-medium md:hidden">
									Search Opportunities
								</Button>
							</form>
						</div>
					</div>
				</div>
			</div>
		</section>
	);
}
