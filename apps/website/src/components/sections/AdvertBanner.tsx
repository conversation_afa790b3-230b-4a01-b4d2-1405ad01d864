"use client";

import Image from "next/image";
import { useEffect, useRef } from "react";

interface Banner {
	id: number;
	image: string;
}

const banners: Banner[] = [
	{
		id: 1,
		image: "/Ad1.png",
	},
	{
		id: 2,
		image: "/Ad2.png",
	},
	{
		id: 3,
		image: "/Ad1.png",
	},
];

const BannerComponent = ({ banner }: { banner: Banner }) => {
	return (
		<div className="w-[90vw] h-[120px] sm:w-[810px] sm:h-[216px] overflow-hidden rounded-[16px] relative flex-shrink-0">
			<Image
				src={banner.image}
				alt={`Banner ${banner.id}`}
				fill
				className="w-full h-full object-cover"
			/>
		</div>
	);
};

export default function AdvertBanner() {
	const scrollRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		const scrollContainer = scrollRef.current;
		if (!scrollContainer) return;

		let scrollAmount = 0;
		const scrollSpeed = 1; // pixels per frame
		const maxScroll = scrollContainer.scrollWidth - scrollContainer.clientWidth;

		const smoothScroll = () => {
			if (scrollContainer) {
				scrollAmount += scrollSpeed;

				// Reset to beginning when reaching the end
				if (scrollAmount >= maxScroll) {
					scrollAmount = 0;
				}

				scrollContainer.scrollLeft = scrollAmount;
			}
		};

		const intervalId = setInterval(smoothScroll, 16); // ~60fps

		return () => clearInterval(intervalId);
	}, []);

	// Duplicate banners for seamless loop
	const duplicatedBanners = [...banners, ...banners];

	return (
		<section className="w-full bg-white">
			<div className="py-4 sm:py-[21px] bg-white overflow-hidden w-full mx-auto max-w-[1440px]">
				<div
					ref={scrollRef}
					className="flex gap-3 sm:gap-5 items-center overflow-hidden pl-2 sm:pl-[22px]"
					style={{ scrollBehavior: "auto" }}>
					{duplicatedBanners.map((banner, index) => (
						<BannerComponent key={`${banner.id}-${index}`} banner={banner} />
					))}
				</div>
			</div>
		</section>
	);
}
