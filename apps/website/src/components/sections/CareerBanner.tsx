export default function CareerBanner() {
	return (
		<section className="w-full bg-primary-300-light" >
			<div className="bg-primary-300-light py-3 sm:py-6 overflow-x-hidden w-full mx-auto max-w-[1440px]">
				<div className="overflow-hidden whitespace-nowrap">
					<div className="animate-scroll-text inline-block">
						<span className="text-2xl sm:text-4xl md:text-6xl lg:text-[72px] font-500 leading-10 sm:leading-[60px] md:leading-[80px] lg:leading-[110px] text-neutral-50 inline-block">
							{" "}
							&nbsp;Career{" "}
							<span className="text-2xl sm:text-4xl md:text-6xl lg:text-[72px] font-500 leading-10 sm:leading-[60px] md:leading-[80px] lg:leading-[110px] text-[#A1B4FF] animate-twinkle">
								✦
							</span>{" "}
							Talent{" "}
							<span className="text-2xl sm:text-4xl md:text-6xl lg:text-[72px] font-500 leading-10 sm:leading-[60px] md:leading-[80px] lg:leading-[110px] text-[#A1B4FF] animate-twinkle">
								✦
							</span>{" "}
							Recruitment{" "}
							<span className="text-2xl sm:text-4xl md:text-6xl lg:text-[72px] font-500 leading-10 sm:leading-[60px] md:leading-[80px] lg:leading-[110px] text-[#A1B4FF] animate-twinkle">
								✦
							</span>{" "}
							Procurement{" "}
							<span className="text-2xl sm:text-4xl md:text-6xl lg:text-[72px] font-500 leading-10 sm:leading-[60px] md:leading-[80px] lg:leading-[110px] text-[#A1B4FF] animate-twinkle">
								✦
							</span>{" "}
						</span>
						{/* Duplicate for seamless loop */}
						<span className="text-2xl sm:text-4xl md:text-6xl lg:text-[72px] font-500 leading-10 sm:leading-[60px] md:leading-[80px] lg:leading-[110px] text-neutral-50 inline-block">
							{" "}
							&nbsp;Career{" "}
							<span className="text-2xl sm:text-4xl md:text-6xl lg:text-[72px] font-500 leading-10 sm:leading-[60px] md:leading-[80px] lg:leading-[110px] text-[#A1B4FF] animate-twinkle">
								✦
							</span>{" "}
							Talent{" "}
							<span className="text-2xl sm:text-4xl md:text-6xl lg:text-[72px] font-500 leading-10 sm:leading-[60px] md:leading-[80px] lg:leading-[110px] text-[#A1B4FF] animate-twinkle">
								✦
							</span>{" "}
							Recruitment{" "}
							<span className="text-2xl sm:text-4xl md:text-6xl lg:text-[72px] font-500 leading-10 sm:leading-[60px] md:leading-[80px] lg:leading-[110px] text-[#A1B4FF] animate-twinkle">
								✦
							</span>{" "}
							Procurement{" "}
							<span className="text-2xl sm:text-4xl md:text-6xl lg:text-[72px] font-500 leading-10 sm:leading-[60px] md:leading-[80px] lg:leading-[110px] text-[#A1B4FF] animate-twinkle">
								✦
							</span>{" "}
						</span>
					</div>
				</div>
			</div>
		</section>
	);
}
