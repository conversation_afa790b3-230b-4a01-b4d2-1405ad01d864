"use client";

import { Calendar } from "lucide-react";
import Image from "next/image";

interface EventCard {
	id: number;
	title: string;
	organizer: string;
	date: string;
	time: string;
	price: string;
	image: string;
	isFree: boolean;
}

const eventData: EventCard[] = [
	{
		id: 1,
		title: "The Lagos Network: For Tech startups.",
		organizer: "TechAcademy Africa",
		date: "Thur, May 8th",
		time: "2pm",
		price: "Free",
		image: "/corporate1.png",
		isFree: true,
	},
	{
		id: 2,
		title: "Digital Marketing Masterclass",
		organizer: "Marketing Hub Africa",
		date: "Fri, May 9th",
		time: "10am",
		price: "$56",
		image: "/corporate2.png",
		isFree: false,
	},
	{
		id: 3,
		title: "AI & Machine Learning Workshop",
		organizer: "Data Science Nigeria",
		date: "Sat, May 10th",
		time: "9am",
		price: "$96",
		image: "/corporate3.png",
		isFree: false,
	},
	{
		id: 4,
		title: "Fintech Innovation Summit",
		organizer: "Lagos Business School",
		date: "Mon, May 12th",
		time: "1pm",
		price: "Free",
		image: "/corporate1.png",
		isFree: true,
	},
	{
		id: 5,
		title: "UX/UI Design Bootcamp",
		organizer: "Design Academy",
		date: "Wed, May 14th",
		time: "11am",
		price: "$120",
		image: "/corporate2.png",
		isFree: false,
	},
];

const EventCard = ({ event }: { event: EventCard }) => {
	return (
		<div
			className="bg-white rounded-2xl overflow-hidden min-w-[85vw] sm:min-w-[320px] transition-shadow duration-300"
			style={{ boxShadow: "0px 0px 16px 4px #DFDFDF40" }}>
			{/* Event Image */}
			<div className="relative h-32 overflow-hidden">
				<Image
					src={event.image}
					alt={event.title}
					className="w-full h-full object-cover"
					width={320}
					height={128}
				/>
			</div>

			{/* Event Content */}
			<div className="py-4 sm:py-6 px-3 sm:px-4">
				{/* Title */}
				<h3 className="text-lg sm:text-[24px] sm:leading-[32px] font-semibold text-neutral-600 mb-1 sm:mb-2 line-clamp-2">
					{event.title}
				</h3>

				{/* Organizer */}
				<p className="text-neutral-500 text-sm sm:text-[18px] sm:leading-6 mb-2 sm:mb-5">
					{event.organizer}
				</p>

				{/* Date & Time */}
				<div className="flex items-center gap-2 mb-4 sm:mb-6">
					<Calendar className="w-4 h-4 text-neutral-500 " />
					<span className="text-neutral-600 text-sm sm:text-[18px] sm:leading-6">
						{event.date} • {event.time}
					</span>
				</div>

				{/* Price */}
				<div className="flex justify-end border-t border-neutral-100 pt-4 sm:pt-6">
					<span
						className={`text-success-500 font-bold text-sm sm:text-[18px] sm:leading-6`}>
						{event.price}
					</span>
				</div>
			</div>
		</div>
	);
};

export default function CorporateEvents() {
	return (
		<section className="w-full bg-white" >
			<div className="py-10 sm:py-[120px] bg-white w-full mx-auto max-w-[1440px]">
				<div className="w-full">
					{/* Section Header */}
					<div className="text-center mb-8 sm:mb-12">
						<h2 className="text-neutral-800 font-semibold text-2xl sm:text-[48px] sm:leading-[56px] mb-4 sm:mb-6">
							Corporate Events
						</h2>
						<p className="text-neutral-600 font-400 text-base leading-[26px] sm:text-[24px] sm:leading-[32px]">
							Register now to join our indepth growth sessions with the best
							Career <br className="hidden sm:block" /> experts
						</p>
					</div>

					{/* Carousel Container */}
					<div className="bg-[#F4F4F5] pl-2 sm:pl-[72px] py-4 sm:py-[41px] w-full">
						{/* Carousel Track */}
						<div className="overflow-x-auto scrollbar-hide scroll-smooth">
							<div className="flex gap-4 sm:gap-5">
								{eventData.map((event) => (
									<EventCard key={event.id} event={event} />
								))}
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
	);
}
