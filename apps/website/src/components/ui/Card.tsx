import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface CardProps {
  children: ReactNode;
  className?: string;
  variant?: 'default' | 'elevated' | 'outlined' | 'dark';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  hover?: boolean;
  onClick?: () => void;
}

interface CardHeaderProps {
  children: ReactNode;
  className?: string;
}

interface CardContentProps {
  children: ReactNode;
  className?: string;
}

interface CardFooterProps {
  children: ReactNode;
  className?: string;
}

const Card = ({ 
  children, 
  className, 
  variant = 'default',
  padding = 'md',
  hover = false,
  onClick 
}: CardProps) => {
  const baseStyles = "rounded-xl border transition-all duration-200";
  
  const variantStyles = {
    default: "bg-white border-brandGray-200 shadow-sm",
    elevated: "bg-white border-brandGray-200 shadow-lg",
    outlined: "bg-transparent border-brandGray-300",
    dark: "bg-brandGray-900 border-brandGray-700 text-white"
  };

  const paddingStyles = {
    none: "",
    sm: "p-4",
    md: "p-6",
    lg: "p-8",
    xl: "p-10"
  };

  const hoverStyles = hover ? "hover:shadow-lg hover:-translate-y-1 cursor-pointer" : "";

  return (
    <div 
      className={cn(
        baseStyles,
        variantStyles[variant],
        paddingStyles[padding],
        hoverStyles,
        className
      )}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

const CardHeader = ({ children, className }: CardHeaderProps) => (
  <div className={cn("mb-4", className)}>
    {children}
  </div>
);

const CardContent = ({ children, className }: CardContentProps) => (
  <div className={cn("", className)}>
    {children}
  </div>
);

const CardFooter = ({ children, className }: CardFooterProps) => (
  <div className={cn("mt-4 pt-4 border-t border-brandGray-200", className)}>
    {children}
  </div>
);

// Export compound component
Card.Header = CardHeader;
Card.Content = CardContent;
Card.Footer = CardFooter;

export default Card;
export { CardHeader, CardContent, CardFooter };
