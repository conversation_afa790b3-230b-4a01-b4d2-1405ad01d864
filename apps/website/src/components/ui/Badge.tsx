import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface BadgeProps {
  children: ReactNode;
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
  rounded?: boolean;
  className?: string;
  icon?: ReactNode;
}

const Badge = ({
  children,
  variant = 'default',
  size = 'md',
  rounded = false,
  className,
  icon
}: BadgeProps) => {
  const baseStyles = "inline-flex items-center font-medium transition-colors duration-200";

  const variantStyles = {
    default: "bg-brandGray-100 text-brandGray-800",
    primary: "bg-brand-100 text-brand-800",
    secondary: "bg-purple-100 text-purple-800",
    success: "bg-green-100 text-green-800",
    warning: "bg-yellow-100 text-yellow-800",
    error: "bg-red-100 text-red-800",
    info: "bg-blue-100 text-blue-800"
  };

  const sizeStyles = {
    sm: "px-2 py-0.5 text-xs",
    md: "px-2.5 py-1 text-sm",
    lg: "px-3 py-1.5 text-base"
  };

  const roundedStyles = rounded ? "rounded-full" : "rounded-md";

  return (
    <span className={cn(
      baseStyles,
      variantStyles[variant],
      sizeStyles[size],
      roundedStyles,
      className
    )}>
      {icon && (
        <span className="mr-1">
          {icon}
        </span>
      )}
      {children}
    </span>
  );
};

export default Badge;
