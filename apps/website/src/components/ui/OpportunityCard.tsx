"use client";

import { Bookmark } from "lucide-react";
import Image from "next/image";
import {
	ClockIcon,
	ShareIcon,
	SuitcaseIconNew,
	BudgetIcon,
	CalendarIcon,
	LocationIcon,
} from "@/components/common/icons";
import Button from "./Button";
import { cn } from "@/lib/utils";
import { ReactNode } from "react";
import { ButtonProps } from "./Button";

interface TagProps {
	label: string;
	bgColor?: string; // Tailwind class or CSS value
	borderColor?: string; // Tailwind class or CSS value
	textColor?: string; // Tailwind class or CSS value
}

interface OpportunityCardProps {
	id: string;
	title: string;
	company: string;
	companyLogo: string;
	location?: string;
	workType: string;
	timezone: string;
	tags: (string | TagProps)[];
	showTime?: boolean;
	time?: string;
	showBudget?: boolean;
	isFulltime?: boolean;
	isBookmarked?: boolean;
	btnText?: string;
	showDate?: boolean;
	date?: string;
	city?: string;
	isCourse?: boolean;
	courseCompany?: string;
	isShareIcon?: boolean;
	isBookmarkIcon?: boolean;
	budget?: string;
	rightIcon?: ReactNode;
	leftIcon?: ReactNode;
	btnSize?: ButtonProps["size"];
	onBookmark?: (id: string) => void;
	onShare?: (id: string) => void;
	onViewDetails?: (id: string) => void;
}

export default function OpportunityCard({
	id,
	title,
	company,
	companyLogo,
	location,
	workType,
	timezone,
	tags,
	isBookmarked = false,
	onBookmark,
	onShare,
	onViewDetails,
	showTime = true,
	time,
	showBudget = false,
	budget,
	isFulltime = true,
	showDate = false,
	date,
	city,
	courseCompany,
	isCourse = false,
	btnText = "View details",
	isShareIcon = true,
	isBookmarkIcon = true,
	rightIcon,
	leftIcon,
	btnSize = "sm",
}: OpportunityCardProps) {
	return (
		<div
			className="bg-white rounded-[12px] p-3 sm:p-5 transition-shadow duration-300 border border-[#E4E4E7] flex flex-col gap-4 sm:gap-6 w-full"
			// style={{ boxShadow: "0px 0px 16px 4px #DFDFDF40" }}
		>
			{/* Header with Tags and Actions */}
			<div className="flex items-center md:items-start justify-between mb-2 md:mb-4 flex-wrap gap-y-2">
				{/* Tags */}
				<div className="flex gap-2 flex-wrap">
					{tags.map((tag, index) => {
						if (typeof tag === "string") {
							// Old style: string tag, use default logic
							return (
								<span
									key={index}
									className={cn(
										tag.toLowerCase() === "new"
											? "text-[#F17171] border-[#FDE7E7] border bg-[#FEF6F6]"
											: "text-neutral-500 border-neutral-200 border bg-neutral-100",
										"px-2 sm:px-3 py-0.5 sm:py-1 text-xs sm:text-sm leading-[18px] sm:leading-[22px] rounded-full font-medium"
									)}>
									{tag}
								</span>
							);
						} else {
							// New style: object tag, use custom colors if provided
							return (
								<span
									key={index}
									className={cn(
										tag.textColor || "text-neutral-500",
										tag.borderColor || "border-neutral-200",
										tag.bgColor || "bg-neutral-100",
										"px-2 sm:px-3 py-0.5 sm:py-1 text-xs sm:text-sm leading-[18px] sm:leading-[22px] rounded-full font-medium border"
									)}>
									{tag.label}
								</span>
							);
						}
					})}
				</div>

				{/* Action Icons */}
				<div className="flex items-center gap-2 sm:gap-3">
					{isShareIcon && (
						<button
							onClick={() => onShare?.(id)}
							className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
							aria-label="Share opportunity">
							<ShareIcon className="w-4 h-4 md:w-auto md:h-auto" />
						</button>
					)}
					{isBookmarkIcon && (
						<>
							<div className="w-[1px] h-4 bg-[#E4E4E7]"></div>
							<button
								onClick={() => onBookmark?.(id)}
								className={`p-2 hover:bg-gray-100 rounded-lg transition-colors ${
									isBookmarked ? "text-primary-500" : "text-gray-500"
								}`}
								aria-label={
									isBookmarked ? "Remove bookmark" : "Bookmark opportunity"
								}>
								<Bookmark
									className={`w-4 h-4 md:w-auto md:h-auto text-neutral-400 ${
										isBookmarked ? "fill-current" : ""
									}`}
								/>
							</button>
						</>
					)}
				</div>
			</div>

			{/* Company Info */}
			<div className="flex items-center gap-2.5 sm:gap-3.5 mb-2 sm:mb-4">
				{isCourse ? (
					<div className="w-10 h-10 sm:w-[74px] sm:h-[74px] relative rounded-[4px] overflow-hidden bg-white border flex items-center justify-center">
						<Image
							src={companyLogo}
							alt={`${company} logo`}
							fill
							className="object-cover"
						/>
					</div>
				) : (
					<div className="w-10 h-10 sm:w-12 sm:h-12 relative rounded-[4px] overflow-hidden bg-white border border-neutral-200 flex items-center justify-center">
						<Image
							src={companyLogo}
							alt={`${company} logo`}
							fill
							className="object-cover"
						/>
					</div>
				)}
				<div>
					<h3 className="font-semibold text-neutral-800 text-base sm:text-lg leading-5 sm:leading-6">
						{title}
					</h3>
					{isCourse && courseCompany && (
						<p className="text-[#3F3F46] sm:text-sm leading-[18px] sm:leading-[22px] font-medium">
							{courseCompany}
						</p>
					)}
					{!isCourse && (
						<p className="text-neutral-500 text-xs sm:text-sm leading-[18px] sm:leading-[22px] font-medium">
							{company}
						</p>
					)}
				</div>
			</div>

			<div className="flex flex-col md:flex-row justify-between md:items-center md:gap-2 sm:gap-0">
				{/* Job Details */}
				{isCourse ? (
					<div className="flex items-center gap-2 text-xs md:text-sm text-neutral-500 font-500">
						{city && (
							<div className="flex items-center gap-1">
								<LocationIcon color="#A1A1AA" />
								<span className="text-[#71717A] font-medium text-xs md:text-sm leading-[22px]">
									{city}
								</span>
							</div>
						)}

						{isFulltime && (
							<div className="flex items-center gap-1">
								<SuitcaseIconNew />
								<span className="text-[#71717A] font-medium text-xs md:text-sm leading-[22px]">
									{workType}
								</span>
							</div>
						)}
					</div>
				) : (
					<div className="flex items-center flex-wrap gap-2 text-xs md:text-sm text-neutral-500 font-500">
						{location && (
							<div className="flex items-center gap-1">
								<Image
									src="/nigeria-flag.png"
									alt="country flag"
									width={24}
									height={18}
									className="rounded-[4px]"
								/>

								<span className="text-[#71717A] font-medium text-xs md:text-sm leading-[22px]">
									{location}
								</span>
							</div>
						)}
						{isFulltime && (
							<div className="flex items-center gap-1">
								<SuitcaseIconNew />
								<span className="text-[#71717A] font-medium text-xs md:text-sm leading-[22px]">
									{workType}
								</span>
							</div>
						)}
						{showTime && (
							<div className="flex items-center gap-1">
								<ClockIcon />
								<span className="text-[#71717A] font-medium text-xs md:text-sm leading-[22px]">
									{time ?? timezone}
								</span>
							</div>
						)}
						{showBudget && (
							<div className="flex items-center gap-1">
								<BudgetIcon />
								<span className="text-[#71717A] font-medium text-xs md:text-sm leading-[22px]">
									{budget ?? "$500k - $1M"}
								</span>
							</div>
						)}
						{showDate && (
							<div className="flex items-center gap-1">
								<CalendarIcon color="#A1A1AA" />
								<span className="text-[#71717A] font-medium text-xs md:text-sm leading-[22px]">
									{date}
								</span>
							</div>
						)}

						{city && (
							<div className="flex items-center gap-1">
								<LocationIcon color="#A1A1AA" />
								<span className="text-[#71717A] font-medium text-xs md:text-sm leading-[22px]">
									{city}
								</span>
							</div>
						)}
					</div>
				)}

				{/* View Details Button */}
				<Button
					onClick={() => onViewDetails?.(id)}
					variant="outline"
					size={btnSize}
					rightIcon={rightIcon}
					leftIcon={leftIcon}
					className="w-full md:w-auto mt-2 md:mt-0 max-w-fit hidden md:block">
					{btnText}
				</Button>
				<Button
					onClick={() => onViewDetails?.(id)}
					variant="outline"
					size="xs"
					rightIcon={rightIcon}
					leftIcon={leftIcon}
					className="w-full md:w-auto mt-3 md:mt-0 max-w-fit md:hidden">
					{btnText}
				</Button>
			</div>
		</div>
	);
}
