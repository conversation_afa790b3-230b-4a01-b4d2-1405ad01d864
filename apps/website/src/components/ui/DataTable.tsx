import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface Column<T> {
  key: keyof T;
  header: string;
  render?: (value: unknown, row: T) => ReactNode;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
}

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  className?: string;
  striped?: boolean;
  hoverable?: boolean;
  compact?: boolean;
  loading?: boolean;
  emptyMessage?: string;
  onRowClick?: (row: T) => void;
}

const DataTable = <T extends Record<string, unknown>>({
  data,
  columns,
  className,
  striped = false,
  hoverable = true,
  compact = false,
  loading = false,
  emptyMessage = "No data available",
  onRowClick
}: DataTableProps<T>) => {
  const tableClasses = cn(
    "w-full border-collapse",
    className
  );

  const cellPadding = compact ? "px-3 py-2" : "px-6 py-4";

  if (loading) {
    return (
      <div className="w-full">
        <div className="animate-pulse">
          <div className="bg-brandGray-200 h-12 rounded mb-2"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="bg-brandGray-100 h-10 rounded mb-1"></div>
          ))}
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="w-full text-center py-12">
        <div className="text-brandGray-400 text-lg mb-2">📊</div>
        <p className="text-brandGray-500">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className="w-full overflow-x-auto">
      <table className={tableClasses}>
        <thead>
          <tr className="border-b border-brandGray-200 bg-brandGray-50">
            {columns.map((column) => (
              <th
                key={String(column.key)}
                className={cn(
                  cellPadding,
                  "text-left text-xs font-medium text-brandGray-500 uppercase tracking-wider",
                  column.align === 'center' && "text-center",
                  column.align === 'right' && "text-right"
                )}
                style={{ width: column.width }}
              >
                <div className="flex items-center">
                  {column.header}
                  {column.sortable && (
                    <svg className="w-4 h-4 ml-1 text-brandGray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M5 12a1 1 0 102 0V6.414l1.293 1.293a1 1 0 001.414-1.414l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L5 6.414V12zM15 8a1 1 0 10-2 0v5.586l-1.293-1.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L15 13.586V8z" />
                    </svg>
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-brandGray-200">
          {data.map((row, rowIndex) => (
            <tr
              key={rowIndex}
              className={cn(
                "transition-colors duration-150",
                striped && rowIndex % 2 === 1 && "bg-brandGray-50",
                hoverable && "hover:bg-brandGray-50",
                onRowClick && "cursor-pointer"
              )}
              onClick={() => onRowClick?.(row)}
            >
              {columns.map((column) => {
                const value = row[column.key];
                const displayValue = column.render ? column.render(value, row) : value;
                
                return (
                  <td
                    key={String(column.key)}
                    className={cn(
                      cellPadding,
                      "text-sm text-brandGray-900",
                      column.align === 'center' && "text-center",
                      column.align === 'right' && "text-right"
                    )}
                  >
                    {displayValue as ReactNode}
                  </td>
                );
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default DataTable;
