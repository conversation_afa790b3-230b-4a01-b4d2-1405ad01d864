import React from "react";

interface TabProps {
	tabs: string[];
	activeTab: string;
	onTabChange: (tab: string) => void;
	className?: string;
}

const Tab: React.FC<TabProps> = ({
	tabs,
	activeTab,
	onTabChange,
	className,
}) => {
	return (
		<nav
			className={`w-full border-b border-t border-[#E4E4E7] overflow-x-auto bg-white${
				className ? ` ${className}` : ""
			}`.replace(
				/\b(bg-white|border-b|border-t|border-\[#E4E4E7\]|w-full|overflow-x-auto)\b(?=.*\b(bg-\[#[0-9a-fA-F]{6}\]|border-none|w-fit)\b)/g,
				""
			)}>
			<ul className="flex  justify-center  px-2 py-2 whitespace-nowrap">
				{tabs.map((tab) => (
					<li key={tab}>
						<button
							className={`px-[24px] py-3 rounded-[8px] cursor-pointer font-medium text-sm leading-[22px] transition-all duration-150 ${
								activeTab === tab
									? "bg-white text-[#52525B]"
									: "bg-transparent text-[#71717A] hover:text-neutral-700"
							}`}
							style={
								activeTab === tab
									? { boxShadow: "1px 2px 4px 0px #d1d5db" }
									: {}
							}
							onClick={() => onTabChange(tab)}>
							{tab}
						</button>
					</li>
				))}
			</ul>
		</nav>
	);
};

export default Tab;
