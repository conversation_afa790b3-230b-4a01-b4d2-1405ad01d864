import { cn } from "@/lib/utils";
import { ReactNode, ButtonHTMLAttributes } from "react";

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
	children: ReactNode;
	variant?:
		| "primary"
		| "secondary"
		| "outline"
		| "ghost"
		| "danger"
		| "success";
	size?: "xs" | "sm" | "md" | "lg" | "xl";
	fullWidth?: boolean;
	loading?: boolean;
	leftIcon?: ReactNode;
	rightIcon?: ReactNode;
	/**
	 * If true, applies a custom double box-shadow to the button.
	 */
	shadow?: boolean;
}

const Button = ({
	children,
	className,
	variant = "primary",
	size = "md",
	fullWidth = false,
	loading = false,
	leftIcon,
	rightIcon,
	disabled,
	shadow = false,
	...props
}: ButtonProps) => {
	const baseStyles =
		"cursor-pointer inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed";

	const variantStyles = {
		primary:
			"bg-brand-500 text-white hover:bg-brand-600 focus:ring-brand-500 border border-brand-500",
		secondary:
			"bg-white text-brandGray-900 hover:bg-brandGray-50 focus:ring-brandGray-500 border border-brandGray-300",
		outline:
			"bg-transparent text-brand-500 hover:bg-brand-50 focus:ring-brand-500 border border-brand-500",
		ghost:
			"bg-transparent text-brandGray-700 hover:bg-brandGray-100 focus:ring-brandGray-500",
		danger:
			"bg-error-500 text-white hover:bg-error-600 focus:ring-error-500 border border-error-500",
		success:
			"bg-success-500 text-white hover:bg-success-600 focus:ring-success-500 border border-success-500",
	};

	const sizeStyles = {
		xs: "px-2.5 py-1.5 text-xs rounded-md",
		sm: "px-3 py-2 text-sm rounded-lg",
		md: "px-4 py-2.5 text-sm rounded-lg",
		lg: "px-6 py-3 text-base rounded-lg font-semibold leading-[26px]",
		xl: "px-8 py-4 text-lg rounded-xl",
	};

	const widthStyles = fullWidth ? "w-full" : "";

	return (
		<button
			className={cn(
				baseStyles,
				variantStyles[variant],
				sizeStyles[size],
				widthStyles,
				className
			)}
			style={
				shadow
					? { boxShadow: "0px 0px 0px 4px #476CFF1A, 0px 0px 0px 2px #FFFFFF" }
					: undefined
			}
			disabled={disabled || loading}
			{...props}>
			{loading && (
				<svg
					className="animate-spin -ml-1 mr-2 h-4 w-4"
					xmlns="http://www.w3.org/2000/svg"
					fill="none"
					viewBox="0 0 24 24">
					<circle
						className="opacity-25"
						cx="12"
						cy="12"
						r="10"
						stroke="currentColor"
						strokeWidth="4"
					/>
					<path
						className="opacity-75"
						fill="currentColor"
						d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
					/>
				</svg>
			)}

			{!loading && leftIcon && <span className="mr-2">{leftIcon}</span>}

			{children}

			{!loading && rightIcon && <span className="ml-2">{rightIcon}</span>}
		</button>
	);
};

export default Button;
