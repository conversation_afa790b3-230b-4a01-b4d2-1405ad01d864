export const opportunitiesData = [
	{
		id: "1",
		title: "IT Infrastructure Upgrade for Regional offices",
		company: "Revnet",
		subtitle:
			"Seeking proposals for upgrading IT infrastructure across 5 regional offices to improve network security and performance.",
		companyLogo: "/rn-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["IT Services", "Request for Proposal (RFP)"],
		description:
			"Our organization is looking to enhance its IT infrastructure to meet growing operational demands and strengthen cybersecurity. The project includes network upgrades, server replacement, and implementation of modern security protocols across 5 regional offices located in major cities. Vendors are expected to provide comprehensive solutions including hardware, software, installation, and training.",
		isBookmarked: false,
		showTime: false,
		showBudget: false,
		isFulltime: false,
		deadline: "Deadline: Jun 20, 2025",
		deadlineCounter: "68",
		showDate: true,
		date: "Posted: Apr 2, 2025",
	},
	{
		id: "2",
		title: "Tender for Construction of School Buildings",
		company: "Department of Education",
		companyLogo: "/rn-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Construction", "Tender"],
		isBookmarked: false,
		showTime: false,
		showBudget: true,
		isFulltime: false,
	},
	{
		id: "3",
		title: "Improving Healthcare Delivery in Remote Regions",
		company: "Ministry of Health",
		companyLogo: "/rn-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Healthcare", "Expression of Interest (EOI)"],
		isBookmarked: false,
		showTime: false,
		showBudget: true,
		isFulltime: false,
	},
	{
		id: "4",
		title: "Tender for Construction of School Buildings",
		company: "Department of Education",
		companyLogo: "/rn-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Construction", "Tender"],
		isBookmarked: false,
		showTime: false,
		showBudget: true,
		isFulltime: false,
	},
];


interface IJob {
	id: string;
	title: string;
	company: string;
	companyLogo: string;
	location: string;
	workType: string;
	timezone: string;
	tags: string[];
	isBookmarked: boolean;
	showTime: boolean;
	// city: string;
	// isFulltime: boolean,
	// time: string;

}


export const jobs: IJob[] = [
	{
		id: "1",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
		// city: "San Fransisco, CA",
		// isFulltime: true,
		// time: "remote",
		

	},
	{
		id: "2",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
	},
	{
		id: "3",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
	},
	{
		id: "4",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
	},
	{
		id: "5",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
	},
	{
		id: "6",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
	},
	{
		id: "7",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
	},
	{
		id: "8",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
	},
	// ...repeat or map for demo
];
