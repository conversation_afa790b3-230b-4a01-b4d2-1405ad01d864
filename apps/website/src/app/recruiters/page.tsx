import {
	
	BuildingIcon,
	CalculatorIcon,
	CaretIcon,
	HandshakeIcon,
	LightningIcon,
	MediaIcon,
	OfficeIcon,
	PiggyIcon,
} from "@/components/common/icons";
import Footer from "@/components/layout/Footer";
import Header from "@/components/layout/Header";
import Button from "@/components/ui/Button";
import Image from "next/image";
import { ReactNode } from "react";

interface CategoryCardProps {
	icon: ReactNode;
	title?: string;
	subtitle?: string;
}

const executiveSearchTerms = [
	{
		id: "1",
		icon: <CalculatorIcon />,
		subtitle: "Projects and Infrastructure development recruitment",
	},
	{
		id: "2",
		icon: <OfficeIcon color="#5C7DFF" />,
		subtitle: "Real estate development and construction",
	},
	{
		id: "3",
		icon: <MediaIcon />,
		subtitle: "Telecommunications, Media, Technology",
	},
	{
		id: "4",
		icon: <PiggyIcon color="#5C7DFF" />,
		subtitle: "Not for Profits and Foundations",
	},
	{
		id: "5",
		icon: <HandshakeIcon />,
		subtitle:
			"African development financial institutions and Development Agencies",
	},
	{
		id: "6",
		icon: <BuildingIcon color="#5C7DFF" />,
		subtitle:
			"Financial institutions (investment banking, commercial banking, private equity, venture capital, private equity, pension funds).",
	},
];
// const recruitmentTerms = [
// 	{
// 		id: "1",
// 		icon: <CalculatorIcon />,
// 		subtitle: "African development financial institutions",
// 	},
// 	{
// 		id: "2",
// 		icon: <OfficeIcon color="#5C7DFF" />,
// 		subtitle: "Multinational and international companies across Africa",
// 	},
// 	{
// 		id: "3",
// 		icon: <MediaIcon />,
// 		subtitle: "Africa Law Firms",
// 	},
// 	{
// 		id: "4",
// 		icon: <PiggyIcon color="#5C7DFF" />,
// 		subtitle:
// 			"The Projects, Infrastructure, Real estate development and construction industry",
// 	},
// ];

const jobRoles = [
	{
		icon: <LightningIcon className="w-5 h-5" />,
		subtitle: "Interim Contract Management",
	},
	{
		icon: <LightningIcon className="w-5 h-5" />,
		subtitle: "Law Firms",
	},
	{
		icon: <LightningIcon className="w-5 h-5" />,
		subtitle: "In house legal counsel",
	},
	{
		icon: <LightningIcon className="w-5 h-5" />,
		subtitle:
			"Law Firm Management roles such as Chief Operating Officers, Business Development Manager, Practice Managers.  ",
	},
];

const RecuitersPills = ({ icon, subtitle }: CategoryCardProps) => (
	<div className="flex items-center gap-2 p-2 rounded-full border border-[#DCC0E8] w-fit">
		<div className="w-4 h-4 md:w-5 md:h-5">{icon}</div>
		<p className="font-regular text-[12px] leading-[16px] md:text-[18px] md:leading-[24px] text-[#52525B]">
			{subtitle}
		</p>
	</div>
);

const CategoryCard = ({ icon, title, subtitle }: CategoryCardProps) => (
	<div className="bg-white px-3 py-4 md:py-[32px] md:px-5 rounded-2xl flex items-center gap-4 border-[0.75] border-[#E4E4E7]">
		<div className="border-[0.3px] border-[#C0CCFF] bg-[#EBEFFF] rounded-full p-2 w-[31px] h-[31px] md:w-auto md:h-auto md:p-[14px] flex items-center justify-center">
			{icon}
		</div>

		<div>
			<h3 className="font-semibold text-[18px] leading-[24px] text-[#27272A] mb-1">
				{title}
			</h3>
			<p className="md:font-semibold font-regular whitespace-normal text-[12px] leading-[16px] md:text-[14px] md:leading-[22px] text-[#71717A] ">
				{subtitle}
			</p>
		</div>
	</div>
);

export default function RecruitersPage() {
	return (
		<main className="w-full">
			<Header />
			<section className="relative h-fit bg-white overflow-hidden px-4 py-8 md:py-[72px] md:grid grid-cols-2 ">
				{/* Content */}
				<div className=" md:pl-[56px] md:pb-[80px] md:pt-[72px] ">
					<div className="flex flex-col gap-3 md:gap-5 items-start">
						<div className="p-2 flex items-center sm:pl-2 sm:pr-8 sm:py-2 space-x-2 rounded-4xl border border-brandGray-200  ">
							<div className="flex -space-x-2">
								<div className="w-5 h-5 md:w-7 md:h-7 rounded-full  border-2 border-white">
									<Image
										src="/trusted-img-1.jpg"
										alt="avatar image"
										width={28}
										height={28}
										className="rounded-full "
									/>
								</div>
								<div className="w-5 h-5 md:w-7 md:h-7 rounded-full  border-2 border-white">
									<Image
										src="/trusted-img-2.jpg"
										alt="avatar image"
										width={28}
										height={28}
										className="rounded-full "
									/>
								</div>
								<div className="w-5 h-5 md:w-7 md:h-7 rounded-full  border-2 border-white">
									<Image
										src="/trusted-img-3.jpg"
										alt="avatar image"
										width={28}
										height={28}
										className="rounded-full "
									/>
								</div>
								<div className="w-5 h-5 md:w-7 md:h-7 rounded-full  border-2 border-white">
									<Image
										src="/trusted-img-4.jpg"
										alt="avatar image"
										width={28}
										height={28}
										className="rounded-full "
									/>
								</div>
							</div>
							<span className="text-brandGray-500 text-[12px] md:text-sm font-medium">
								Trusted by professionals across Africa.
							</span>
						</div>

						{/* Trust Indicators */}
						<h1 className="text-[36px] leading-[44px] md:text-7xl font-semibold md:font-medium text-brandGray-800 md:leading-[110%] flex flex-col">
							Africaskillz Recruiter Portal
						</h1>
						<p className="text-base md:text-2xl text-[#A1A1AA] font-semibold leading-8">
							The premium site for Recruiters in Africa
						</p>
					</div>

					<div className="md:hidden md:my-10 my-8">
						<Button size="sm" variant="primary" rightIcon={<CaretIcon />}>
							Continue as Recruiter
						</Button>
					</div>

					{/* logo area */}
					<div className="mt-2">
						<p className="text-[14px] leading-[22px] md:text-base md:leading-[26px] text-[#52525B] font-semibold mb-4">
							Supported development agencies in Africa
						</p>
						<div className="relative w-full md:w-[50%] overflow-hidden rounded-lg">
							{/* Animated logo container - true infinite scroll */}
							<div className="flex items-center animate-infinite-scroll">
								{/* First complete set */}
								<div className="flex items-center space-x-6 flex-shrink-0">
									<Image
										src="/brand1.png"
										alt="brand Logo"
										width={31}
										height={31}
										className="w-[31px] h-[31px] md:w-[51px] md:h-[51px] opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
									<Image
										src="/brand2.png"
										alt="brand Logo"
										width={31}
										height={31}
										className="w-[31px] h-[31px] md:w-[51px] md:h-[51px] opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
									<Image
										src="/brand3.png"
										alt="brand Logo"
										width={31}
										height={31}
										className="w-[31px] h-[31px] md:w-[51px] md:h-[51px] opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
									<Image
										src="/brand4.png"
										alt="brand Logo"
										width={31}
										height={31}
										className="w-[31px] h-[31px] md:w-[51px] md:h-[51px] opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
									<Image
										src="/brand5.png"
										alt="brand Logo"
										width={31}
										height={31}
										className="w-[31px] h-[31px] md:w-[51px] md:h-[51px] opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
								</div>

								{/* Spacer */}
								<div className="w-6 flex-shrink-0"></div>

								{/* Duplicate set for seamless loop */}
								<div className="flex items-center space-x-6 flex-shrink-0">
									<Image
										src="/brand1.png"
										alt="brand Logo"
										width={51}
										height={51}
										className="opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
									<Image
										src="/brand2.png"
										alt="brand Logo"
										width={51}
										height={51}
										className="opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
									<Image
										src="/brand3.png"
										alt="brand Logo"
										width={51}
										height={51}
										className="opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
									<Image
										src="/brand4.png"
										alt="brand Logo"
										width={51}
										height={51}
										className="opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
									<Image
										src="/brand5.png"
										alt="brand Logo"
										width={51}
										height={51}
										className="opacity-70 hover:opacity-100 transition-opacity duration-300"
									/>
								</div>
							</div>

							{/* Left fade gradient - extended to cover edge completely */}
							<div className="absolute -left-1 top-0 w-24 h-full bg-gradient-to-r from-white via-white/70 to-transparent pointer-events-none z-10"></div>

							{/* Right fade gradient - extended to cover edge completely */}
							<div className="absolute -right-1 top-0 w-24 h-full bg-gradient-to-l from-white via-white/70 to-transparent pointer-events-none z-10"></div>
						</div>
					</div>
				</div>

				<div className=" justify-start md:h-[510px] h-[159px] max-w-[619px] md:max-w-none w-full relative hidden md:flex">
					<div
						className="absolute top-0 left-0 w-full h-full rounded-4xl rounded-6 p-8 overflow-hidden"
						style={{
							backgroundImage: "url(/recruiters-hero-img.png)",
							backgroundSize: "cover",
							backgroundPosition: "center",
							backgroundRepeat: "no-repeat",
						}}></div>
				</div>
			</section>

			<section className="w-full px-4 py-12 md:px-[84px] md:py-[120px] flex flex-col md:flex-row  gap-[31px] md:gap-[34px] bg-[#F4F4F5] ">
				<div>
					<div>
						<h2 className="font-semibold text-[24px] leading-[32px] md:text-[48px] md:leading-[56px] text-[#3F3F46]">
							Africaskillz Executive Search
						</h2>
						<p className="font-regular text-base md:text-[24px] md:leading-[32px] text-[#52525B] mt-3 md:mt-6 max-w-[618px]">
							Africaskillz Executive Search excels in recruiting top executive
							talent across Africa, helping companies find the right leaders to
							drive their business strategies.
						</p>
					</div>

					<div className="rounded-2xl w-full md:w-[618px] relative h-[270px] md:d:h-[337px] mt-6 md:mt-[69px] ">
						<Image
							src="/africaskillz-executive-img.png"
							fill
							alt="executive image"
							className="rounded-2xl object-cover"
						/>

						<p className="font-regular text-[18px] leading-[24px] text-[#FAFAFA] absolute bottom-4  md:bottom-10 mx-4 z-20">
							We source high-caliber candidates fluent in anglophone,
							francophone, and lusophone languages to meet the diverse needs of
							businesses across the continent.
						</p>
						<div className="h-full w-full bg-gradient-to-t rounded-2xl absolute from-black via-black to-transparent"></div>
					</div>
				</div>

				<div className="flex flex-row overflow-x-auto md:overflow-visible md:grid grid-cols-2 gap-x-5 md:gap-y-[42px]">
					{executiveSearchTerms.map((term, index) => (
						<div
							className="min-w-[80vw] max-w-xs flex-shrink-0 md:min-w-0 md:max-w-none"
							key={index}>
							<CategoryCard {...term} />
						</div>
					))}
				</div>
			</section>

			<section className="bg-white px-2 py-12 md:px-[84px] md:py-[120px] flex flex-col md:flex-row gap-6 md:gap-[34px] items-start ">
				<div className="max-w-[618px] px-2">
					<h2 className="font-semibold text-[24px] leading-[32px] md:text-[48px] md:leading-[56px] text-[#27272A]">
						Africaskillz Engineering and Technical Recruitment
					</h2>
					<p className="font-regular text-base md:text-[24px] md:leading-[32px] text-[#52525B] mt-6 max-w-[618px]">
						The Africaskillz team can assist with the hiring of engineers across
						different disciplines and other technical staff to work on projects
						and infrastructure transactions across Africa.
					</p>
				</div>

				<div className="rounded-2xl w-[377px] md:w-[618px] relative h-[379px] md:h-[582px]  ">
					<Image
						src="/africaskillz-engineering-img.png"
						fill
						alt="engineering image"
						className="rounded-2xl object-cover"
					/>

					<p className="font-regular text-[18px] leading-[24px] text-[#FAFAFA] absolute bottom-10 mx-4 z-20">
						We have a good track record of sourcing engineering and technical
						personnel for projects across Africa in the oil and gas and civil
						infrastructure industry in Africa. Where there is a project in
						Africa, we are available to assist with the sourcing of staff on
						projects across Africa. 
					</p>
					<div className="h-full w-full bg-gradient-to-t rounded-2xl absolute from-black via-transparent to-transparent"></div>
				</div>
			</section>

			<section className="bg-white px-2 py-12 md:px-[84px] md:py-[120px] flex flex-col md:flex-row gap-6 md:gap-[34px]">
				<div className="max-w-[618px] px-2">
					<div className=" ">
						<h2 className="font-semibold text-[24px] leading-[32px] md:text-[48px] md:leading-[56px] text-[#27272A]">
							Africaskillz Legal Recruitment
						</h2>
						<p className="font-regular text-base md:text-[24px] md:leading-[32px] text-[#52525B] mt-6 max-w-[618px]">
							We specialize in sourcing top-tier legal talent across Africa,
							with a strong track record in transactional law for project
							finance and infrastructure development. We have executed legal
							recruitment mandates in the following industries:
						</p>
					</div>

					<div className="flex flex-row overflow-x-auto md:overflow-visible md:grid grid-cols-2 gap-x-5 md:gap-y-[42px] mt-6">
						{executiveSearchTerms.map((term, index) => (
							<div
								className="min-w-[80vw] max-w-xs flex-shrink-0 md:min-w-0 md:max-w-none"
								key={index}>
								<CategoryCard {...term} />
							</div>
						))}
					</div>
				</div>

				<div className="p-4 md:px-[27px] md:py-[42px] bg-[#F4F4F5} flex flex-col gap-6  md:gap-[32px] border border-[#E4E4E7] rounded-[12px] bg-[#F4F4F5]">
					<p className="font-semibold text-[20px] leading-[26px] md:font-bold md:text-[24px] lmd:eading-[32px] text-[#3F3F46]">
						We recruit for the following roles
					</p>

					<div className="flex flex-col gap-2.5">
						{/* Render the first pill (index 0) on its own line */}
						{jobRoles[0] && <RecuitersPills {...jobRoles[0]} key={0} />}
						{/* Render pills with indices 1 and 2 side by side */}
						<div className="flex items-center gap-2.5">
							{jobRoles[1] && <RecuitersPills {...jobRoles[1]} key={1} />}
							{jobRoles[2] && <RecuitersPills {...jobRoles[2]} key={2} />}
						</div>
						{/* Render the rest of the pills (index 3 and above) each on their own line */}
						{jobRoles.slice(3).map((job, index) => (
							<RecuitersPills {...job} key={index + 3} />
						))}
					</div>

					<p className="font-regular text-[18px] leading-[24px] text-[#52525B] max-w-[618px] mt-[31px] md:mt-auto">
						We source high-caliber candidates fluent in anglophone, francophone,
						and lusophone languages to meet the diverse needs of businesses
						across the continent.
					</p>
				</div>
			</section>

			<Footer />
		</main>
	);
}
