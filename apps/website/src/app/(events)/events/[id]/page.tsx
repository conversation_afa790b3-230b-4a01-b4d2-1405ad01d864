"use client";

import {
	ArrowRightIcon,
	CalendarCheckIcon,
	ClockIcon,
	LocationIcon,
	OfficeIcon,
	ShareIcon,
	CaretIcon,
} from "@/components/common/icons";
import Footer from "@/components/layout/Footer";
import Header from "@/components/layout/Header";
import Button from "@/components/ui/Button";
import Link from "next/link";
import { useParams } from "next/navigation";
import Image from "next/image";
import { Calendar } from "lucide-react";
import { useState, useEffect, useRef } from "react";

// Data for corporate notices

interface EventCard {
	id: string;
	title: string;
	organizer: string;
	date: string;
	time: string;
	price: string;
	image: string;
	isFree: boolean;
}

const eventData: EventCard[] = [
	{
		id: "1",
		title:
			"The Lagos Network: Tech Startups, Professionals & Investors Connect Event.",
		organizer: "TechAcademy Africa",
		date: "Thur, May 8th",
		time: "2pm",
		price: "Free",
		image: "/corporate1.png",
		isFree: true,
	},
	{
		id: "2",
		title: "Digital Marketing Masterclass",
		organizer: "Marketing Hub Africa",
		date: "Fri, May 9th",
		time: "10am",
		price: "$56",
		image: "/corporate2.png",
		isFree: false,
	},
	{
		id: "3",
		title: "AI & Machine Learning Workshop",
		organizer: "Data Science Nigeria",
		date: "Sat, May 10th",
		time: "9am",
		price: "$96",
		image: "/corporate3.png",
		isFree: false,
	},
	{
		id: "4",
		title: "Fintech Innovation Summit",
		organizer: "Lagos Business School",
		date: "Mon, May 12th",
		time: "1pm",
		price: "Free",
		image: "/corporate1.png",
		isFree: true,
	},
	{
		id: "5",
		title: "UX/UI Design Bootcamp",
		organizer: "Design Academy",
		date: "Wed, May 14th",
		time: "11am",
		price: "$120",
		image: "/corporate2.png",
		isFree: false,
	},
	{
		id: "6",
		title: "Digital Marketing Masterclass",
		organizer: "Marketing Hub Africa",
		date: "Fri, May 9th",
		time: "10am",
		price: "$56",
		image: "/corporate2.png",
		isFree: false,
	},
	{
		id: "7",
		title: "AI & Machine Learning Workshop",
		organizer: "Data Science Nigeria",
		date: "Sat, May 10th",
		time: "9am",
		price: "$96",
		image: "/corporate3.png",
		isFree: false,
	},
	{
		id: "8",
		title: "Fintech Innovation Summit",
		organizer: "Lagos Business School",
		date: "Mon, May 12th",
		time: "1pm",
		price: "Free",
		image: "/corporate1.png",
		isFree: true,
	},
	{
		id: "9",
		title: "UX/UI Design Bootcamp",
		organizer: "Design Academy",
		date: "Wed, May 14th",
		time: "11am",
		price: "$120",
		image: "/corporate2.png",
		isFree: false,
	},
];

const EventCard = ({
	event,
	handleEventClick,
}: {
	event: EventCard;
	handleEventClick: (id: string) => void;
}) => {
	return (
		<div
			className="bg-white rounded-2xl overflow-hidden min-w-[85vw] sm:min-w-[320px] cursor-pointer transition-shadow duration-300"
			style={{ boxShadow: "0px 0px 16px 4px #DFDFDF40" }}
			onClick={() => handleEventClick(event.id)}>
			{/* Event Image */}
			<div className="relative h-32 overflow-hidden hover:scale-110 transition-transform duration-300 ease-in-out">
				<Image
					src={event.image}
					alt={event.title}
					className="w-full h-full object-cover"
					width={320}
					height={128}
				/>
			</div>

			{/* Event Content */}
			<div className="py-4 sm:py-6 px-3 sm:px-4">
				{/* Title */}
				<h3 className="text-lg sm:text-[24px] sm:leading-[32px] font-semibold text-neutral-600 mb-1 sm:mb-2 line-clamp-2">
					{event.title}
				</h3>

				{/* Organizer */}
				<p className="text-neutral-500 text-sm sm:text-[18px] sm:leading-6 mb-2 sm:mb-5">
					{event.organizer}
				</p>

				{/* Date & Time */}
				<div className="flex items-center gap-2 mb-4 sm:mb-6">
					<Calendar className="w-4 h-4 text-neutral-500 " />
					<span className="text-neutral-600 text-sm sm:text-[18px] sm:leading-6">
						{event.date} • {event.time}
					</span>
				</div>

				{/* Price */}
				<div className="flex justify-end border-t border-neutral-100 pt-4 sm:pt-6">
					<span
						className={`text-success-500 font-bold text-sm sm:text-[18px] sm:leading-6`}>
						{event.price}
					</span>
				</div>
			</div>
		</div>
	);
};

// Mobile Events Carousel Component
interface MobileEventsCarouselProps {
	// events: EventCard[];
	// onViewMore: () => void;
	// onEventClick: (id: string) => void;
	children: React.ReactNode[];
	title: string;
	description: string;
	onViewMore: () => void;
}

function MobileEventsCarousel({
	children,
	title,
	description,
	onViewMore,
}: MobileEventsCarouselProps) {
	const [currentIndex, setCurrentIndex] = useState(0);
	const [cardWidth, setCardWidth] = useState(0);
	const cardRef = useRef<HTMLDivElement>(null);
	const GAP = 16; // px gap between cards

	// Measure card width on mount and when window resizes
	useEffect(() => {
		function updateCardWidth() {
			if (cardRef.current) {
				setCardWidth(cardRef.current.offsetWidth + GAP);
			}
		}
		updateCardWidth();
		window.addEventListener("resize", updateCardWidth);
		return () => window.removeEventListener("resize", updateCardWidth);
	}, []);

	const nextSlide = () => {
		setCurrentIndex((prev) => (prev + 1) % children.length);
	};

	const prevSlide = () => {
		setCurrentIndex((prev) => (prev - 1 + children.length) % children.length);
	};

	return (
		<section className="md:hidden bg-[#F4F4F5]">
			<div className="px-4 py-8">
				<div className="flex items-center justify-between mb-6">
					<h2 className="text-[24px] leading-[32px] font-semibold text-[#18181B]">
						{title}
					</h2>
					<Button onClick={onViewMore} variant="primary" size="sm">
						View all
					</Button>
				</div>

				{/* Carousel Container */}
				<div className="bg-[#F4F4F5]">
					{/* Carousel Track */}
					<div className="overflow-hidden">
						<div
							className="flex gap-4 transition-transform duration-500 ease-in-out py-[22px]"
							style={{
								transform: `translateX(-${currentIndex * cardWidth}px)`,
								width: cardWidth ? `${children.length * cardWidth}px` : "auto",
							}}>
							{children.map((child, idx) => (
								<div
									key={idx}
									ref={idx === 0 ? cardRef : null}
									className="w-[280px] flex-shrink-0 shadow-md rounded-[12px]">
									{child}
								</div>
							))}
						</div>
					</div>

					{/* Navigation Buttons */}
					<div className="flex gap-2 pb-6">
						<button
							onClick={prevSlide}
							className="w-10 h-10 rounded-full border border-[#52525B] bg-white flex items-center justify-center hover:bg-gray-50 transition-colors">
							<ArrowRightIcon className="rotate-180 w-5 h-5" color="#52525B" />
						</button>
						<button
							onClick={nextSlide}
							className="w-10 h-10 rounded-full border border-[#52525B] bg-white flex items-center justify-center hover:bg-gray-50 transition-colors">
							<ArrowRightIcon className="w-5 h-5" color="#52525B" />
						</button>
					</div>
				</div>
			</div>
		</section>
	);
}

export default function EventsDetailsPage() {
	const params = useParams();
	const { id } = params;

	const event = eventData.find((event) => event.id === id);

	console.log(event);

	const handleViewMore = () => {
		console.log("Share opportunity:", id);
		// Implement share functionality
	};

	const handleEventClick = (id: string) => {
		console.log(id);
	};

	if (!event) {
		return (
			<div className="min-h-screen flex flex-col items-center justify-center">
				<h1 className="text-3xl font-bold mb-4">Course Not Found</h1>
				<p className="text-lg">
					No Event found for ID: <span className="font-mono">{id}</span>
				</p>
			</div>
		);
	}

	return (
		<main className="w-full">
			<Header />

			{/* Desktop Layout */}
			<section className="hidden md:block w-full bg-[#FAFAFA]">
				<div className="max-w-[1440px] mx-auto py-8 px-[90px]">
					<Link
						href="/events"
						className="flex items-center gap-2 text-sm font-medium leading-[22px] text-[#52525B] mb-[32px] hover:text-brand-500 transition-all duration-300 ">
						<ArrowRightIcon
							className="rotate-180 w-6 h-6 hover:text-brand-500"
							color="#52525B"
						/>{" "}
						Back to Events
					</Link>

					<section className="w-full h-full mt-8">
						<div className="grid grid-cols-[68%_30%] gap-[42px] mt-12">
							<div className="flex flex-col gap-5">
								<div>
									<h2 className="text-[48px] leading-[64px] font-regular text-[#18181B] mt-4 mb-5">
										{event.title}
									</h2>
								</div>

								<div className="w-full rounded-[12px] relative h-[294px]">
									<Image
										src={event.image}
										fill
										alt="event image"
										className="object-cover rounded-[12px] "
									/>
								</div>

								{/* white cards section */}
								<div className="grid grid-cols-3 gap-y-6 p-5 mt-6 bg-white mb-5">
									{/* details cards */}
									<div className="flex items-start gap-2 ">
										<div className="p-2 rounded-full border border-[#E4E4E7]">
											<CalendarCheckIcon />
										</div>
										<div className="">
											<p className="text-sm leading-[22px] font-medium text-[#18181B]">
												Event Date
											</p>
											<p className="text-sm leading-[22px] font-medium text-[#71717A]">
												Jan 30, 2025
											</p>
										</div>
									</div>
									<div className="flex items-start gap-2 ">
										<div className="p-2 rounded-full border border-[#E4E4E7]">
											<LocationIcon />
										</div>
										<div className="">
											<p className="text-sm leading-[22px] font-medium text-[#18181B]">
												Location
											</p>
											<p className="text-sm leading-[22px] font-medium text-[#71717A]">
												Lagos, Nigeria
											</p>
										</div>
									</div>
									<div className="flex items-start gap-2 ">
										<div className="p-2 rounded-full border border-[#E4E4E7]">
											<ClockIcon />
										</div>
										<div className="">
											<p className="text-sm leading-[22px] font-medium text-[#18181B]">
												Time
											</p>
											<p className="text-sm leading-[22px] font-medium text-[#71717A]">
												2:30pm -5:00pm (WAT)
											</p>
										</div>
									</div>
								</div>

								<div>
									<p className="font-semibold text-[24px] leading-[32px] text-[#52525B] mb-[14px]">
										About the Event
									</p>
									<p className="font-regular text-[24px] leading-[32px] text-[#71717A]">
										The Lagos Network: Exclusive Tech Startups, Entrepreneurs &
										Professionals Networking Event <br />
										Join The London Network for a dynamic and exclusive event
										designed for tech enthusiasts, entrepreneurs, startup
										founders, and professionals who are shaping the future of
										technology. This is your opportunity to connect with
										industry leaders, exchange ideas, and build valuable
										relationships in a welcoming, no-pressure environment.
										🕒 Time: 6:00 PM – 9:00 PM
										<br />
										📍 Location: Gourmet Bar at Novotel London Bridge, located
										at 53-61 Southwark Bridge Rd, Lagos. A contemporary venue
										offering a relaxed atmosphere with modern decor, perfect for
										networking and vibrant conversations. 👔 Dress Code:
										Business smart casual.
									</p>
								</div>
							</div>

							<aside>
								<div className="px-6 py-8 rounded-[16px] border-[0.75px] border-[#E4E4E7] bg-white flex flex-col gap-[14px] h-fit">
									<div>
										<p className="text-sm leading-[22px] font-medium text-[#71717A] mb-3">
											School
										</p>
										<div className="flex items-center gap-2">
											<OfficeIcon />
											<span className="text-[#71717A] font-medium text-sm leading-[22px]">
												TechAcademy Africa
											</span>
										</div>
									</div>
								</div>

								<Button
									variant="primary"
									rightIcon={<CaretIcon color="white" />}
									className="mt-4 w-full">
									I am attending
								</Button>
								<Button
									variant="outline"
									leftIcon={<ShareIcon color="#335CFF" />}
									className="mt-4 w-full">
									Share Event
								</Button>
							</aside>
						</div>
					</section>
				</div>
			</section>

			{/* Desktop More in Series Section */}
			<section className="hidden md:block w-full bg-white">
				<div className="max-w-[1440px] mx-auto px-[90px] py-[120px]">
					{/* Section Header */}
					<div className="flex items-start justify-between mb-12">
						<div>
							<h2 className="text-neutral-800 font-regular text-[48px] leading-[56px] mb-6">
								More in this series
							</h2>
						</div>

						{/* View More Button */}
						<Button onClick={handleViewMore} variant="primary" size="md">
							View all
						</Button>
					</div>

					{/* Articles Grid */}
					<div className="grid grid-cols-3 gap-5">
						{eventData.slice(0, 3).map((event) => (
							<EventCard
								key={event.id}
								event={event}
								handleEventClick={handleEventClick}
							/>
						))}
					</div>
				</div>
			</section>

			{/* Mobile Layout */}
			{/* Mobile Back Navigation */}
			<section className="md:hidden bg-white p-4">
				<Link
					href="/events"
					className="flex items-center gap-2 text-sm font-medium leading-[22px] text-[#52525B] hover:text-brand-500 transition-all duration-300">
					<ArrowRightIcon className="rotate-180 w-5 h-5" color="#52525B" />
					Back to Events
				</Link>
			</section>

			<section className="px-4  bg-white md:hidden ">
				<div className="p-2 rounded-[8px] bg-[#FAFAFA] flex flex-col gap-4">
					{/* Event Title */}
					<h1 className="text-[20px] leading-[26px] font-regular text-[#18181B]">
						{event.title}
					</h1>
					{/* Organizer */}
					<div className="">
						<p className="text-sm leading-[20px] font-medium text-[#71717A] mb-2">
							Organizer
						</p>
						<div className="flex items-center gap-2">
							<OfficeIcon />
							<span className="text-[#71717A] font-medium text-sm leading-[20px]">
								{event.organizer}
							</span>
						</div>
					</div>
				</div>
			</section>

			<div className="py-4 bg-white px-4 md:hidden">
				<div className="w-full border-t-[1.5px] border-[#E4E4E7] "></div>
			</div>

			{/* Mobile Hero Section */}
			<section className="md:hidden bg-[#FAFAFA] px-4 pb-6">
				{/* Event Image */}
				<div className="relative h-[176px] overflow-hidden">
					<Image
						src={event.image}
						fill
						alt="event image"
						className="object-cover rounded-[12px]"
					/>
				</div>
				<div className="rounded-[12px] overflow-hidden  mt-4">
					{/* Event Content */}

					{/* Event Details Grid */}
					<div className="grid grid-cols-2 gap-6 mb-4 bg-white py-5 px-2 rounded-[6px]">
						{/* Location */}
						<div className="flex items-center gap-3">
							<div className="p-2 rounded-full border border-[#E4E4E7] bg-white">
								<LocationIcon className="w-[13.5px] h-[13.5px]" />
							</div>
							<div>
								<p className="text-xs leading-[16px] font-medium text-[#18181B]">
									Location
								</p>
								<p className="text-xs leading-[16px] font-regular text-[#71717A]">
									Lagos, Nigeria
								</p>
							</div>
						</div>
						{/* Date */}
						<div className="flex items-center gap-3">
							<div className="p-2 rounded-full border border-[#E4E4E7] bg-white">
								<CalendarCheckIcon className="w-[13.5px] h-[13.5px]" />
							</div>
							<div>
								<p className="text-xs leading-[16px] font-medium text-[#18181B]">
									Event Date
								</p>
								<p className="text-xs leading-[16px] font-regular text-[#71717A]">
									Jan 30, 2025
								</p>
							</div>
						</div>

						{/* Time */}
						<div className="flex items-center gap-3">
							<div className="p-2 rounded-full border border-[#E4E4E7] bg-white">
								<ClockIcon className="w-[13.5px] h-[13.5px]" />
							</div>
							<div>
								<p className="text-xs leading-[16px] font-medium text-[#18181B]">
									Time
								</p>
								<p className="text-xs leading-[16px] font-regular text-[#71717A]">
									2:30pm - 5:00pm (WAT)
								</p>
							</div>
						</div>
					</div>

					{/* CTA Buttons */}
					<div className="space-y-3">
						<Button variant="primary" size="sm" className="w-full">
							I am Attending
						</Button>
					</div>
				</div>
			</section>

			{/* Mobile Event Description Section */}
			<section className="md:hidden bg-[#FAFAFA] px-4 py-12">
				<div className="bg-[#FAFAFA]">
					<h2 className="text-[20px] leading-[28px] font-semibold text-[#52525B] mb-4">
						About the Event
					</h2>
					<p className="text-[16px] leading-[24px] font-regular text-[#71717A]">
						The Lagos Network: Exclusive Tech Startups, Entrepreneurs &
						Professionals Networking Event
						<br />
						<br />
						Join The London Network for a dynamic and exclusive event designed
						for tech enthusiasts, entrepreneurs, startup founders, and
						professionals who are shaping the future of technology. This is your
						opportunity to connect with industry leaders, exchange ideas, and
						build valuable relationships in a welcoming, no-pressure
						environment.
						<br />
						<br />
						🕒 Time: 6:00 PM – 9:00 PM
						<br />
						📍 Location: Gourmet Bar at Novotel London Bridge, located at 53-61
						Southwark Bridge Rd, Lagos. A contemporary venue offering a relaxed
						atmosphere with modern decor, perfect for networking and vibrant
						conversations.
						<br />
						👔 Dress Code: Business smart casual.
					</p>
				</div>
			</section>

			{/* Mobile More in Series Section */}
			<MobileEventsCarousel
				// events={eventData}
				// onViewMore={handleViewMore}
				// onEventClick={handleEventClick}
				title="More in this series"
				description=""
				onViewMore={handleViewMore}>
				{eventData.slice(0, 3).map((event) => (
					<EventCard
						key={event.id}
						event={event}
						handleEventClick={handleEventClick}
					/>
				))}
			</MobileEventsCarousel>

			<Footer />
		</main>
	);
}
