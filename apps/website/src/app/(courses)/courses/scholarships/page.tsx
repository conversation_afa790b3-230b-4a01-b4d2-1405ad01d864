"use client";

import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import OpportunityCard from "@/components/ui/OpportunityCard";
import { LabelInput, Tab } from "@/components/ui";
import { ArrowRightIcon, EmptyStateIcon, FilterIcon } from "@/components/common/icons";

import Image from "next/image";
import { useState } from "react";
import FilterSidebar, { FilterSection } from "@/components/ui/FilterSidebar";
import EmptyState from "@/components/ui/EmptyState";
import { usePathname, useRouter } from "next/navigation";
import { scholarshipsData } from "@/lib/courses";

const tabs = [
	"All",
	"African Scholarships",
	"Internatiional Scholarships",
	"Asian Scholarships",
	"European Scholarships",
	"American Scholarships",
];

const filters = {
	category: [
		"Business",
		"Technology",
		"Healthcare",
		"Agriculture",
		"Education",
		"Marketing",
		"Design",
		"Language",
	],
};

export default function ScholarshipsPage() {
	const [selectedCountries, setSelectedCountries] = useState<string[]>([]);
	const [activeTab, setActiveTab] = useState("All");
	const router = useRouter();
	const pathname = usePathname();

	const handleBookmark = (id: string) => {
		console.log("Bookmark opportunity:", id);
		// Implement bookmark functionality
	};

	const handleShare = (id: string) => {
		console.log("Share opportunity:", id);
		// Implement share functionality
	};

	const handleViewDetails = (id: string) => {
		router.push(`${pathname}/${id}`);
		console.log("View details for opportunity:", id);
		// Implement navigation to opportunity details
	};

	// const handleViewMore = () => {
	// 	console.log("View more opportunities");
	// 	// Implement navigation to opportunities page
	// };

	const buildFilterSections = (): FilterSection[] => {
		if (activeTab === "All") {
			return [
				{
					type: "checkbox",
					title: "Category",
					options: filters.category.map((c) => ({ label: c, value: c })),
					selected: selectedCountries,
					onChange: setSelectedCountries,
					placeholder: "Select category",
				},
				// {
				// 	type: "checkbox",
				// 	title: "Category",
				// 	options: filters.category.map((j) => ({ label: j, value: j })),
				// 	selected: selectedCategory,
				// 	onChange: setSelectedCategory,
				// 	placeholder: "Select category",
				// },
				// {
				// 	type: "checkbox",
				// 	title: "Working Hours",
				// 	options: filters.workingHours.map((j) => ({ label: j, value: j })),
				// 	selected: selectedWorkingHour,
				// 	onChange: setSelectedWorkingHour,
				// 	placeholder: "Select job types",
				// },
			];
		}
		return [];
	};

	return (
		<div className="min-h-screen flex flex-col bg-[#FAFAFA]">
			<Header />
			<main className="flex-1 w-full">
				{/* Tab Bar */}
				<Tab
					tabs={tabs}
					activeTab={activeTab}
					onTabChange={setActiveTab}
					className="hidden md:block"
				/>

				{/* Top Section */}
				<section className="mx-auto">
					<div className="flex flex-col px-4 py-8 md:flex-row md:items-center md:justify-between gap-6 md:gap-[58px] md:px-[80px] md:py-[60px]">
						<div className="w-full">
							<h1 className="text-[36px] leading-[44px] md:text-[48px] mdleading-[64px] font-regular text-[#27272A] mb-3 md:mb-5">
								Secure Scholarships and Fee waivers
							</h1>
							<p className="text-[#A1A1AA] text-base md:text-[20px] font-semibold leading-[26px] mb-[18px] md:mb-[30px]">
								Discover scholarship opportunities to further your education
							</p>
							<div className="flex items-center gap-5 mb-3 md:mb-5">
								<LabelInput
									inputType="search"
									placeholder="Scholarship name or keyword"
									className="bg-[#FFFFFE] w-full"
								/>
							</div>
						</div>
						<div className="w-full h-[176px] md:h-[313px] rounded-xl overflow-hidden relative">
							<Image
								src="/scholarships-img.png"
								alt="Job search"
								fill
								className="object-cover"
							/>
						</div>
					</div>
				</section>
				{/* Main Content */}

				<div className="md:hidden  bg-white pt-12 pb-6 px-2">
					<LabelInput
						inputType="dropdown"
						className="w-fit ml-auto"
						dropdownLeftIcon={<FilterIcon />}
						data={[
							{
								label: "Filters",
								value: "Last 7 days",
							},
						]}
					/>
				</div>

				<section className="mx-auto px-2 py-6 md:px-[90px] md:py-[60px] flex gap-[48px] bg-[#FFFFFF] md:bg-[#F4F4F5]">
					<div  className="w-fit hidden md:block">
						{/* Filter Sidebar */}
						<FilterSidebar
							sections={buildFilterSections()}
							onApplyFilters={() => {}}
							onClearAll={() => {
								setSelectedCountries([]);
							}}
						/>
					</div>
					{/* Job Grid */}

					{scholarshipsData.length === 0 ? (
						<section className="w-full m-auto">
							<EmptyState
								icon={<EmptyStateIcon />}
								title="Sorry, there are no jobs available for that search"
								subtitle="Try removing filters or changing some of your search criteria"
								onClear={() => {
									setSelectedCountries([]);

									// Add any other filter resets here
								}}
							/>
						</section>
					) : (
						<section className="w-full">
							<div className="flex flex-col gap-[18px]">
								{scholarshipsData.map((scholarship) => (
									<OpportunityCard
										key={scholarship.id}
										{...scholarship}
										onBookmark={handleBookmark}
										onShare={handleShare}
										onViewDetails={() => handleViewDetails(scholarship.id)}
										rightIcon={
											<ArrowRightIcon className="w-5 h-5" color="#335CFF" />
										}
										btnText="View details"
										btnSize="xs"
									/>
								))}
							</div>
						</section>
					)}
				</section>

				<Footer />
			</main>
		</div>
	);
}
