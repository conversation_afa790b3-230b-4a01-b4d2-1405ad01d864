"use client";
import {
	ArrowRightIcon,
	ShareIcon,
	CalendarIcon,
	ClockIcon,
	DeadlineIcon,
	// CalendarCheckIcon,
	FileIcon,
	DownloadIcon,
	OfficeIcon,
	GlobeIcon,
	CardIcon,
	GraduationHatIcon,
	LocationIcon,
} from "@/components/common/icons";
import Header from "@/components/layout/Header";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { useParams } from "next/navigation";
import Image from "next/image";
import { Button } from "@/components/ui";
import Footer from "@/components/layout/Footer";
import { courses } from "@/lib/courses";

const SubmissionDetailsCard = ({
	courseCompany,
}: {
	courseCompany: string;
}) => {
	return (
		<div className="px-6 py-8 rounded-[16px] border-[0.75px] border-[#E4E4E7] bg-white flex flex-col gap-[14px] h-fit">
			<div className="w-full border-t-[1.5px] border-[#F4F4F5]"></div>

			<div className="">
				<p className="text-sm leading-[22px] font-medium text-[#71717A] mb-3">
					School
				</p>
				<div className="flex items-center gap-2">
					<OfficeIcon />
					<span className="text-[#71717A] font-medium text-sm leading-[22px]">
						{courseCompany}
					</span>
				</div>
			</div>
		</div>
	);
};

interface CourseCard {
	id: number;
	title: string;
	organizer: string;
	date: string;
	time: string;
	price: string;
	image: string;
	isFree: boolean;
	courseLevel: string;
}

const relatedCourses: CourseCard[] = [
	{
		id: 1,
		title: "Advanced Data Science Class",
		organizer: "TechAcademy Africa",
		date: "12th May - 13 June",
		time: "2pm",
		price: "Free",
		image: "/corporate1.png",
		isFree: true,
		courseLevel: "Postgraduate",
	},
	{
		id: 2,
		title: "Business Management Fundamentals",
		organizer: "African Business School",
		date: "1st Aug - 13 September",
		time: "10am",
		price: "$52",
		image: "/corporate2.png",
		isFree: false,
		courseLevel: "Postgraduate",
	},
	{
		id: 3,
		title: "Business Management Fundamentals",
		organizer: "African Business School",
		date: "Sat, May 10th",
		time: "9am",
		price: "$52",
		image: "/corporate3.png",
		isFree: false,
		courseLevel: "Postgraduate",
	},
	{
		id: 4,
		title: "Digital Marketing Masterclass",
		organizer: "Lagos Marketing Institute",
		date: "6th May - 7 June",
		time: "1pm",
		price: "$35",
		image: "/corporate1.png",
		isFree: true,
		courseLevel: "Postgraduate",
	},
	{
		id: 5,
		title: "UX/UI Design Bootcamp",
		organizer: "Design Academy",
		date: "Wed, May 14th",
		time: "11am",
		price: "$120",
		image: "/corporate2.png",
		isFree: false,
		courseLevel: "Postgraduate",
	},
];

const CourseCard = ({ course }: { course: CourseCard }) => {
	return (
		<div
			className="bg-white rounded-2xl overflow-hidden min-w-[85vw] sm:min-w-[320px] transition-shadow duration-300"
			style={{ boxShadow: "0px 0px 16px 4px #DFDFDF40" }}>
			{/* Event Image */}
			<div className="relative h-32 overflow-hidden">
				<Image
					src={course.image}
					alt={course.title}
					className="w-full h-full object-cover"
					width={320}
					height={128}
				/>

				<div className="absolute bottom-[10px] left-[10px] sm:bottom-[16px] sm:left-[16px]">
					<span className="bg-[#F4EBF8] backdrop-blur-sm text-[#8F34B4] px-2 sm:px-3 py-0.5 sm:py-1 rounded-full text-xs sm:text-base leading-[18px] sm:leading-[26px] font-normal">
						{course.courseLevel}
					</span>
				</div>
			</div>

			{/* Event Content */}
			<div className="py-4 sm:py-6 px-3 sm:px-4">
				{/* Title */}
				<h3 className="text-lg sm:text-[24px] sm:leading-[32px] font-semibold text-neutral-600 mb-1 sm:mb-2 line-clamp-2">
					{course.title}
				</h3>

				{/* Organizer */}
				<p className="text-neutral-500 text-sm sm:text-[18px] sm:leading-6 mb-2 sm:mb-5">
					{course.organizer}
				</p>

				{/* Date & Time */}
				<div className="flex items-center gap-2 mb-4 sm:mb-6">
					<CalendarIcon className="w-4 h-4 text-neutral-500 " />
					<span className="text-neutral-600 text-sm sm:text-[18px] sm:leading-6">
						{course.date}
					</span>
				</div>

				{/* Price */}
				<div className="flex justify-start border-t border-neutral-100 pt-4 sm:pt-6">
					<span
						className={`text-success-500 font-bold text-sm sm:text-[18px] sm:leading-6`}>
						{course.price}
					</span>
				</div>
			</div>
		</div>
	);
};

export default function OpportunityDetailPage() {
	const params = useParams();
	const { id } = params;

	const course = courses.find((opp) => opp.id === id);

	console.log(course);

	if (!course) {
		return (
			<div className="min-h-screen flex flex-col items-center justify-center">
				<h1 className="text-3xl font-bold mb-4">Course Not Found</h1>
				<p className="text-lg">
					No course found for ID: <span className="font-mono">{id}</span>
				</p>
			</div>
		);
	}

	// const handleBookmark = (id: string) => {
	// 	console.log("Bookmark opportunity:", id);
	// 	// Implement bookmark functionality
	// };

	// const handleShare = (id: string) => {
	// 	console.log("Share opportunity:", id);
	// 	// Implement share functionality
	// };
	const handleViewMore = () => {
		console.log("Share opportunity:", id);
		// Implement share functionality
	};
	// const handleViewDetails = (id: string) => {
	// 	console.log("Share opportunity:", id);
	// 	// Implement share functionality
	// };

	return (
		<main className="w-full h-full">
			<Header />

			<section className="w-full p-4 md:px-[90px] md:py-[32px] bg-white md:bg-[#FAFAFA] h-full">
				<Link
					href="/courses"
					className="flex items-center gap-2 text-sm font-medium leading-[22px] text-[#52525B] mb-4 md:mb-[32px] hover:text-brand-500 transition-all duration-300 ">
					<ArrowRightIcon
						className="rotate-180 w-6 h-6 hover:text-brand-500"
						color="#52525B"
					/>{" "}
					Back to Courses
				</Link>

				<div className="w-full bg-[#FAFAFA] rounded-[8px] md:rounded-auto h-full">
					<div className="flex gap-2 flex-wrap mx-2 pt-2 md:mx-auto md:pt-auto">
						{course.tags.map((tag, index) => {
							// New style: object tag, use custom colors if provided
							return (
								<span
									key={index}
									className={cn(
										tag.textColor || "text-neutral-500",
										tag.borderColor || "border-neutral-200",
										tag.bgColor || "bg-neutral-100",
										"px-2 sm:px-3 py-0.5 sm:py-1 text-xs sm:text-sm leading-[18px] sm:leading-[22px] rounded-full font-medium border"
									)}>
									{tag.label}
								</span>
							);
						})}
					</div>
					<div className="grid grid-cols-1 md:grid-cols-[70%_30%] gap-[42px] mt-4">
						<div className="flex flex-col md:gap-10">
							<div className="mx-2  md:mx-auto">
								<h2 className="font-regular text-[#18181B] text-[20px] leading-[26px]  md:text-[48px] md:leading-[64px] max-w-[833px]">
									{course.title}
								</h2>

								<div className="md:hidden my-4 ">
									<p className="text-sm leading-[22px] font-medium text-[#71717A] mb-3">
										School
									</p>
									<div className="flex items-center gap-2">
										<OfficeIcon />
										<span className="text-[#71717A] font-medium text-sm leading-[22px]">
											{course.courseCompany}
										</span>
									</div>
								</div>
							</div>

							<div className="flex items-center md:hidden py-4 bg-white">
								<div className="w-full border-t-[1.5px] border-[#E4E4E7] md:hidden "></div>
							</div>

							{/* white cards section */}
							<div className="grid grid-cols-2 md:grid-cols-3 gap-y-6 p-5 md:mt-10 bg-white border md:border-none rounded-[6px] border-[#F4F4F5]">
								{/* details cards */}
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<LocationIcon className="w-3.5 h-3.5 md:w-auto md:h-auto" />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											Start Date
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											September 15, 2024
										</p>
									</div>
								</div>
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<DeadlineIcon className="w-3.5 h-3.5 md:w-auto md:h-auto" color="#52525B" />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											End Date
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											June 30, 2025
										</p>
									</div>
								</div>
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<ClockIcon color="#52525B" className="w-3.5 h-3.5 md:w-auto md:h-auto" />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											Duration
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											9 months
										</p>
									</div>
								</div>
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<CardIcon className="w-3.5 h-3.5 md:w-auto md:h-auto" />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											Fees
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											$1200
										</p>
									</div>
								</div>
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<GlobeIcon className="w-3.5 h-3.5 md:w-auto md:h-auto" color="#52525B" />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											Mode
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											Hybrid
										</p>
									</div>
								</div>
							</div>

							<Button variant="primary" size="sm" className="md:hidden mt-4">Enroll Now (Free)</Button>

							<div className="mt-12 md:mt-auto">
								<p className="font-semibold text-base leading-[26px] md:text-[24px] text-[#52525B] md:leading-[22px]">
									Introduction to the Course
								</p>
								<p className="text-base leading-[26px] md:text-[20px] md:leading-[26px] font-regular text-[#71717A] mt-[14px]">
									This Advanced Data Science program is designed to equip
									professionals with cutting-edge skills in data analysis,
									machine learning, and big data technologies. The curriculum
									covers statistical methods, predictive modeling, deep
									learning, natural language processing, and data visualization.
									Students will work on real-world projects using
									industry-standard tools and frameworks. Upon completion,
									graduates will be able to: - Design and implement complex data
									science solutions - Apply advanced machine learning algorithms
									to solve business problems - Work with big data technologies
									and distributed computing - Communicate insights effectively
									through data visualization - Lead data science teams and
									projects in various industries
								</p>
							</div>

							<div className="mt-12 md:mt-auto">
								<p className="font-semibold text-base leading-[26px] md:text-[24px] text-[#52525B] md:leading-[22px]">
									Eligibility Criteria
								</p>
								<ul className=" ">
									<li className="text-base leading-[26px] md:text-[20px] md:leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Minimum 5 years of experience in IT services
									</li>
									<li className="text-base leading-[26px] md:text-[20px] md:leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Previous experience with government systems
									</li>
									<li className="text-base leading-[26px] md:text-[20px] md:leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* ISO 27001 certification
									</li>
									<li className="text-base leading-[26px] md:text-[20px] md:leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Local presence with 24/7 support capability
									</li>
									<li className="text-base leading-[26px] md:text-[20px] md:leading-[26px] font-regular text-[#71717A] mt-[14px]">
										* Financial stability and capacity to handle projects of
										this scale
									</li>
								</ul>
							</div>

							<div className=" flex flex-col gap-3.5 mt-12 md:mt-auto">
								<p className="font-semibold text-base leading-[26px] md:text-[24px] text-[#52525B] md:leading-[22px]">
									Course Materials
								</p>
								<div className="py-3 px-2 md:p-5 border border-[#E4E4E7] rounded-2xl ">
									<div className="flex items-center gap-4">
										<div className="p-2 md:p-4 rounded-full bg-[#F4F4F5] border border-[#F4F4F5]">
											<FileIcon className="w-5 h-5 md:h-8 md:w-8" />
										</div>
										<div className="">
											<p className="text-sm leading-[22px] font-medium text-[#18181B]">
												Course Brochure.pdf
											</p>
											<p className="text-sm leading-[22px] font-medium text-[#71717A]">
												1.2 MB
											</p>
										</div>

										<div className="ml-auto flex items-center gap-1 border border-[#E4E4E7] px-[6px] py-[7px] rounded-[10px]">
											<DownloadIcon />
											<button className="text-sm leading-[22px] font-medium text-[#52525B]">
												Download
											</button>
										</div>
									</div>
								</div>
								<div className="py-3 px-2 md:p-5 border border-[#E4E4E7] rounded-2xl ">
									<div className="flex items-center gap-4">
										<div className="p-2 md:p-4 rounded-full bg-[#F4F4F5] border border-[#F4F4F5]">
											<FileIcon className="w-5 h-5 md:h-8 md:w-8" />
										</div>
										<div className="">
											<p className="text-sm leading-[22px] font-medium text-[#18181B]">
												Curriculum Details.pdf
											</p>
											<p className="text-sm leading-[22px]  font-medium text-[#71717A]">
												920 KB
											</p>
										</div>

										<div className="ml-auto flex items-center gap-1 border border-[#E4E4E7] px-[6px] py-[7px] rounded-[10px]">
											<DownloadIcon />
											<button className="text-sm leading-[22px] font-medium text-[#52525B]">
												Download
											</button>
										</div>
									</div>
								</div>
							</div>
							<div className=" flex flex-col gap-3.5 mt-12 md:mt-auto">
								<p className="font-semibold text-base leading-[26px] md:text-[24px] text-[#52525B] md:leading-[22px]">
									Course Modules
								</p>
								<div className="p-5 border border-[#E4E4E7] rounded-2xl ">
									<div className="flex items-center gap-4">
										<div className="p-2.5 rounded-full bg-[#F4EBF8] border border-[#F4F4F5]">
											<GraduationHatIcon className="w-4 h-4 md:size-[22px]" color="#A55DC3" />
										</div>
										<div className="">
											<p className="text-sm md:text-[20px] leading-[22px] font-medium md:font-semibold text-[#52525B]">
												Foundations of Data Science
											</p>
										</div>
									</div>
								</div>
								<div className="p-5 border border-[#E4E4E7] rounded-2xl ">
									<div className="flex items-center gap-4">
										<div className="p-2.5 rounded-full bg-[#F4EBF8] border border-[#F4F4F5]">
											<GraduationHatIcon className="w-4 h-4 md:size-[22px]" color="#A55DC3" />
										</div>
										<div className="">
											<p className="text-sm md:text-[20px] leading-[22px] font-medium md:font-semibold text-[#52525B]">
												Advanced Statistical Methods
											</p>
										</div>
									</div>
								</div>
								<div className="p-5 border border-[#E4E4E7] rounded-2xl ">
									<div className="flex items-center gap-4">
										<div className="p-2.5 rounded-full bg-[#F4EBF8] border border-[#F4F4F5]">
											<GraduationHatIcon className="w-4 h-4 md:size-[22px]" color="#A55DC3" />
										</div>
										<div className="">
											<p className="text-sm md:text-[20px] leading-[22px] font-medium md:font-semibold text-[#52525B]">
												Machine Learning Algorithms
											</p>
										</div>
									</div>
								</div>
								<div className="p-5 border border-[#E4E4E7] rounded-2xl ">
									<div className="flex items-center gap-4">
										<div className="p-2.5 rounded-full bg-[#F4EBF8] border border-[#F4F4F5]">
											<GraduationHatIcon className="w-4 h-4 md:size-[22px]" color="#A55DC3" />
										</div>
										<div className="">
											<p className="text-sm md:text-[20px] leading-[22px] font-medium md:font-semibold text-[#52525B]">
												Deep Learning and Neural Networks
											</p>
										</div>
									</div>
								</div>
								<div className="p-5 border border-[#E4E4E7] rounded-2xl ">
									<div className="flex items-center gap-4">
										<div className="p-2.5 rounded-full bg-[#F4EBF8] border border-[#F4F4F5]">
											<GraduationHatIcon className="w-4 h-4 md:size-[22px]" color="#A55DC3" />
										</div>
										<div className="">
											<p className="text-sm md:text-[20px] leading-[22px] font-medium md:font-semibold text-[#52525B]">
												Big Data Technologies
											</p>
										</div>
									</div>
								</div>
								<div className="p-5 border border-[#E4E4E7] rounded-2xl ">
									<div className="flex items-center gap-4">
										<div className="p-2.5 rounded-full bg-[#F4EBF8] border border-[#F4F4F5]">
											<GraduationHatIcon className="w-4 h-4 md:size-[22px]" color="#A55DC3" />
										</div>
										<div className="">
											<p className="text-sm md:text-[20px] leading-[22px] font-medium md:font-semibold text-[#52525B]">
												Data Visualization and Communication
											</p>
										</div>
									</div>
								</div>
								<div className="p-5 border border-[#E4E4E7] rounded-2xl ">
									<div className="flex items-center gap-4">
										<div className="p-2.5 rounded-full bg-[#F4EBF8] border border-[#F4F4F5]">
											<GraduationHatIcon className="w-4 h-4 md:size-[22px]" color="#A55DC3" />
										</div>
										<div className="">
											<p className="text-sm md:text-[20px] leading-[22px] font-medium md:font-semibold text-[#52525B]">
												Capstone Project
											</p>
										</div>
									</div>
								</div>
							</div>
						</div>
						<aside className="hidden md:block">
							<SubmissionDetailsCard courseCompany={course.courseCompany} />

							<Button
								variant="primary"
								className="w-full mt-4 focus:outline-none shadow-none font-semibold">
								Enroll Now (Free)
							</Button>
							<Button
								variant="outline"
								leftIcon={<ShareIcon color="#335CFF" />}
								className="w-full mt-4 focus:outline-none shadow-none ">
								Share Course
							</Button>
						</aside>
					</div>

					{/* <div className="w-full border-t-[1.5px] border-[#E4E4E7] my-10"></div> */}
				</div>
			</section>

			<section className="px-[90px] py-[120px] bg-white hidden md:block">
				<div>
					{/* Section Header */}
					<div className="flex items-start justify-between mb-12">
						<div>
							<h2 className="text-neutral-800 font-regular text-[48px] leading-[56px] mb-6">
								Related Courses
							</h2>
						</div>

						{/* View More Button */}
						<Button onClick={handleViewMore} variant="primary" size="md">
							View More
						</Button>
					</div>

					{/* Opportunities Grid */}
					<div className="flex gap-5 overflow-x-auto">
						{relatedCourses.map((course, index) => (
							<CourseCard course={course} key={index} />
						))}
					</div>
				</div>
			</section>
			<Footer />
		</main>
	);
}
