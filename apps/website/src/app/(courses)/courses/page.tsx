"use client";
import { EmptyStateIcon, FilterIcon } from "@/components/common/icons";
import Footer from "@/components/layout/Footer";
import Header from "@/components/layout/Header";
import EmptyState from "@/components/ui/EmptyState";
import FilterSidebar, { FilterSection } from "@/components/ui/FilterSidebar";
import LabelInput from "@/components/ui/LabelInput";
import OpportunityCard from "@/components/ui/OpportunityCard";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { courses } from "@/lib/courses";

// interface IJob {
// 	id: string;
// 	title: string;
// 	company: string;
// 	companyLogo: string;
// 	location: string;
// 	workType: string;
// 	timezone: string;
// 	tags: string[];
// 	isBookmarked: boolean;
// 	showTime: boolean;
// }

const filters = {
	category: [
		"Business",
		"Technology",
		"Healthcare",
		"Agriculture",
		"Education",
		"Marketing",
		"Design",
		"Language",
	],
};

export default function CoursesPage() {
	const router = useRouter();
	// const pathname = usePathname();
	const [selectedCategory, setSelectedCategory] = useState<string[]>([]);

	const buildFilterSections = (): FilterSection[] => {
		return [
			{
				type: "checkbox",
				title: "Category",
				options: filters.category.map((c) => ({ label: c, value: c })),
				selected: selectedCategory,
				onChange: setSelectedCategory,
				placeholder: "Select category",
			},
		];
	};

	const handleViewDetails = (id: string) => {
		router.push(`/courses/${id}`);
	};

	// const handleViewMore = () => {
	// 	console.log("View more opportunities");
	// 	// Implement navigation to opportunities page
	// };

	return (
		<section className="min-h-screen flex flex-col bg-[#FAFAFA] ">
			<Header />
			<main className="flex-1 w-full">
				{/* Top Section */}
				<section className=" mx-auto ">
					<div className="flex flex-col px-4 py-8 md:flex-row md:items-center md:justify-between gap-6 md:gap-[58px] md:px-[80px] md:py-[60px]">
						<div className="w-full">
							<h1 className="text-[36px] leading-[44px] md:text-[48px] mdleading-[64px] font-regular text-[#27272A] mb-3 md:mb-5">
								On-Demand Courses
							</h1>
							<p className="text-[#A1A1AA] text-base md:text-[20px] font-semibold leading-[26px] mb-[18px] md:mb-[30px]">
								Enhance your skills with various courses from top African
								educators
							</p>
							<div className="flex items-center gap-2 mb-5">
								<LabelInput
									inputType="search"
									placeholder="What do you want to learn"
									className="w-full max-w-md"
								/>
							</div>
							<div className="flex flex-wrap gap-4 md:gap-2">
								<span className="bg-[#F4EBF8] text-[#8F34B4] px-3 py-1 rounded-full text-xs leading-[16px] md:text-[18px] md:leading-[24px] font-normal">
									SEO
								</span>
								<span className="bg-[#F4EBF8] text-[#8F34B4] px-3 py-1 rounded-full text-xs leading-[16px] md:text-[18px] md:leading-[24px] font-normal">
									Photoshop
								</span>
								<span className="bg-[#F4EBF8] text-[#8F34B4] px-3 py-1 rounded-full text-xs leading-[16px] md:text-[18px] md:leading-[24px] font-normal">
									Freelancing
								</span>
								<span className="bg-[#F4EBF8] text-[#8F34B4] px-3 py-1 rounded-full text-xs leading-[16px] md:text-[18px] md:leading-[24px] font-normal">
									eCommerce
								</span>
								<span className="bg-[#F4EBF8] text-[#8F34B4] px-3 py-1 rounded-full text-xs leading-[16px] md:text-[18px] md:leading-[24px] font-normal">
									Content
								</span>
							</div>
						</div>
						<div className="w-full h-[176px] md:h-[313px] rounded-xl overflow-hidden relative">
							<Image
								src="/courses-img.png"
								alt="Job search"
								fill
								className="object-cover"
							/>
						</div>
					</div>
				</section>

				<div className="md:hidden  bg-white py-6 px-4">
					<LabelInput
						inputType="dropdown"
						className="w-fit ml-auto"
						dropdownLeftIcon={<FilterIcon />}
						data={[
							{
								label: "Filters",
								value: "Last 7 days",
							},
						]}
					/>
				</div>

				{/* Main Content */}
				<section className="mx-auto px-2 py-6 md:px-[90px] md:py-[60px] flex gap-[48px] bg-[#FFFFFF] md:bg-[#F4F4F5]">
					{/* Filter Sidebar */}
					<div className="w-fit hidden md:block">
						<FilterSidebar
							sections={buildFilterSections()}
							onApplyFilters={() => {}}
							onClearAll={() => {}}
						/>
					</div>
					{/* Job Grid */}
					{courses.length === 0 ? (
						<section className="w-full m-auto">
							<EmptyState
								icon={<EmptyStateIcon />}
								title="Sorry, there are no jobs available for that search"
								subtitle="Try removing filters or changing some of your search criteria"
								onClear={() => {
									// Add any other filter resets here
								}}
							/>
						</section>
					) : (
						<section className="flex-1 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-4">
							{courses.slice(0, 8).map((course, i) => (
								<OpportunityCard
									onViewDetails={() => handleViewDetails(course.id)}
									key={i}
									{...course}
								/>
							))}
						</section>
					)}
				</section>

				<Footer />
			</main>
		</section>
	);
}
