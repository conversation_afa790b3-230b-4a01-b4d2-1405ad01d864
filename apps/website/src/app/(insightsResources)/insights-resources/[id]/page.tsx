"use client";
import {
	<PERSON>AngleIcon,
	ArrowRightIcon,
	ShareIcon,
} from "@/components/common/icons";
import Header from "@/components/layout/Header";
import Link from "next/link";
import { useParams } from "next/navigation";
import Image from "next/image";
import Button from "@/components/ui/Button";
import ArticleCard from "@/components/ui/ArticleCard";
import Footer from "@/components/layout/Footer";
import { useState, useRef, useEffect } from "react";

const PillComponent = ({ tag }: { tag: string }) => {
	return (
		<span
			className={
				"text-[#8F34B4] border-neutral-200  bg-[#F4EBF8] px-2 sm:px-5.5 py-0.5 sm:py-[7px] text-xs sm:text-[20px] leading-[18px] sm:leading-[26px] rounded-full font-regular"
			}>
			{tag}
		</span>
	);
};

const articlesData = [
	{
		id: "1",
		title: "Top 10 In-demand Skills Across Africa in 2025",
		description:
			"Discover the most sought-after professional skills that are driving the African job market...",
		image: "/insights1.png",
		category: "Career Insights",
		publishedDate: "Posted 12th May",
	},
	{
		id: "2",
		title: "Why remote work is transforming African Employment",
		description:
			"The rise of remote work has opened new doors for professionals across the contin...",
		image: "/trending-insights1.jpg",
		category: "Workplace Trends",
		publishedDate: "Posted 12th May",
		isAudio: true,
	},
	{
		id: "3",
		title: "Building a standout Resume - 10 Expert tips",
		description:
			"Learn how to create a professional profile that catches recruiters' attention and highli...",
		image: "/trending-insights2.jpg",
		category: "Career Development",
		publishedDate: "Posted 12th May",
		isArticle: true,
	},
	{
		id: "4",
		title: "Top 10 In-demand Skills Across Africa in 2025",
		description:
			"Discover the most sought-after professional skills that are driving the African job market...",
		image: "/insights1.png",
		category: "Career Insights",
		publishedDate: "Posted 12th May",
	},
	{
		id: "5",
		title: "Why remote work is transforming African Employment",
		description:
			"The rise of remote work has opened new doors for professionals across the contin...",
		image: "/trending-insights1.jpg",
		category: "Workplace Trends",
		publishedDate: "Posted 12th May",
		isAudio: true,
	},
	{
		id: "6",
		title: "Building a standout Resume - 10 Expert tips",
		description:
			"Learn how to create a professional profile that catches recruiters' attention and highli...",
		image: "/trending-insights2.jpg",
		category: "Career Development",
		publishedDate: "Posted 12th May",
		isArticle: true,
	},
];

// Mobile Carousel Component for Related Articles
interface MobileCarouselProps {
	children: React.ReactNode[];
	title: string;
	description: string;
	onViewMore: () => void;
}

function MobileCarousel({
	children,
	title,
	description,
	onViewMore,
}: MobileCarouselProps) {
	const [currentIndex, setCurrentIndex] = useState(0);
	const [cardWidth, setCardWidth] = useState(0);
	const cardRef = useRef<HTMLDivElement>(null);
	const GAP = 16; // px gap between cards

	// Measure card width on mount and when window resizes
	useEffect(() => {
		function updateCardWidth() {
			if (cardRef.current) {
				setCardWidth(cardRef.current.offsetWidth + GAP);
			}
		}
		updateCardWidth();
		window.addEventListener("resize", updateCardWidth);
		return () => window.removeEventListener("resize", updateCardWidth);
	}, []);

	const nextSlide = () => {
		setCurrentIndex((prev) => (prev + 1) % children.length);
	};

	const prevSlide = () => {
		setCurrentIndex((prev) => (prev - 1 + children.length) % children.length);
	};

	return (
		<div>
			{/* Section Header */}
			<div className="flex flex-col items-start gap-6 pb-6 px-4 pt-8 bg-[#FFFFFF]">
				<div>
					<h2 className="text-[#27272A] font-semibold text-[24px] leading-[32px] mb-3">
						{title}
					</h2>
					<p className="text-[#52525B] font-400 text-[16px] leading-[26px]">
						{description}
					</p>
				</div>

				{/* View More Button */}
				<Button
					onClick={onViewMore}
					variant="outline"
					size="xs"
					rightIcon={<ArrowAngleIcon />}>
					View More articles
				</Button>
			</div>

			{/* Carousel Container */}
			<div className="relative bg-[#FFFFFF]">
				{/* Carousel Track */}
				<div className="overflow-hidden px-4">
					<div
						className="flex gap-4 transition-transform duration-500 ease-in-out py-[22px]"
						style={{
							transform: `translateX(-${currentIndex * cardWidth}px)`,
							width: cardWidth ? `${children.length * cardWidth}px` : "auto",
						}}>
						{children.map((child, idx) => (
							<div
								key={idx}
								ref={idx === 0 ? cardRef : null}
								className="w-[280px] sm:w-[320px] md:w-[350px] flex-shrink-0">
								{child}
							</div>
						))}
					</div>
				</div>

				{/* Navigation Arrows */}
				<div className="flex justify-start gap-4 pb-6 px-4 bg-[#FFFFFF]">
					<button
						onClick={prevSlide}
						className="w-[42px] h-[42px] md:w-[51px] md:h-[51px] cursor-pointer rounded-[38.86px] border-[1.21px] border-[#52525B] p-[8.5px] bg-white flex items-center justify-center hover:bg-neutral-50 transition-colors duration-200"
						aria-label="Previous article">
						<ArrowRightIcon className="w-[24px] h-[24px] md:w-[34px] md:h-[34px] text-neutral-600 rotate-180" />
					</button>
					<button
						onClick={nextSlide}
						className="w-[42px] h-[42px] md:w-[51px] md:h-[51px] cursor-pointer rounded-[38.86px] border-[1.21px] border-[#52525B] p-[8.5px] bg-white flex items-center justify-center hover:bg-neutral-50 transition-colors duration-200"
						aria-label="Next article">
						<ArrowRightIcon className="w-[24px] h-[24px] md:w-[34px] md:h-[34px] text-neutral-600" />
					</button>
				</div>
			</div>
		</div>
	);
}

export default function InsightsDetailsPage() {
	const params = useParams();
	const { id } = params;

	const article = articlesData.find((opp) => opp.id === id);

	console.log(article);

	if (!article) {
		return (
			<div className="min-h-screen flex flex-col items-center justify-center">
				<h1 className="text-3xl font-bold mb-4">Course Not Found</h1>
				<p className="text-lg">
					No Article found for ID: <span className="font-mono">{id}</span>
				</p>
			</div>
		);
	}

	const handleReadMore = (id: string) => {
		console.log("Read article:", id);
		// Implement navigation to article details
	};
	const handleViewMore = () => {
		console.log("Share opportunity:", id);
		// Implement share functionality
	};
	// const handleViewDetails = (id: string) => {
	// 	console.log("Share opportunity:", id);
	// 	// Implement share functionality
	// };

	return (
		<main>
			<Header />
			{/* Desktop Layout */}
			<section className="hidden md:block w-full h-full py-8 px-[90px] bg-[#FAFAFA]">
				<Link
					href="/insights-resources"
					className="flex items-center gap-2 text-sm font-medium leading-[22px] text-[#52525B] mb-[32px] hover:text-brand-500 transition-all duration-300 ">
					<ArrowRightIcon
						className="rotate-180 w-6 h-6 hover:text-brand-500"
						color="#52525B"
					/>{" "}
					Back to Insights
				</Link>

				<section className="w-full h-full mt-8">
					<div>
						<PillComponent tag={article.category} />

						<h2 className="text-[48px] leading-[64px] font-regular text-[#18181B] mt-4">
							{article.title}
						</h2>
					</div>

					<div className="w-full h-[382px] relative rounded-2xl mt-10">
						<Image
							src={article.image}
							fill
							className="object-cover rounded-2xl"
							alt="Big image"
						/>
					</div>

					<div className="grid grid-cols-[68%_30%] gap-[42px] mt-12">
						<div className="flex flex-col gap-5">
							<p className="font-regular text-[24px] leading-[32px] text-[#71717A]">
								Africa is rapidly evolving—economically, technologically, and
								socially. As governments invest in digital infrastructure and
								industries modernize, the demand for skilled professionals has
								never been greater. Whether you&apos;re a student, professional,
								or entrepreneur, knowing what skills are most valuable can help
								you stay ahead of the curve. Here are the top 10 in-demand
								skills across Africa in 2025, based on employment trends,
								industry needs, and digital transformation across the continent.
							</p>
							<p className="font-regular text-[24px] leading-[32px] text-[#71717A]">
								1. Software Development & Programming Why it’s in demand: With
								the tech startup ecosystem booming in cities like Lagos,
								Nairobi, and Cape Town, software engineers are highly
								sought-after. From mobile apps to fintech platforms, skilled
								developers are the backbone of digital Africa. Top
								Tools/Languages: Python, JavaScript, Flutter, React Native
							</p>
							<p className="font-regular text-[24px] leading-[32px] text-[#71717A]">
								2. Data Analysis & Data Science Why it’s in demand: Governments,
								NGOs, and private companies increasingly rely on data to drive
								decisions in agriculture, finance, healthcare, and logistics.
								The ability to interpret and visualize data is critical. Key
								Skills: Excel, SQL, Python (Pandas, NumPy), Power BI, Tableau
							</p>
							<p className="font-regular text-[24px] leading-[32px] text-[#71717A]">
								3. Cybersecurity Why it’s in demand: As more businesses go
								digital, cyber threats are increasing. Companies need
								cybersecurity professionals to safeguard data and maintain
								trust, especially in the banking and telecom sectors.
								Certifications to consider: CEH, CompTIA Security+, CISSP
							</p>
							<p className="font-regular text-[24px] leading-[32px] text-[#71717A]">
								4. Digital Marketing Why it’s in demand: With mobile penetration
								growing and social media being a key marketing channel,
								businesses are hiring digital marketers to create engaging
								campaigns and drive sales online. Must-know areas: SEO, Social
								Media Marketing, Google Ads, Email Marketing, Analytics
							</p>
							<p className="font-regular text-[24px] leading-[32px] text-[#71717A]">
								5. Renewable Energy Technology Why it’s in demand: Africa’s
								growing energy needs and the shift toward sustainability are
								creating new roles in solar installation, energy auditing, and
								maintenance of off-grid systems. In-demand roles: Solar
								Technicians, Energy Consultants, Grid System Designers
							</p>
							<p className="font-regular text-[24px] leading-[32px] text-[#71717A]">
								6. Healthcare & Telemedicine Why it’s in demand: Access to
								healthcare is a growing challenge in rural parts of Africa. With
								innovations in telemedicine and health tech, the need for
								trained healthcare professionals who can work both physically
								and remotely is increasing. Emerging roles: Community Health
								Workers, Telemedicine Coordinators, Health Data Analysts
							</p>
							<p className="font-regular text-[24px] leading-[32px] text-[#71717A]">
								7. Agritech & Precision Farming Why it’s in demand: Agriculture
								remains a cornerstone of African economies. New technologies are
								transforming traditional farming. Professionals who understand
								data-driven agriculture, drone mapping, and smart irrigation are
								needed. Skills in focus: GIS, IoT for agriculture, Drone
								Operations, Soil Health Monitoring
							</p>
						</div>

						<aside>
							<div className="px-6 py-8 rounded-[16px] border-[0.75px] border-[#E4E4E7] bg-white flex flex-col gap-[14px] h-fit">
								<div className="">
									<p className="text-sm leading-[22px] font-medium text-[#71717A] mb-3">
										Author
									</p>
									<div className="flex items-center gap-2">
										<span className="text-[#71717A] font-medium text-sm leading-[22px]">
											Jesugbami Alagbara
										</span>
									</div>
								</div>
								<div className="w-full border-t-[1.5px] border-[#F4F4F5]"></div>

								<div className="">
									<p className="text-sm leading-[22px] font-medium text-[#71717A] mb-3">
										Last Updated
									</p>
									<div className="flex items-center gap-2">
										<span className="text-[#71717A] font-medium text-sm leading-[22px]">
											March 26, 2025
										</span>
									</div>
								</div>
							</div>

							<Button
								variant="primary"
								leftIcon={<ShareIcon color="white" />}
								className="mt-4 w-full">
								Share Article
							</Button>
						</aside>
					</div>
				</section>
			</section>

			<section className="hidden md:block px-[90px] py-[120px] bg-white">
				<div>
					{/* Section Header */}
					<div className="flex items-start justify-between mb-12">
						<div>
							<h2 className="text-neutral-800 font-semibold text-[48px] leading-[56px] mb-6">
								Related Articles
							</h2>
							<p className="text-neutral-600 font-400 text-[24px] leading-[32px]">
								Stay updated with the latest career advice, industry trends, and{" "}
								<br /> professional development resources
							</p>
						</div>

						{/* View More Button */}
						<Button
							onClick={handleViewMore}
							variant="outline"
							size="md"
							rightIcon={<ArrowAngleIcon />}>
							View more articles
						</Button>
					</div>

					{/* Articles Grid */}
					<div className="flex items-start gap-5">
						{articlesData.slice(0, 3).map((article) => (
							<ArticleCard
								key={article.id}
								{...article}
								onReadMore={handleReadMore}
							/>
						))}
					</div>
				</div>
			</section>

			{/* Mobile Layout */}
			<section className="md:hidden w-full h-full py-4 px-4 bg-[#FAFAFA]">
				<Link
					href="/insights-resources"
					className="flex items-center gap-2 text-sm font-medium leading-[22px] text-[#52525B] mb-6 hover:text-brand-500 transition-all duration-300">
					<ArrowRightIcon
						className="rotate-180 w-5 h-5 hover:text-brand-500"
						color="#52525B"
					/>
					Back to Insights
				</Link>

				<section className="w-full h-full">
					<div>
						<PillComponent tag={article.category} />

						<h2 className="text-[28px] leading-[36px] font-regular text-[#18181B] mt-4">
							{article.title}
						</h2>
					</div>

					<div className="w-full h-[200px] relative rounded-2xl mt-6">
						<Image
							src={article.image}
							fill
							className="object-cover rounded-2xl"
							alt="Article image"
						/>
					</div>

					{/* Mobile Single Column Layout */}
					<div className="flex flex-col gap-6 mt-8 pb-5">
						{/* Article Content */}
						<div className="flex flex-col gap-4">
							<p className="font-regular text-[16px] leading-[24px] text-[#71717A]">
								Africa is rapidly evolving—economically, technologically, and
								socially. As governments invest in digital infrastructure and
								industries modernize, the demand for skilled professionals has
								never been greater. Whether you&apos;re a student, professional,
								or entrepreneur, knowing what skills are most valuable can help
								you stay ahead of the curve. Here are the top 10 in-demand
								skills across Africa in 2025, based on employment trends,
								industry needs, and digital transformation across the continent.
							</p>
							<p className="font-regular text-[16px] leading-[24px] text-[#71717A]">
								1. Software Development & Programming Why it&apos;s in demand: With
								the tech startup ecosystem booming in cities like Lagos,
								Nairobi, and Cape Town, software engineers are highly
								sought-after. From mobile apps to fintech platforms, skilled
								developers are the backbone of digital Africa. Top
								Tools/Languages: Python, JavaScript, Flutter, React Native
							</p>
							<p className="font-regular text-[16px] leading-[24px] text-[#71717A]">
								2. Data Analysis & Data Science Why it&apos;s in demand: Governments,
								NGOs, and private companies increasingly rely on data to drive
								decisions in agriculture, finance, healthcare, and logistics.
								The ability to interpret and visualize data is critical. Key
								Skills: Excel, SQL, Python (Pandas, NumPy), Power BI, Tableau
							</p>
							<p className="font-regular text-[16px] leading-[24px] text-[#71717A]">
								3. Cybersecurity Why it&apos;s in demand: As more businesses go
								digital, cyber threats are increasing. Companies need
								cybersecurity professionals to safeguard data and maintain
								trust, especially in the banking and telecom sectors.
								Certifications to consider: CEH, CompTIA Security+, CISSP
							</p>
							<p className="font-regular text-[16px] leading-[24px] text-[#71717A]">
								4. Digital Marketing Why it&apos;s in demand: With mobile penetration
								growing and social media being a key marketing channel,
								businesses are hiring digital marketers to create engaging
								campaigns and drive sales online. Must-know areas: SEO, Social
								Media Marketing, Google Ads, Email Marketing, Analytics
							</p>
							<p className="font-regular text-[16px] leading-[24px] text-[#71717A]">
								5. Renewable Energy Technology Why it&apos;s in demand: Africa&apos;s
								growing energy needs and the shift toward sustainability are
								creating new roles in solar installation, energy auditing, and
								maintenance of off-grid systems. In-demand roles: Solar
								Technicians, Energy Consultants, Grid System Designers
							</p>
							<p className="font-regular text-[16px] leading-[24px] text-[#71717A]">
								6. Healthcare & Telemedicine Why it&apos;s in demand: Access to
								healthcare is a growing challenge in rural parts of Africa. With
								innovations in telemedicine and health tech, the need for
								trained healthcare professionals who can work both physically
								and remotely is increasing. Emerging roles: Community Health
								Workers, Telemedicine Coordinators, Health Data Analysts
							</p>
							<p className="font-regular text-[16px] leading-[24px] text-[#71717A]">
								7. Agritech & Precision Farming Why it&apos;s in demand: Agriculture
								remains a cornerstone of African economies. New technologies are
								transforming traditional farming. Professionals who understand
								data-driven agriculture, drone mapping, and smart irrigation are
								needed. Skills in focus: GIS, IoT for agriculture, Drone
								Operations, Soil Health Monitoring
							</p>
						</div>

					
					</div>
				</section>
			</section>

			{/* Mobile Related Articles Section */}
			<section className="md:hidden bg-white">
				<MobileCarousel
					title="Related Articles"
					description="Stay updated with the latest career advice, industry trends, and professional development resources"
					onViewMore={handleViewMore}>
					{articlesData.slice(0, 3).map((article) => (
						<ArticleCard
							key={article.id}
							{...article}
							onReadMore={handleReadMore}
						/>
					))}
				</MobileCarousel>
			</section>

			<Footer />
		</main>
	);
}
