"use client";

import {
	ArrowAngleIcon,
	ArrowRightIcon,
	CalendarCheckIcon,
	CalendarIcon,
	CaretIcon,
	DownloadIcon,
	FileIcon,
	LocationIcon,
	MailIcon,
	OfficeIcon,
	PhoneIcon,
	ShareIcon,
} from "@/components/common/icons";
import Footer from "@/components/layout/Footer";
import Header from "@/components/layout/Header";
import Button from "@/components/ui/Button";
import Link from "next/link";
import { useParams } from "next/navigation";
import React, { useState, useRef, useEffect } from "react";

// Data for corporate notices
const corporateNotices = [
	{
		id: "1",
		title: "Regional Development Bank - Policy Update",
		description:
			"New financing options for SMEs in the agricultural sector across Eastern and Southern Africa.",
		organization: "TechAcademy Africa",
		postedDate: "April 8, 2025",
	},
	{
		id: "2",
		title: "Regional Development Bank - Policy Update",
		description:
			"New financing options for SMEs in the agricultural sector across Eastern and Southern Africa.",
		organization: "TechAcademy Africa",
		postedDate: "April 8, 2025",
	},
	{
		id: "3",
		title: "Regional Development Bank - Policy Update",
		description:
			"New financing options for SMEs in the agricultural sector across Eastern and Southern Africa.",
		organization: "TechAcademy Africa",
		postedDate: "April 8, 2025",
	},
	{
		id: "4",
		title: "Regional Development Bank - Policy Update",
		description:
			"New financing options for SMEs in the agricultural sector across Eastern and Southern Africa.",
		organization: "TechAcademy Africa",
		postedDate: "April 8, 2025",
	},
	{
		id: "5",
		title: "Regional Development Bank - Policy Update",
		description:
			"New financing options for SMEs in the agricultural sector across Eastern and Southern Africa.",
		organization: "TechAcademy Africa",
		postedDate: "April 8, 2025",
	},
	{
		id: "6",
		title: "Regional Development Bank - Policy Update",
		description:
			"New financing options for SMEs in the agricultural sector across Eastern and Southern Africa.",
		organization: "TechAcademy Africa",
		postedDate: "April 8, 2025",
	},
];

interface CorporateNoticesCardProps {
	id: string;
	title: string;
	description: string;
	organization: string;
	postedDate: string;
	onViewDetails: (id: string) => void;
}

// Update CorporateNoticesCard to accept typed props
const CorporateNoticesCard = ({
	id,
	title,
	description,
	organization,
	postedDate,
	onViewDetails,
}: CorporateNoticesCardProps) => {
	return (
		<div className="border border-[#E6E6E6] rounded-[12px] px-4 py-6 flex flex-col gap-5 cursor-pointer hover:shadow-sm transition-shadow">
			<div>
				<h3 className="font-semibold text-[24px] leading-[32px] text-[#52525B]">
					{title}
				</h3>
				<p className="font-regular text-[14px] leading-[24px] text-[#71717A] mt-2">
					{description}
				</p>
			</div>

			<div className="h-[1.5px] bg-[#F4F4F5] w-full"></div>

			<div className="">
				<div className="flex items-center gap-2">
					<OfficeIcon />{" "}
					<span className="font-regular text-[14px] leading-[24px] text-[#52525B] ">
						{organization}
					</span>
				</div>
				<div className="flex items-center gap-2 mt-4">
					<CalendarIcon />{" "}
					<span className="font-regular text-[14px] leading-[24px] text-[#52525B] ">
						Posted {postedDate}
					</span>
				</div>
			</div>

			<Button
				onClick={() => onViewDetails(id)}
				variant="outline"
				className="w-fit"
				size="xs"
				rightIcon={<ArrowAngleIcon color="#335CFF" />}>
				View details
			</Button>
		</div>
	);
};

// Mobile Carousel Component for Corporate Notices
interface MobileCarouselProps {
	children: React.ReactNode[];
	title: string;
	description: string;
	onViewMore: () => void;
}

function MobileCarousel({
	children,
	title,
	description,
	onViewMore,
}: MobileCarouselProps) {
	const [currentIndex, setCurrentIndex] = useState(0);
	const [cardWidth, setCardWidth] = useState(0);
	const cardRef = useRef<HTMLDivElement>(null);
	const GAP = 16; // px gap between cards

	// Measure card width on mount and when window resizes
	useEffect(() => {
		function updateCardWidth() {
			if (cardRef.current) {
				setCardWidth(cardRef.current.offsetWidth + GAP);
			}
		}
		updateCardWidth();
		window.addEventListener("resize", updateCardWidth);
		return () => window.removeEventListener("resize", updateCardWidth);
	}, []);

	const nextSlide = () => {
		setCurrentIndex((prev) => (prev + 1) % children.length);
	};

	const prevSlide = () => {
		setCurrentIndex((prev) => (prev - 1 + children.length) % children.length);
	};

	return (
		<div>
			{/* Section Header */}
			<div className="flex flex-col items-start gap-6 pb-6 px-4 pt-8 bg-[#FFFFFF]">
				<div>
					<h2 className="text-[#27272A] font-semibold text-[24px] leading-[32px] mb-3">
						{title}
					</h2>
					<p className="text-[#52525B] font-400 text-[16px] leading-[26px]">
						{description}
					</p>
				</div>

				{/* View More Button */}
				<Button
					onClick={onViewMore}
					variant="outline"
					size="xs"
					rightIcon={<ArrowAngleIcon />}>
					View all
				</Button>
			</div>

			{/* Carousel Container */}
			<div className="bg-[#FFFFFF]">
				{/* Carousel Track */}
				<div className="overflow-hidden px-4">
					<div
						className="flex gap-4 transition-transform duration-500 ease-in-out py-[22px]"
						style={{
							transform: `translateX(-${currentIndex * cardWidth}px)`,
							width: cardWidth ? `${children.length * cardWidth}px` : "auto",
						}}>
						{children.map((child, idx) => (
							<div
								key={idx}
								ref={idx === 0 ? cardRef : null}
								className="w-[280px] sm:w-[320px] md:w-[350px] flex-shrink-0">
								{child}
							</div>
						))}
					</div>
				</div>

				{/* Navigation Buttons - Positioned at Bottom */}
				<div className="flex gap-2 px-4 pb-6">
					<button
						onClick={prevSlide}
						className="w-10 h-10 rounded-full border border-[#52525B] bg-white flex items-center justify-center hover:bg-gray-50 transition-colors">
						<ArrowRightIcon className="rotate-180 w-5 h-5" color="#52525B" />
					</button>
					<button
						onClick={nextSlide}
						className="w-10 h-10 rounded-full border border-[#52525B] bg-white flex items-center justify-center hover:bg-gray-50 transition-colors">
						<ArrowRightIcon className="w-5 h-5" color="#52525B" />
					</button>
				</div>
			</div>
		</div>
	);
}

export default function CorporateNoticesPage() {
	const params = useParams();
	const { id } = params;

	const notice = corporateNotices.find((notice) => notice.id === id);

	console.log(notice);

	const handleReadMore = (id: string) => {
		console.log("Read article:", id);
		// Implement navigation to article details
	};

	const handleViewMore = () => {
		console.log("Share opportunity:", id);
		// Implement share functionality
	};

	if (!notice) {
		return (
			<div className="min-h-screen flex flex-col items-center justify-center">
				<h1 className="text-3xl font-bold mb-4">Course Not Found</h1>
				<p className="text-lg">
					No Corporate Notice found for ID:{" "}
					<span className="font-mono">{id}</span>
				</p>
			</div>
		);
	}

	return (
		<main className="">
			<Header />
			{/* Desktop Layout */}
			<section className="hidden md:block w-full h-full py-8 px-[90px] bg-[#FAFAFA]">
				<Link
					href="/insights-resources"
					className="flex items-center gap-2 text-sm font-medium leading-[22px] text-[#52525B] mb-[32px] hover:text-brand-500 transition-all duration-300 ">
					<ArrowRightIcon
						className="rotate-180 w-6 h-6 hover:text-brand-500"
						color="#52525B"
					/>{" "}
					Back to Events
				</Link>

				<section className="w-full h-full mt-8">
					<div className="grid grid-cols-[68%_30%] gap-[42px] mt-12">
						<div className="flex flex-col gap-5">
							<div>
								<h2 className="text-[48px] leading-[64px] font-regular text-[#18181B] mt-4 mb-5">
									{notice.title}
								</h2>
							</div>

							{/* white cards section */}
							<div className="grid grid-cols-4 gap-y-6 p-5 mt-6 bg-white mb-5">
								{/* details cards */}
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<LocationIcon />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											Location
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											San Fransisco, CA
										</p>
									</div>
								</div>
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<MailIcon />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											Mail
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											<EMAIL>
										</p>
									</div>
								</div>
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<PhoneIcon />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											Phone number
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											+****************
										</p>
									</div>
								</div>
								<div className="flex items-start gap-2 ">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<CalendarCheckIcon />
									</div>
									<div className="">
										<p className="text-sm leading-[22px] font-medium text-[#18181B]">
											Date Posted
										</p>
										<p className="text-sm leading-[22px] font-medium text-[#71717A]">
											1 day ago
										</p>
									</div>
								</div>
							</div>

							<div>
								<p className="font-semibold text-[24px] leading-[32px] text-[#52525B] mb-[14px]">
									Summary
								</p>
								<p className="font-regular text-[24px] leading-[32px] text-[#71717A]">
									The African Development Bank has announced significant changes
									to its financing policy for small and medium-sized enterprises
									(SMEs) in the agricultural sector. These updates aim to
									address the unique challenges faced by agricultural businesses
									across Eastern and Southern Africa.
								</p>
							</div>

							<div>
								<h3 className="font-semibold text-[24px] leading-[32px] text-[#52525B] mb-[14px]">
									Key Policy Changes
								</h3>
								<p className="font-regular text-[24px] leading-[32px] text-[#71717A]">
									The new policy framework introduces several important changes:
									<ul className="ml-5 list-disc">
										<li>
											Reduced Collateral Requirements: Recognizing the
											challenges agricultural SMEs face in providing traditional
											collateral, the bank will now accept alternative forms of
											security, including crop insurance policies, warehouse
											receipts, and equipment.
										</li>
										<li>
											Longer Grace Periods: To accommodate agricultural
											production cycles, grace periods on loan repayments have
											been extended to up to 12 months for seasonal crops and 24
											months for perennial crops.
										</li>
										<li>
											Technical Assistance Package: Borrowers will receive
											complementary business development services, including
											financial management training, agricultural best
											practices, and market linkage support.
										</li>
										<li>
											Green Financing Incentives: Preferential interest rates
											will be offered to businesses implementing climate-smart
											agricultural practices or renewable energy solutions.
										</li>
									</ul>
								</p>
							</div>

							<div className=" flex flex-col gap-3.5">
								<p className="font-semibold text-[24px] leading-[32px] text-[#52525B] ">
									Attachments
								</p>
								<div className="p-5 border border-[#E4E4E7] rounded-2xl ">
									<div className="flex items-center gap-4">
										<div className="p-4 rounded-full bg-[#F4F4F5] border border-[#F4F4F5]">
											<FileIcon />
										</div>
										<div className="">
											<p className="text-sm leading-[22px] font-medium text-[#18181B]">
												Policy Framework Document.pdf
											</p>
											<p className="text-sm leading-[22px] font-medium text-[#71717A]">
												1.2 MB
											</p>
										</div>

										<div className="ml-auto flex items-center gap-1 border border-[#E4E4E7] px-[6px] py-[7px] rounded-[10px]">
											<DownloadIcon />
											<button className="text-sm leading-[22px] font-medium text-[#52525B]">
												Download
											</button>
										</div>
									</div>
								</div>
								<div className="p-5 border border-[#E4E4E7] rounded-2xl ">
									<div className="flex items-center gap-4">
										<div className="p-4 rounded-full bg-[#F4F4F5] border border-[#F4F4F5]">
											<FileIcon />
										</div>
										<div className="">
											<p className="text-sm leading-[22px] font-medium text-[#18181B]">
												Application Guidelines.pdf
											</p>
											<p className="text-sm leading-[22px] font-medium text-[#71717A]">
												920 KB
											</p>
										</div>

										<div className="ml-auto flex items-center gap-1 border border-[#E4E4E7] px-[6px] py-[7px] rounded-[10px]">
											<DownloadIcon />
											<button className="text-sm leading-[22px] font-medium text-[#52525B]">
												Download
											</button>
										</div>
									</div>
								</div>
							</div>
						</div>

						<aside>
							<div className="px-6 py-8 rounded-[16px] border-[0.75px] border-[#E4E4E7] bg-white flex flex-col gap-[14px] h-fit">
								<div className="">
									<p className="text-sm leading-[22px] font-medium text-[#71717A] mb-3">
										Company
									</p>
									<div className="flex items-center gap-2">
										<OfficeIcon />
										<span className="text-[#71717A] font-medium text-sm leading-[22px]">
											TechAcademy Africa
										</span>
									</div>
								</div>
							</div>

							<Button
								variant="primary"
								rightIcon={<CaretIcon color="white" />}
								className="mt-4 w-full">
								Visit official website
							</Button>
							<Button
								variant="outline"
								leftIcon={<ShareIcon color="#335CFF" />}
								className="mt-4 w-full">
								Share Notice
							</Button>
						</aside>
					</div>
				</section>
			</section>

			<section className="hidden md:block px-[90px] py-[120px] bg-white">
				<div>
					{/* Section Header */}
					<div className="flex items-start justify-between mb-12">
						<div>
							<h2 className="text-neutral-800 font-semibold text-[48px] leading-[56px] mb-6">
								More in this series
							</h2>
						</div>

						{/* View More Button */}
						<Button
							onClick={handleViewMore}
							variant="primary"
							size="md"
							// rightIcon={<ArrowAngleIcon />}
						>
							View all
						</Button>
					</div>

					{/* Articles Grid */}
					<div className="flex items-start gap-5">
						{corporateNotices.slice(0, 4).map((notice) => (
							<CorporateNoticesCard
								key={notice.id}
								{...notice}
								onViewDetails={handleReadMore}
							/>
						))}
					</div>
				</div>
			</section>

			{/* Mobile Layout */}
			<section className="md:hidden w-full h-full py-4  bg-white">
				<Link
					href="/insights-resources"
					className="flex items-center gap-2 text-sm font-medium leading-[22px] px-4 text-[#52525B] mb-6 hover:text-brand-500 transition-all duration-300">
					<ArrowRightIcon
						className="rotate-180 w-5 h-5 hover:text-brand-500"
						color="#52525B"
					/>
					Back to Events
				</Link>

				<section className="w-full h-full">
					{/* Mobile Single Column Layout */}
					<div className="flex flex-col gap-4">
						{/* Title */}

						<div className="flex flex-col gap-4 px-4">
							<div className="p-2 rounded-[8px] bg-[#FAFAFA]">
								<h2 className="text-[28px] leading-[36px] font-regular text-[#18181B] mb-4">
									{notice.title}
								</h2>
							</div>
							<div className="border-t-[1.5px] border-[#E4E4E7] w-full"></div>

							{/* Mobile Info Cards - 2x2 Grid */}
							<div className="grid grid-cols-2 gap-4 p-4 bg-white  border border-[#F4F4F5] rounded-[6px]">
								<div className="flex flex-col items-start gap-2">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<LocationIcon />
									</div>
									<div>
										<p className="text-xs leading-[18px] font-medium text-[#18181B]">
											Location
										</p>
										<p className="text-xs leading-[18px] font-medium text-[#71717A]">
											San Fransisco, CA
										</p>
									</div>
								</div>
								<div className="flex flex-col items-start gap-2">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<MailIcon />
									</div>
									<div>
										<p className="text-xs leading-[18px] font-medium text-[#18181B]">
											Mail
										</p>
										<p className="text-xs leading-[18px] font-medium text-[#71717A]">
											<EMAIL>
										</p>
									</div>
								</div>
								<div className="flex flex-col items-start gap-2">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<PhoneIcon />
									</div>
									<div>
										<p className="text-xs leading-[18px] font-medium text-[#18181B]">
											Phone number
										</p>
										<p className="text-xs leading-[18px] font-medium text-[#71717A]">
											+****************
										</p>
									</div>
								</div>
								<div className="flex flex-col items-start gap-2">
									<div className="p-2 rounded-full border border-[#E4E4E7]">
										<CalendarCheckIcon />
									</div>
									<div>
										<p className="text-xs leading-[18px] font-medium text-[#18181B]">
											Date Posted
										</p>
										<p className="text-xs leading-[18px] font-medium text-[#71717A]">
											1 day ago
										</p>
									</div>
								</div>
							</div>

							<Button variant="primary" size="md" className="w-full">
								Visit official website
							</Button>
						</div>

						{/* Summary Section */}
						<div className="bg-[#FAFAFA] flex flex-col gap-4 px-4 pb-8">
							<div>
								<p className="font-semibold text-[20px] leading-[28px] text-[#52525B] mb-3">
									Summary
								</p>
								<p className="font-regular text-[16px] leading-[24px] text-[#71717A]">
									The African Development Bank has announced significant changes
									to its financing policy for small and medium-sized enterprises
									(SMEs) in the agricultural sector. These updates aim to
									address the unique challenges faced by agricultural businesses
									across Eastern and Southern Africa.
								</p>
							</div>

							{/* Key Policy Changes Section */}
							<div>
								<h3 className="font-semibold text-[20px] leading-[28px] text-[#52525B] mb-3">
									Key Policy Changes
								</h3>
								<div className="font-regular text-[16px] leading-[24px] text-[#71717A]">
									<p className="mb-3">
										The new policy framework introduces several important
										changes:
									</p>
									<ul className="ml-5 list-disc space-y-2">
										<li>
											Reduced Collateral Requirements: Recognizing the
											challenges agricultural SMEs face in providing traditional
											collateral, the bank will now accept alternative forms of
											security, including crop insurance policies, warehouse
											receipts, and equipment.
										</li>
										<li>
											Longer Grace Periods: To accommodate agricultural
											production cycles, grace periods on loan repayments have
											been extended to up to 12 months for seasonal crops and 24
											months for perennial crops.
										</li>
										<li>
											Technical Assistance Package: Borrowers will receive
											complementary business development services, including
											financial management training, agricultural best
											practices, and market linkage support.
										</li>
										<li>
											Green Financing Incentives: Preferential interest rates
											will be offered to businesses implementing climate-smart
											agricultural practices or renewable energy solutions.
										</li>
									</ul>
								</div>
							</div>

							{/* Attachments Section */}
							<div className="flex flex-col gap-3">
								<p className="font-semibold text-[20px] leading-[28px] text-[#52525B]">
									Attachments
								</p>
								<div className="p-4 border border-[#E4E4E7] rounded-2xl">
									<div className="flex items-center gap-3">
										<div className="p-3 rounded-full bg-[#F4F4F5] border border-[#F4F4F5]">
											<FileIcon className="w-5 h-5" />
										</div>
										<div className="flex-1">
											<p className="text-sm leading-[20px] font-medium text-[#18181B]">
												Policy Framework Document.pdf
											</p>
											<p className="text-xs leading-[18px] font-medium text-[#71717A]">
												1.2 MB
											</p>
										</div>
										<div className="flex items-center gap-1 border border-[#E4E4E7] px-2 py-1 rounded-[8px]">
											<DownloadIcon />
											<button className="text-xs leading-[18px] font-medium text-[#52525B]">
												Download
											</button>
										</div>
									</div>
								</div>
								<div className="p-4 border border-[#E4E4E7] rounded-2xl">
									<div className="flex items-center gap-3">
										<div className="p-3 rounded-full bg-[#F4F4F5] border border-[#F4F4F5]">
											<FileIcon className="w-5 h-5" />
										</div>
										<div className="flex-1">
											<p className="text-sm leading-[20px] font-medium text-[#18181B]">
												Application Guidelines.pdf
											</p>
											<p className="text-xs leading-[18px] font-medium text-[#71717A]">
												920 KB
											</p>
										</div>
										<div className="flex items-center gap-1 border border-[#E4E4E7] px-2 py-1 rounded-[8px]">
											<DownloadIcon />
											<button className="text-xs leading-[18px] font-medium text-[#52525B]">
												Download
											</button>
										</div>
									</div>
								</div>
							</div>
						</div>

						{/* Mobile Sidebar Content */}
						{/* <div className="px-4 py-6 rounded-[16px] border-[0.75px] border-[#E4E4E7] bg-white flex flex-col gap-4">
							<div>
								<p className="text-sm leading-[22px] font-medium text-[#71717A] mb-3">
									Company
								</p>
								<div className="flex items-center gap-2">
									<OfficeIcon />
									<span className="text-[#71717A] font-medium text-sm leading-[22px]">
										TechAcademy Africa
									</span>
								</div>
							</div>

							<div className="flex flex-col gap-3">
								<Button
									variant="primary"
									rightIcon={<CaretIcon color="white" />}
									className="w-full">
									Visit official website
								</Button>
								<Button
									variant="outline"
									leftIcon={<ShareIcon color="#335CFF" />}
									className="w-full">
									Share Notice
								</Button>
							</div>
						</div> */}
					</div>
				</section>
			</section>

			{/* Mobile More in this series Section */}
			<section className="md:hidden bg-white">
				<MobileCarousel
					title="More in this series"
					description=""
					onViewMore={handleViewMore}>
					{corporateNotices.slice(0, 6).map((notice) => (
						<div
							key={notice.id}
							className="border border-[#E6E6E6] rounded-[12px] px-4 py-4 flex flex-col gap-4 h-full">
							<div>
								<h3 className="font-semibold text-[18px] leading-[24px] text-[#52525B]">
									{notice.title}
								</h3>
								<p className="font-regular text-[14px] leading-[20px] text-[#71717A] mt-2">
									{notice.description}
								</p>
							</div>

							<div className="h-[1px] bg-[#F4F4F5] w-full"></div>

							<div className="flex flex-col gap-2">
								<div className="flex items-center gap-2">
									<OfficeIcon />
									<span className="font-regular text-[12px] leading-[18px] text-[#52525B]">
										{notice.organization}
									</span>
								</div>
								<div className="flex items-center gap-2">
									<CalendarIcon />
									<span className="font-regular text-[12px] leading-[18px] text-[#52525B]">
										Posted {notice.postedDate}
									</span>
								</div>
							</div>

							<Button
								onClick={() => handleReadMore(notice.id)}
								variant="outline"
								className="w-fit mt-auto"
								size="xs"
								rightIcon={<ArrowAngleIcon color="#335CFF" />}>
								View details
							</Button>
						</div>
					))}
				</MobileCarousel>
			</section>

			<Footer />
		</main>
	);
}
