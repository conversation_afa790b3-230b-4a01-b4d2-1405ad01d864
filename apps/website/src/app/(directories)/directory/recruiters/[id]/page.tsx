"use client";

import {
	ArrowRightIcon,
	FilterIcon,
	SuitcaseIconNew,
} from "@/components/common/icons";
import Footer from "@/components/layout/Footer";
import Header from "@/components/layout/Header";
import OpportunityCard from "@/components/ui/OpportunityCard";
import FilterSidebar, { FilterSection } from "@/components/ui/FilterSidebar";
import Image from "next/image";
import Link from "next/link";
import { notFound } from "next/navigation";
import { useState, use } from "react";
import { LabelInput } from "@/components/ui";

// Recruiters data array (you might want to move this to a separate file)
const recruitersData = [
	{
		id: 1,
		image: "/brand1.png",
		companyName: "Avid <PERSON> Consulting",
		jobNumber: "196",
		country: "🇳🇬 Nigeria",
		description:
			"Leading HR consulting firm specializing in talent acquisition and workforce solutions across Africa.",
		website: "https://avidhrconsulting.com",
		email: "<EMAIL>",
		phone: "+234 ************",
		address: "Lagos, Nigeria",
		specializations: ["Technology", "Finance", "Healthcare", "Manufacturing"],
		activeJobs: 45,
	},
	{
		id: 2,
		image: "/brand2.png",
		companyName: "ASA Talent Succession",
		jobNumber: "396",
		country: "🇳🇬 Nigeria",
		description:
			"Premier talent succession and executive search firm with deep expertise in leadership development.",
		website: "https://asatalent.com",
		email: "<EMAIL>",
		phone: "+234 ************",
		address: "Abuja, Nigeria",
		specializations: [
			"Executive Search",
			"Leadership Development",
			"Board Advisory",
			"Succession Planning",
		],
		activeJobs: 78,
	},
	{
		id: 3,
		image: "/brand3.png",
		companyName: "Edge Recruiter Nigeria Limited",
		jobNumber: "122",
		country: "🇳🇬 Nigeria",
		description:
			"Innovative recruitment solutions provider connecting top talent with leading organizations.",
		website: "https://edgerecruiter.ng",
		email: "<EMAIL>",
		phone: "+234 ************",
		address: "Port Harcourt, Nigeria",
		specializations: [
			"Oil & Gas",
			"Engineering",
			"Project Management",
			"Operations",
		],
		activeJobs: 32,
	},
];

// Sample job data for the recruiter
const jobData = [
	{
		id: "1",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
	},
	{
		id: "2",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote"],
		isBookmarked: false,
		showTime: false,
	},
	{
		id: "3",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
	},
	{
		id: "4",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote"],
		isBookmarked: false,
		showTime: false,
	},
	{
		id: "5",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote", "New"],
		isBookmarked: false,
		showTime: false,
	},
	{
		id: "6",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Design", "Remote"],
		isBookmarked: false,
		showTime: false,
	},
];

type Props = {
	params: Promise<{
		id: string;
	}>;
};

export default function RecruiterDetailsPage({ params }: Props) {
	const [activeTab, setActiveTab] = useState("Job Opportunities");
	const [selectedCountries, setSelectedCountries] = useState<string[]>([
		"Nigeria",
	]);
	const [selectedJobTypes, setSelectedJobTypes] = useState<string[]>([]);
	const [selectedWorkingHours, setSelectedWorkingHours] = useState<string[]>(
		[]
	);

	const unwrappedParams = use(params);
	const recruiter = recruitersData.find(
		(r) => r.id === parseInt(unwrappedParams.id)
	);

	if (!recruiter) {
		notFound();
	}

	const handleBookmark = (id: string) => {
		console.log("Bookmark job:", id);
	};

	const handleShare = (id: string) => {
		console.log("Share job:", id);
	};

	const handleViewDetails = (id: string) => {
		console.log("View job details:", id);
	};

	const buildFilterSections = (): FilterSection[] => [
		{
			type: "checkbox",
			title: "COUNTRIES",
			options: [
				{ label: "Nigeria", value: "Nigeria" },
				{ label: "Kenya", value: "Kenya" },
				{ label: "South Africa", value: "South Africa" },
				{ label: "Ghana", value: "Ghana" },
				{ label: "Remote", value: "Remote" },
			],
			selected: selectedCountries,
			onChange: setSelectedCountries,
		},
		{
			type: "checkbox",
			title: "JOB TYPE",
			options: [
				{ label: "FULL TIME", value: "Full-time" },
				{ label: "PART TIME", value: "Part-time" },
				{ label: "INTERNSHIP", value: "Internship" },
			],
			selected: selectedJobTypes,
			onChange: setSelectedJobTypes,
		},
		{
			type: "checkbox",
			title: "WORKING HOURS",
			options: [
				{ label: "9 AM - 5 PM", value: "9-5" },
				{ label: "Flexible", value: "Flexible" },
				{ label: "Night Shift", value: "Night" },
			],
			selected: selectedWorkingHours,
			onChange: setSelectedWorkingHours,
		},
	];

	return (
		<main>
			<Header />

			{/* Recruiter Profile Section */}
			<section className="bg-white border-[#FAFAFA] md:py-[60px] md:px-[90px]">
				<div className=" ">
					{/* Back Link */}
					<div className="mb-6 md:mb-8 px-4 pt-8">
						<Link
							href="/directory/recruiters"
							className="flex items-center gap-2 text-[#71717A] hover:text-[#52525B] transition-colors">
							<ArrowRightIcon className="w-6 h-6 rotate-180" />
							<span className="text-sm font-medium leading-[22px]">
								Back to Recruiters
							</span>
						</Link>
					</div>

					{/* Recruiter Info */}
					<div className="flex flex-row items-start md:items-center gap-3 md:gap-4 px-4 pb-3 md:pb-10 border-b-[1.5px] border-[#E4E4E7]">
						<div className="flex-shrink-0">
							<div className="w-[47px] h-[47px] md:w-[104px] md:h-[104px] rounded-full border-2 border-[#E4E4E7] relative overflow-hidden">
								<Image
									src={recruiter.image}
									fill
									className="object-cover"
									alt={`${recruiter.companyName} logo`}
								/>
							</div>
						</div>
						<div className="flex-1">
							<h1 className="text-2xl leading-[32px] md:text-[48px] md:leading-[64px] font-regular text-[#18181B] mb-2">
								{recruiter.companyName}
							</h1>
							<div className="flex md:flex-row items-start md:items-center gap-6 md:gap-4 text-gray-600">
								<div className="flex items-center gap-2">
									<SuitcaseIconNew color="#71717A" />
									<span className="font-regular text-base leading-[26px] md:text-[24px] md:leading-[32px] text-[#3F3F46]">
										Hiring for {recruiter.jobNumber} Positions
									</span>
								</div>
								<div className="font-regular text-base leading-[26px] md:text-[24px] md:leading-[32px] text-[#3F3F46]">
									<span className="text-lg">{recruiter.country}</span>
								</div>
							</div>
						</div>
					</div>

					{/* Tabs */}
					<div className="mt-8 bg-[#F4F4F5] md:rounded-[12px] w-full p-1 md:px-[9px] md:py-[8px] flex items-center">
						<button
							onClick={() => setActiveTab("Job Opportunities")}
							className={`rounded-[6px] md:rounded-[8px] w-1/2 py-1 md:py-[12px] flex cursor-pointer justify-center items-center transition-all duration-200 ${
								activeTab === "Job Opportunities"
									? "bg-[#FFFFFF]"
									: "bg-transparent"
							}`}>
							<span
								className={`font-regular text-xs leading-[22px] md:text-[18px] md:leading-[24px]  ${
									activeTab === "Job Opportunities"
										? "text-[#3F3F46]"
										: "text-[#A1A1AA]"
								}`}>
								Job Opportunities
							</span>
						</button>
						<button
							onClick={() => setActiveTab("About Company")}
							className={`rounded-[6px] md:rounded-[8px] w-1/2 py-1 md:py-[12px] flex cursor-pointer justify-center items-center transition-all duration-200 ${
								activeTab === "About Company"
									? "bg-[#FFFFFF]"
									: "bg-transparent"
							}`}>
							<span
								className={`font-regular text-xs leading-[22px] md:text-[18px] md:leading-[24px]   ${
									activeTab === "About Company"
										? "text-[#3F3F46]"
										: "text-[#A1A1AA]"
								}`}>
								About Company
							</span>
						</button>
					</div>
				</div>
			</section>

			{activeTab === "Job Opportunities" && (
				<div className="md:hidden  bg-white py-6 px-4">
					<LabelInput
						inputType="dropdown"
						className="w-fit ml-auto"
						dropdownLeftIcon={<FilterIcon />}
						data={[
							{
								label: "Filters",
								value: "Last 7 days",
							},
						]}
					/>
				</div>
			)}

			{/* Main Content */}
			{activeTab === "Job Opportunities" ? (
				<section className="bg-[#F4F4F5] min-h-screen">
					<div className=" mx-auto px-1 md:px-6 lg:px-8 py-12">
						<div className="flex flex-col lg:flex-row gap-5">
							{/* Filter Sidebar */}
							<div className="hidden md:block">
								<FilterSidebar
									sections={buildFilterSections()}
									onApplyFilters={() => {}}
									onClearAll={() => {
										setSelectedCountries([]);
										setSelectedJobTypes([]);
										setSelectedWorkingHours([]);
									}}
								/>
							</div>

							{/* Job Listings */}
							<div className="flex-1">
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									{jobData.map((job) => (
										<OpportunityCard
											key={job.id}
											{...job}
											onBookmark={handleBookmark}
											onShare={handleShare}
											onViewDetails={handleViewDetails}
										/>
									))}
								</div>
							</div>
						</div>
					</div>
				</section>
			) : (
				/* About Company Tab */
				<section className="bg-[#F4F4F5] py-12 px-4 md:py-[60px] md:px-[103px]">
					<div className="max-w-[810px]">
						<div className=" ">
							<h2 className="text-base leading-[26px] md:text-[24px] md:leading-[32px] font-semibold text-[#52525B] mb-[14px]">
								About {recruiter.companyName}
							</h2>
							<p className="text-base leading-[26px] md:text-[24px] md:leading-[32px] font-regular text-[#71717A] mb-8">
								At {recruiter.companyName}, we believe great ideas can come from
								anywhere. We care deeply about creating products that are
								helpful, equitable, and designed for everyone.
							</p>

							<div className="space-y-6">
								<div>
									<label className="text-base leading-[26px] md:text-[24px] md:leading-[32px] font-semibold text-[#52525B]  mb-[14px]">
										Industry
									</label>
									<p className="text-base leading-[26px] md:text-[24px] md:leading-[32px] font-regular text-[#71717A] mb-8">
										{recruiter.specializations.join(", ")}
									</p>
								</div>

								<div>
									<label className="text-base leading-[26px] md:text-[24px] md:leading-[32px] font-semibold text-[#52525B] mb-[14px]">
										Web address
									</label>
									<p className="text-base leading-[26px] md:text-[24px] md:leading-[32px] font-regular text-[#71717A] mb-8">
										{recruiter.website.replace("https://", "www.")}
									</p>
								</div>

								<div>
									<label className="text-base leading-[26px] md:text-[24px] md:leading-[32px] font-semibold text-[#52525B] mb-[14px]">
										Email address
									</label>
									<p className="text-base leading-[26px] md:text-[24px] md:leading-[32px] font-regular text-[#71717A] mb-8">
										{recruiter.email}
									</p>
								</div>

								<div>
									<label className="text-base leading-[26px] md:text-[24px] md:leading-[32px] font-semibold text-[#52525B] mb-[14px]">
										Size
									</label>
									<p className="text-base leading-[26px] md:text-[24px] md:leading-[32px] font-regular text-[#71717A]">
										10K - 15K employees
									</p>
								</div>
							</div>
						</div>
					</div>
				</section>
			)}

			<Footer />
		</main>
	);
}
