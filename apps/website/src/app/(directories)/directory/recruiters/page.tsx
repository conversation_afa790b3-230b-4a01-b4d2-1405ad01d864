import { SuitcaseIconNew } from "@/components/common/icons";
import Footer from "@/components/layout/Footer";
import Header from "@/components/layout/Header";
import LabelInput from "@/components/ui/LabelInput";
import Image from "next/image";
import Link from "next/link";

type RecruitersCardProps = {
	id: number;
	image: string;
	companyName: string;
	jobNumber: string;
	country: string;
};

const RecruitersCard = ({
	id,
	image,
	companyName,
	jobNumber,
	country,
}: RecruitersCardProps) => {
	return (
		<Link href={`/directory/recruiters/${id}`} className="block">
			<div className="p-5 rounded-[12px] border border-[#E4E4E7] flex flex-col gap-6 hover:shadow-sm  transition-all duration-200 cursor-pointer">
				<div className="flex gap-[14px] items-center">
					<div className="p-2 rounded-full border border-[#E4E4E7] relative w-[48px] h-[48px]">
						<Image
							src={image}
							fill
							className="object-cover "
							alt="logo image"
						/>
					</div>

					<p className="font-semibold text-[18px] leading-[24px] text-[#27272A]">
						{companyName}
					</p>
				</div>

				<div className="flex gap-[16px] items-center">
					<div className="flex gap-[8px] items-center">
						<span>
							<SuitcaseIconNew color="#71717A" />
						</span>

						<p className="font-medium text-[14px] leading-[22px] text-[#71717A]">
							Browse {jobNumber} jobs
						</p>
					</div>
					<p className="font-medium text-[14px] leading-[22px] text-[#71717A]">
						{country}
					</p>
				</div>
			</div>
		</Link>
	);
};

// Recruiters data array
const recruitersData = [
	{
		id: 1,
		image: "/brand1.png",
		companyName: "Avid HR Consulting",
		jobNumber: "196",
		country: "🇳🇬 Nigeria",
	},
	{
		id: 2,
		image: "/brand2.png",
		companyName: "ASA Talent Succession",
		jobNumber: "396",
		country: "🇳🇬 Nigeria",
	},
	{
		id: 3,
		image: "/brand3.png",
		companyName: "Edge Recruiter Nigeria Limited",
		jobNumber: "122",
		country: "🇳🇬 Nigeria",
	},
];

export default function DirectoryRecruitersPage() {
	return (
		<main>
			<Header />
			<section className="mx-auto bg-[#2E54E8]">
				<div className="flex flex-col px-4 py-8 md:flex-row md:items-center md:justify-between gap-6 md:gap-[58px] md:px-[80px] md:py-[60px]">
					<div className="w-full">
						<h1 className="text-[36px] leading-[44px] md:text-[48px] mdleading-[64px] font-regular text-[#FAFAFA] mb-3 md:mb-5">
							Browse various{" "}
							<span className="font-semibold">Top Recruiters</span> across
							Africa
						</h1>
						<p className="text-[#FBEEE9] text-base md:text-[20px] font-semibold leading-[26px] mb-6 md:mb-[30px]">
							Browse thousands of job listings across Africa and find your next
							career opportunity.
						</p>
						<div className="flex items-center gap-5 mb-5">
							<LabelInput
								inputType="search"
								placeholder="Find a procurement opportunity"
								className=" w-full"
							/>
						</div>
					</div>
					<div className="  w-full h-[176px] md:h-[313px] rounded-xl overflow-hidden relative">
						<Image
							src="/directories-home-img.png"
							alt="Job search"
							fill
							className="object-cover"
						/>
					</div>
				</div>
			</section>
			<section className="px-2 py-12 md:px-[90px] md:py-[120px] bg-white">
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
					{recruitersData.map((recruiter) => (
						<RecruitersCard
							key={recruiter.id}
							id={recruiter.id}
							image={recruiter.image}
							companyName={recruiter.companyName}
							jobNumber={recruiter.jobNumber}
							country={recruiter.country}
						/>
					))}
				</div>
			</section>
			<Footer />
		</main>
	);
}
