"use client";

import { SuitcaseIconNew } from "@/components/common/icons";
import Footer from "@/components/layout/Footer";
import Header from "@/components/layout/Header";
import { Tab } from "@/components/ui";
import LabelInput from "@/components/ui/LabelInput";
import Image from "next/image";
import Link from "next/link";

type UniversitiesCardProps = {
	id: number;
	image: string;
	universityName: string;
	courseNumber: string;
	country: string;
};

const UniversitiesCard = ({
	id,
	image,
	universityName,
	courseNumber,
	country,
}: UniversitiesCardProps) => {
	return (
		<Link href={`/directory/universities/${id}`} className="block">
			<div className="p-5 rounded-[12px] border border-[#E4E4E7] flex flex-col gap-6 shadow-sm transition-all duration-200 cursor-pointer">
				<div className="flex gap-[14px] items-center">
					<div className="p-2 rounded-full border border-[#E4E4E7] relative w-[48px] h-[48px]">
						<Image
							src={image}
							fill
							className="object-cover"
							alt="university logo"
						/>
					</div>

					<p className="font-semibold text-[18px] leading-[24px] text-[#272727]">
						{universityName}
					</p>
				</div>

				<div className="flex gap-[16px] items-center">
					<div className="flex gap-[8px] items-center">
						<span>
							<SuitcaseIconNew color="#71717A" />
						</span>

						<p className="font-medium text-[14px] leading-[22px] text-[#71717A]">
							Browse {courseNumber} courses
						</p>
					</div>
					<p className="font-medium text-[14px] leading-[22px] text-[#71717A]">
						{country}
					</p>
				</div>
			</div>
		</Link>
	);
};

// Universities data array
const universitiesData = [
	{
		id: 1,
		image: "/brand1.png",
		universityName: "Babcock University",
		courseNumber: "22",
		country: "🇳🇬 Nigeria",
	},
	{
		id: 2,
		image: "/brand2.png",
		universityName: "University of Cape Town",
		courseNumber: "33",
		country: "🇿🇦 South Africa",
	},
	{
		id: 3,
		image: "/brand3.png",
		universityName: "University of Lagos",
		courseNumber: "10",
		country: "🇳🇬 Nigeria",
	},
];

export default function DirectoryUniversitiesPage() {
	return (
		<main>
			<Header />
			<div className="w-full hidden md:block">
				<Tab
					tabs={[
						"All",
						"Africa",
						"Asia",
						"Australia",
						"Europe",
						"North America",
						"South America",
					]}
					activeTab="Africa"
					onTabChange={() => {}}
					className="w-full"
				/>
			</div>
			<section className="mx-auto bg-[#2E54E8]">
				<div className="flex flex-col px-4 py-8 md:flex-row md:items-center md:justify-between gap-6 md:gap-[58px] md:px-[80px] md:py-[60px]">
					<div className="w-full">
						<h1 className="text-[36px] leading-[44px] md:text-[48px] mdleading-[64px] font-regular text-[#FAFAFA] mb-3 md:mb-5">
							Our <span className="font-semibold">Partner Universities</span>{" "}
							across the World
						</h1>
						<p className="text-[#FBEEE9] text-base md:text-[20px] font-semibold leading-[26px] mb-6 md:mb-[30px]">
							Browse thousands of African and International universities and
							explore various courses they offer
						</p>
						<div className="flex items-center gap-5 mb-5">
							<LabelInput
								inputType="search"
								placeholder="Search for university name"
								className=" w-full"
							/>
						</div>
					</div>
					<div className=" w-full h-[176px] md:h-[313px] rounded-xl overflow-hidden relative">
						<Image
							src="/directories-home-img.png"
							alt="Job search"
							fill
							className="object-cover"
						/>
					</div>
				</div>
			</section>
			<section className="px-2 py-12 md:px-[90px] md:py-[120px] bg-white">
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
					{universitiesData.map((university) => (
						<UniversitiesCard
							key={university.id}
							id={university.id}
							image={university.image}
							universityName={university.universityName}
							courseNumber={university.courseNumber}
							country={university.country}
						/>
					))}
					{universitiesData.map((university) => (
						<UniversitiesCard
							key={university.id}
							id={university.id}
							image={university.image}
							universityName={university.universityName}
							courseNumber={university.courseNumber}
							country={university.country}
						/>
					))}
					{universitiesData.map((university) => (
						<UniversitiesCard
							key={university.id}
							id={university.id}
							image={university.image}
							universityName={university.universityName}
							courseNumber={university.courseNumber}
							country={university.country}
						/>
					))}
					{universitiesData.map((university) => (
						<UniversitiesCard
							key={university.id}
							id={university.id}
							image={university.image}
							universityName={university.universityName}
							courseNumber={university.courseNumber}
							country={university.country}
						/>
					))}
				</div>
			</section>
			<Footer />
		</main>
	);
}
