"use client";

import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { Eye, EyeOff, Mail } from "lucide-react";
import OpportunityCard from "@/components/ui/OpportunityCard";

const carouselImages = ["/login-img.png", "/login-img2.png", "/login-img3.png"];

export default function SignupPage() {
	const [showPassword, setShowPassword] = useState(false);
	const [showConfirmPassword, setShowConfirmPassword] = useState(false);
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");
	const [confirmPassword, setConfirmPassword] = useState("");
	const [current, setCurrent] = useState(0);

	const handleSignup = (e: React.FormEvent) => {
		e.preventDefault();
		// Handle signup logic here
	};

	const handleDotClick = (idx: number) => {
		setCurrent(idx);
	};

	const opportunityCardMock = {
		id: "1",
		title: "Senior UX Designer",
		company: "Google",
		companyLogo: "/google-logo.png",
		// location: "San Fransisco, CA",
		workType: "Full-time",
		timezone: "GMT -8",
		tags: ["Design", "Remote"],
		isBookmarked: false,
		showTime: false,
		showBudget: false,
		isFulltime: true,
		showDate: false,
		btnText: "View details",
		showCity: true,
		city: "San Francisco, CA",
	};

	return (
		<div className="h-screen flex flex-col bg-white overflow-hidden">
			{/* Top Nav */}
			<nav className="w-full flex items-center justify-between px-4 py-2 border-b border-[#F4F4F5] bg-white z-50 flex-shrink-0">
				<Link href="/" className="flex items-center">
					<Image
						src="/africa-skillz-logo.png"
						alt="Africaskillz Logo"
						width={140}
						height={40}
					/>
				</Link>
			</nav>
			<div className="flex-1 flex flex-col md:flex-row min-h-0 h-0 overflow-hidden">
				{/* Left: Signup Form */}
				<div className="flex-1 flex flex-col justify-center px-6 sm:px-12 md:px-24 py-8 h-full overflow-auto">
					<div className=" w-full mx-auto">
						<h1 className="text-[48px] leading-[56px] font-semibold text-[#27272A] mb-2">
							Welcome to Africaskillz
						</h1>
						<p className="text-[24px] leading-[32px] font-semibold text-[#A1A1AA] mb-8">
							Create an account and follow your <br /> applications seamlessly
						</p>
						{/* Onboarding navigation button */}
						<a
							href={`${process.env.NEXT_PUBLIC_WEBAPP_URL}/onboarding`}
							target="_blank"
							className="w-full block py-3 rounded-lg bg-[#2563EB] text-white font-semibold text-lg hover:bg-[#1746a2] transition-colors text-center mb-4">
							Go to Onboarding
						</a>
						<form onSubmit={handleSignup} className="space-y-6">
							<div>
								<label
									htmlFor="email"
									className="block text-sm font-medium text-[#52525B] mb-2">
									Email
								</label>
								<div className="relative">
									<span className="absolute left-3 top-1/2 -translate-y-1/2 text-[#A1A1AA]">
										<Mail className="w-5 h-5" />
									</span>
									<input
										id="email"
										type="email"
										autoComplete="email"
										required
										value={email}
										onChange={(e) => setEmail(e.target.value)}
										placeholder="Enter your mail"
										className="w-full pl-10 pr-4 py-3 border border-[#E4E4E7] rounded-lg bg-[#FAFAFA] text-base text-[#18181B] focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-[#A1A1AA] placeholder:opacity-100"
									/>
								</div>
							</div>
							<div>
								<label
									htmlFor="password"
									className="block text-sm font-medium text-[#52525B] mb-2">
									Password
								</label>
								<div className="relative">
									<input
										id="password"
										type={showPassword ? "text" : "password"}
										autoComplete="new-password"
										required
										value={password}
										onChange={(e) => setPassword(e.target.value)}
										placeholder="********"
										className="w-full pr-10 pl-4 py-3 border border-[#E4E4E7] rounded-lg bg-[#FAFAFA] text-base text-[#18181B] focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-[#A1A1AA] placeholder:opacity-100"
									/>
									<button
										type="button"
										className="absolute right-3 top-1/2 -translate-y-1/2 text-[#A1A1AA]"
										onClick={() => setShowPassword((v) => !v)}
										tabIndex={-1}>
										{showPassword ? (
											<EyeOff className="w-5 h-5" />
										) : (
											<Eye className="w-5 h-5" />
										)}
									</button>
								</div>
								{/* Password requirements */}
								<div className="flex flex-wrap gap-2 mt-2">
									<span className="bg-[#F4F4F5] text-[#71717A] text-xs px-2 py-1 rounded-full">
										At least 9 character
									</span>
									<span className="bg-[#F4F4F5] text-[#71717A] text-xs px-2 py-1 rounded-full">
										Uppercase
									</span>
									<span className="bg-[#F4F4F5] text-[#71717A] text-xs px-2 py-1 rounded-full">
										Special Character
									</span>
								</div>
							</div>
							<div>
								<label
									htmlFor="confirmPassword"
									className="block text-sm font-medium text-[#52525B] mb-2">
									Confirm Password
								</label>
								<div className="relative">
									<input
										id="confirmPassword"
										type={showConfirmPassword ? "text" : "password"}
										autoComplete="new-password"
										required
										value={confirmPassword}
										onChange={(e) => setConfirmPassword(e.target.value)}
										placeholder="********"
										className="w-full pr-10 pl-4 py-3 border border-[#E4E4E7] rounded-lg bg-[#FAFAFA] text-base text-[#18181B] focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-[#A1A1AA] placeholder:opacity-100"
									/>
									<button
										type="button"
										className="absolute right-3 top-1/2 -translate-y-1/2 text-[#A1A1AA]"
										onClick={() => setShowConfirmPassword((v) => !v)}
										tabIndex={-1}>
										{showConfirmPassword ? (
											<EyeOff className="w-5 h-5" />
										) : (
											<Eye className="w-5 h-5" />
										)}
									</button>
								</div>
							</div>
							<button
								type="submit"
								className="w-full py-3 rounded-lg bg-[#2563EB] text-white font-semibold text-lg hover:bg-[#1746a2] transition-colors mt-2">
								Create Account
							</button>
						</form>
						<div className="text-center text-sm text-[#71717A] mt-6">
							Have an account?{" "}
							<Link
								href="/auth/login"
								className="text-blue-600 font-medium hover:underline">
								Login
							</Link>
						</div>
						<div className="flex items-center my-8">
							<div className="flex-1 h-px bg-[#E4E4E7]" />
							<span className="mx-4 text-[#A1A1AA] text-sm">OR</span>
							<div className="flex-1 h-px bg-[#E4E4E7]" />
						</div>
						<div className="space-y-4">
							<button className="w-full flex items-center justify-center gap-3 py-3 rounded-lg border border-[#E4E4E7] bg-[#F7F7F7] text-[#18181B] font-medium text-base hover:bg-[#F4F4F5] transition-colors">
								<Image
									src="/google-logo.png"
									alt="Google"
									width={20}
									height={20}
								/>
								Continue with Google
							</button>
							<button className="w-full flex items-center justify-center gap-3 py-3 rounded-lg border border-[#E4E4E7] bg-[#F7F7F7] text-[#18181B] font-medium text-base hover:bg-[#F4F4F5] transition-colors">
								<Image
									src="/facebook.png"
									alt="Facebook"
									width={20}
									height={20}
								/>
								Continue with Facebook
							</button>
						</div>
					</div>
				</div>
				{/* Right: Carousel Image and Feature Card */}
				<div className="hidden md:flex flex-1 items-stretch h-full min-h-0 relative overflow-hidden">
					{/* Carousel Image */}
					<div className="relative w-full h-full flex items-center justify-center min-h-0">
						<Image
							src={carouselImages[current]}
							alt="Signup Hero"
							fill
							className="object-cover w-full h-full"
							priority
						/>
						{/* Gradient Overlay */}
						<div className="absolute inset-0 bg-gradient-to-t from-[#00AEA4] to-transparent opacity-90 pointer-events-none z-10" />
						{/* Feature Card */}

						<div className="p-4 rounded-2xl bg-[#335CFF] left-[15%] top-[45%] absolute backdrop:blur-xl z-20 max-w-[60%]">
							<span className="font-regular text-[36px] leading-[44px] text-[#F4F4F5]">
								Easily manage all your job applications
							</span>

							<div className="absolute -top-6 -left-6">
								<Image
									src="/line-md_star.png"
									width={52}
									height={52}
									alt="star"
								/>
							</div>
						</div>
						<div className="absolute left-1/2 -bottom-[40px] -translate-x-1/2 -translate-y-1/2 z-20 flex flex-col items-start gap-4 w-[70%] max-w-full">
							<div
								className="relative w-full flex justify-center items-center"
								style={{ minHeight: 220 }}>
								{/* Back card 2 */}
								<div className="absolute left-1/2 -translate-x-1/2 scale-90 translate-y-8 opacity-60 z-0 w-full">
									<OpportunityCard {...opportunityCardMock} />
								</div>
								{/* Back card 1 */}
								<div className="absolute left-1/2 -translate-x-1/2 scale-95 translate-y-4 opacity-80 z-10 w-full">
									<OpportunityCard {...opportunityCardMock} />
								</div>
								{/* Top card */}
								<div className="relative z-20 w-full ">
									<OpportunityCard {...opportunityCardMock} />
								</div>
							</div>
						</div>
						{/* Dot Controls */}
						<div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-3 z-30">
							{carouselImages.map((_, idx) => (
								<button
									key={idx}
									onClick={() => handleDotClick(idx)}
									className={`w-4 h-4 rounded-full bg-[#D4D4D8]  border-white transition-all duration-200 ${
										current === idx ? "scale-125 bg-white" : ""
									}`}
									aria-label={`Go to slide ${idx + 1}`}
								/>
							))}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
