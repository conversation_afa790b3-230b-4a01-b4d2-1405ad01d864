"use client";

import Link from "next/link";
import { AfricaSkillzLogoColoredIcon } from "@/components/common/icons";

const webAppUrl = process.env.NEXT_PUBLIC_WEBAPP_URL || "http://localhost:3001";

const loginOptions = [
	{
		title: "Recruiter",
		description: "Access your recruiter dashboard to manage job listings",
		href: `${webAppUrl}/auth/login/recruiter`,
	},
	{
		title: "Employer",
		description: "Sign in to post RFQs and RFPs for procurement opportunities",
		href: `${webAppUrl}/auth/login/employer`,
	},
	{
		title: "Facilitator",
		description: "Manage events, scholarships, and courses",
		href: `${webAppUrl}/auth/login/facilitator`,
	},
	{
		title: "Job Seeker",
		description: "Search and apply for job opportunities",
		href: `${webAppUrl}/auth/login/job-seeker`,
	},
];

export default function LoginPage() {
	return (
		<main className="min-h-screen bg-white p-8">
			<div className="mb-12">
				<Link href="/">
					<AfricaSkillzLogoColoredIcon className="h-[42px] w-[116px]" />
				</Link>
			</div>
			<div className="max-w-2xl mx-auto">
				<div className="text-center mb-12">
					<h1 className="text-2xl font-semibold text-[#27272A] mb-2">
						Welcome back to AfricaSkillz
					</h1>
					<p className="text-[#71717A]">Choose your account type to sign in</p>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					{loginOptions.map((option, index) => (
						<a
							key={index}
							href={option.href}
							className="block p-6 border border-[#E4E4E7] rounded-lg hover:border-[#335CFF] hover:shadow-md transition-all duration-200 group">
							<h3 className="font-semibold text-[#27272A] mb-2 group-hover:text-[#335CFF] transition-colors">
								{option.title}
							</h3>
							<p className="text-sm text-[#71717A]">{option.description}</p>
						</a>
					))}
				</div>
				<div className="text-center mt-8">
					<p className="text-sm text-[#71717A]">
						Don&apos;t have an account?{" "}
						<a
							href={webAppUrl}
							className="text-[#335CFF] hover:text-[#2441B5] font-medium transition-colors">
							Sign up here
						</a>
					</p>
				</div>
			</div>
		</main>
	);
}
