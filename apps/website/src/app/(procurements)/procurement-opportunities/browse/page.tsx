"use client";

import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import OpportunityCard from "@/components/ui/OpportunityCard";
import { LabelInput, Tab } from "@/components/ui";
import { EmptyStateIcon, FilterIcon } from "@/components/common/icons";

import Image from "next/image";
import { useState } from "react";
import FilterSidebar, { FilterSection } from "@/components/ui/FilterSidebar";
import EmptyState from "@/components/ui/EmptyState";

const opportunitiesData = [
	{
		id: "1",
		title: "IT Infrastructure Upgrade for Regional offices",
		company: "Revmet",
		companyLogo: "/rn-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["IT Services", "Request for Proposal (RFP)"],
		isBookmarked: false,
		showTime: false,
		showBudget: true,
		isFulltime: false,
		showDate: true,
		date: "Posted: Apr 2, 2025",
	},
	{
		id: "2",
		title: "Tender for Construction of School Buildings",
		company: "Department of Education",
		companyLogo: "/rn-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Construction", "Tender"],
		isBookmarked: false,
		showTime: false,
		showBudget: true,
		isFulltime: false,
		showDate: true,
		date: "Posted: Apr 2, 2025",
	},
	{
		id: "3",
		title: "Improving Healthcare Delivery in Remote Regions",
		company: "Ministry of Health",
		companyLogo: "/rn-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Healthcare", "Expression of Interest (EOI)"],
		isBookmarked: false,
		showTime: false,
		showBudget: true,
		isFulltime: false,
		showDate: true,
		date: "Posted: Apr 2, 2025",
	},
	{
		id: "4",
		title: "Tender for Construction of School Buildings",
		company: "Department of Education",
		companyLogo: "/rn-logo.png",
		location: "Lagos, NG",
		workType: "Full-time",
		timezone: "GMT +1",
		tags: ["Construction", "Tender"],
		isBookmarked: false,
		showTime: false,
		showBudget: true,
		isFulltime: false,
		showDate: true,
		date: "Posted: Apr 2, 2025",
	},
];

const tabs = [
	"All",
	"Africa PPP",
	"BID Notices",
	"Corporate Procurement",
	"Expression of Interest",
	"Procurement Notice",
	"Request for Proposals",
	"Tenders",
];

const filters = {
	countries: ["Nigeria", "Kenya", "South Africa", "Ghana", "Remote"],
	category: [
		"Construction",
		"IT Services",
		"Healthcare",
		"Consultancy",
		"Energy",
		"Agriculture",
		"Logistics",
		"Education",
	],
	workingHours: ["Flexible"],
};

export default function BrowseOpportunitiesPage() {
	const [selectedCountries, setSelectedCountries] = useState<string[]>([]);
	const [selectedCategory, setSelectedCategory] = useState<string[]>([]);
	const [selectedWorkingHour, setSelectedWorkingHour] = useState<string[]>([]);
	const [activeTab, setActiveTab] = useState("All");

	const handleBookmark = (id: string) => {
		console.log("Bookmark opportunity:", id);
		// Implement bookmark functionality
	};

	const handleShare = (id: string) => {
		console.log("Share opportunity:", id);
		// Implement share functionality
	};

	const handleViewDetails = (id: string) => {
		console.log("View details for opportunity:", id);
		// Implement navigation to opportunity details
	};

	// const handleViewMore = () => {
	// 	console.log("View more opportunities");
	// 	// Implement navigation to opportunities page
	// };

	const buildFilterSections = (): FilterSection[] => {
		if (activeTab === "All") {
			return [
				{
					type: "checkbox",
					title: "Countries",
					options: filters.countries.map((c) => ({ label: c, value: c })),
					selected: selectedCountries,
					onChange: setSelectedCountries,
					placeholder: "Select countries",
				},
				{
					type: "checkbox",
					title: "Category",
					options: filters.category.map((j) => ({ label: j, value: j })),
					selected: selectedCategory,
					onChange: setSelectedCategory,
					placeholder: "Select category",
				},
				{
					type: "checkbox",
					title: "Working Hours",
					options: filters.workingHours.map((j) => ({ label: j, value: j })),
					selected: selectedWorkingHour,
					onChange: setSelectedWorkingHour,
					placeholder: "Select job types",
				},
			];
		}
		return [];
	};

	return (
		<div className="min-h-screen flex flex-col bg-[#FAFAFA]">
			<Header />
			<main className="flex-1 w-full">
				{/* Tab Bar */}
				<div className="w-full overflow-x-auto md:hidden border-y border-[#E4E4E7]">
					<Tab
						tabs={tabs}
						activeTab={activeTab}
						onTabChange={setActiveTab}
						className=" md:hidden w-fit"
					/>
				</div>
				<Tab
					tabs={tabs}
					activeTab={activeTab}
					onTabChange={setActiveTab}
					className="md:block hidden"
				/>
				{/* Top Section */}
				<section className="mx-auto">
					<div className="flex flex-col px-4 py-8 md:flex-row md:items-center md:justify-between gap-6 md:gap-[58px] md:px-[80px] md:py-[60px]">
						<div className="w-full">
							<h1 className="text-[36px] leading-[44px] md:text-[48px] mdleading-[64px] font-regular text-[#27272A] mb-3 md:mb-5">
								Find and Post Procurement <br />
								Opportunities
							</h1>
							<p className="text-[#A1A1AA] text-base md:text-[20px] font-semibold leading-[26px] mb-[18px] md:mb-[30px]">
								Connect with quality vendors and businesses. Post your{" "}
								<br className="hidden md:block" /> procurement needs or find
								relevant opportunities all in one place.
							</p>
							<div className="flex items-center gap-5 md:mb-5">
								<LabelInput
									inputType="search"
									placeholder="Find a procurement opportunity"
									className="bg-[#FFFFFE] w-full"
								/>
							</div>
						</div>
						<div className="  w-full h-[176px] md:h-[313px] rounded-xl overflow-hidden relative">
							<Image
								src="/procurement-img.png"
								alt="Job search"
								fill
								className="object-cover"
							/>
						</div>
					</div>
				</section>
				{/* Main Content */}

				<div className="md:hidden  bg-white py-6 px-2">
					<LabelInput
						inputType="dropdown"
						className="w-fit ml-auto"
						dropdownLeftIcon={<FilterIcon />}
						data={[
							{
								label: "Filters",
								value: "Last 7 days",
							},
						]}
					/>
				</div>

				<section className="mx-auto px-2 py-6 md:px-[90px] md:py-[60px] flex gap-[48px] bg-[#FFFFFF] md:bg-[#F4F4F5]">
					<div className="w-fit hidden md:block">
						{/* Filter Sidebar */}
						<FilterSidebar
							sections={buildFilterSections()}
							onApplyFilters={() => {}}
							onClearAll={() => {
								setSelectedCountries([]);
								setSelectedCategory([]);
								setSelectedWorkingHour([]);
							}}
						/>
					</div>
					{/* Job Grid */}

					{opportunitiesData.length === 0 ? (
						<section className="w-full m-auto">
							<EmptyState
								icon={<EmptyStateIcon />}
								title="Sorry, there are no jobs available for that search"
								subtitle="Try removing filters or changing some of your search criteria"
								onClear={() => {
									setSelectedCountries([]);
									setSelectedCategory([]);
									setSelectedWorkingHour([]);
									// Add any other filter resets here
								}}
							/>
						</section>
					) : (
						<section className="w-full">
							<div className="flex flex-col gap-4 md:gap-[18px]">
								{opportunitiesData.map((opportunity) => (
									<OpportunityCard
										key={opportunity.id}
										{...opportunity}
										onBookmark={handleBookmark}
										onShare={handleShare}
										onViewDetails={handleViewDetails}
										btnText="Apply now"
									/>
								))}
							</div>
						</section>
					)}
				</section>

				<Footer />
			</main>
		</div>
	);
}
