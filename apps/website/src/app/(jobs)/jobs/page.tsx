"use client";

import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import OpportunityCard from "@/components/ui/OpportunityCard";
import { LabelInput } from "@/components/ui";
import { useState } from "react";
import Image from "next/image";
import EmptyState from "@/components/ui/EmptyState";
// import { FileIcon } from "lucide-react";
import { EmptyStateIcon, FilterModernIcon } from "@/components/common/icons";
import FilterSidebar, { FilterSection } from "@/components/ui/FilterSidebar";
import { Tab } from "@/components/ui";
import { useRouter } from "next/navigation";
import { jobs } from "@/lib/opportunities";

const filters = {
	countries: ["Nigeria", "Kenya", "South Africa", "Ghana", "Remote"],
	jobType: ["Full Time", "Part Time", "Internship"],
	workingHours: ["Flexible"],
};

const tabs = [
	"All Jobs",
	"Jobs by Recruiter",
	"Jobs by African Country",
	"Jobs by Industry",
	"Jobs by Company",
	"Premium Jobs",
	"Legal Jobs",
	"International Jobs",
];

const recruiters = [
	{ label: "Google", value: "google" },
	{ label: "Microsoft", value: "microsoft" },
	{ label: "Andela", value: "andela" },
	{ label: "Flutterwave", value: "flutterwave" },
	{ label: "Paystack", value: "paystack" },
];

const africanCountriesByRegion: Record<string, string[]> = {
	"West Africa": [
		"Nigeria",
		"Ghana",
		"Senegal",
		"Ivory Coast",
		"Burkina Faso",
		"Mali",
		"Niger",
		"Togo",
		"Benin",
		"Sierra Leone",
		"Liberia",
		"Guinea",
		"Guinea-Bissau",
		"The Gambia",
		"Cape Verde",
	],
	"East Africa": [
		"Kenya",
		"Ethiopia",
		"Tanzania",
		"Uganda",
		"Rwanda",
		"Burundi",
		"South Sudan",
		"Somalia",
		"Eritrea",
		"Djibouti",
		"Comoros",
		"Seychelles",
		"Mauritius",
	],
	"North Africa": [
		"Egypt",
		"Libya",
		"Tunisia",
		"Algeria",
		"Morocco",
		"Sudan",
		"Western Sahara",
	],
	"Central Africa": [
		"Cameroon",
		"Central African Republic",
		"Chad",
		"Republic of the Congo",
		"Democratic Republic of the Congo",
		"Equatorial Guinea",
		"Gabon",
		"Sao Tome and Principe",
		"Angola",
	],
	"Southern Africa": [
		"South Africa",
		"Botswana",
		"Namibia",
		"Lesotho",
		"Eswatini",
		"Zimbabwe",
		"Zambia",
		"Malawi",
		"Mozambique",
		"Madagascar",
	],
};

export default function OpportunitiesPage() {
	const [selectedCountries, setSelectedCountries] = useState<string[]>([]);
	const [selectedJobTypes, setSelectedJobTypes] = useState<string[]>([]);
	const [selectedRecruiters, setSelectedRecruiters] = useState<string[]>([]);
	const [activeTab, setActiveTab] = useState("All Jobs");

	const buildFilterSections = (): FilterSection[] => {
		if (activeTab === "All Jobs") {
			return [
				{
					type: "checkbox",
					title: "Countries",
					options: filters.countries.map((c) => ({ label: c, value: c })),
					selected: selectedCountries,
					onChange: setSelectedCountries,
					placeholder: "Select countries",
				},
				{
					type: "checkbox",
					title: "Job Type",
					options: filters.jobType.map((j) => ({ label: j, value: j })),
					selected: selectedJobTypes,
					onChange: setSelectedJobTypes,
					placeholder: "Select job types",
				},
				{
					type: "checkbox",
					title: "Working Hours",
					options: filters.workingHours.map((j) => ({ label: j, value: j })),
					selected: selectedJobTypes,
					onChange: setSelectedJobTypes,
					placeholder: "Select job types",
				},
			];
		} else if (activeTab === "Jobs by Recruiter") {
			return [
				{
					type: "checkbox",
					title: "Recruiters",
					options: recruiters,
					selected: selectedRecruiters,
					onChange: setSelectedRecruiters,
					placeholder: "Select recruiters",
				},
			];
		} else if (activeTab.toLowerCase() === "jobs by african country") {
			return Object.entries(africanCountriesByRegion).map(
				([region, countries]) => ({
					type: "checkbox",
					title: region,
					options: countries.map((country) => ({
						label: country,
						value: country,
					})),
					selected: selectedCountries,
					onChange: setSelectedCountries,
					placeholder: `Select countries in ${region}`,
				})
			);
		}
		return [];
	};

	const router = useRouter();
	// const pathname = usePathname();

	const handleViewDetails = (id: string) => {
		router.push(`/jobs/${id}`);
	};

	// const handleViewMore = () => {
	// 	console.log("View more opportunities");
	// 	// Implement navigation to opportunities page
	// };

	return (
		<section className="min-h-screen flex flex-col bg-[#FAFAFA]">
			<Header />
			<main className="flex-1 w-full">
				{/* Tab Bar */}
				<Tab
					tabs={tabs}
					activeTab={activeTab}
					onTabChange={setActiveTab}
					className="hidden md:block"
				/>

				<div className="w-full overflow-x-auto md:hidden border-y border-[#E4E4E7]">
					<Tab
						tabs={tabs}
						activeTab={activeTab}
						onTabChange={setActiveTab}
						className=" md:hidden w-fit"
					/>
				</div>
				{/* Top Section */}
				<section className=" mx-auto ">
					<div className="flex flex-col py-8 px-4 md:flex-row sm:items-center md:justify-between gap-6 md:gap-[58px] md:px-[80px] md:py-[60px]">
						<div className="w-full">
							<h1 className="text-[36px] leading-[44px] md:text-[48px] md:leading-[64px] font-regular text-neutral-800 mb-5">
								Discover Job Opportunities <br className="hidden md:block" />{" "}
								across Africa
							</h1>
							<p className="text-[#A1A1AA] text-base md:text-[20px] font-semibold leading-[26px] mb-[30px]">
								Browse thousands of job listings across Africa and find your
								next <br className="hidden md:block" /> career opportunity.
							</p>
							<div className="flex items-center gap-2 mb-5">
								<LabelInput
									inputType="search"
									placeholder="Job title, skill, or company"
									className="w-full max-w-md"
								/>
							</div>
							<div className="flex flex-wrap gap-2">
								<span className="bg-[#F4EBF8] text-[#8F34B4] px-3 py-1 rounded-full text-[12px] leading-[16px] flex items-center md:text-[18px] md:leading-[24px] font-normal">
									UX Designer
								</span>
								<span className="bg-[#F4EBF8] text-[#8F34B4] px-3 py-1 rounded-full text-[12px] leading-[16px] flex items-center md:text-[18px] md:leading-[24px] font-normal">
									AI Engineer
								</span>
								<span className="bg-[#F4EBF8] text-[#8F34B4] px-3 py-1 rounded-full text-[12px] leading-[16px] flex items-center md:text-[18px] md:leading-[24px] font-normal">
									Marketing
								</span>
								<span className="bg-[#F4EBF8] text-[#8F34B4] px-3 py-1 rounded-full text-[12px] leading-[16px] flex items-center md:text-[18px] md:leading-[24px] font-normal">
									Wordpress Dev
								</span>
							</div>
						</div>
						<div className=" w-full h-[176px] md:h-[313px] rounded-xl overflow-hidden relative">
							<Image
								src="/opportunities-img.png"
								alt="Job search"
								fill
								className="object-cover"
							/>
						</div>
					</div>
				</section>
				{/* Main Content */}
				<section className="mx-auto px-4 py-10  md:px-[104px] md:py-[60px] flex flex-col md:flex-row gap-5">
					{/* Filter Sidebar */}
					<LabelInput
						inputType="dropdown"
						data={[{ label: "Last 7 days", value: "7" }]}
						dropdownLeftIcon={<FilterModernIcon />}
						className="ml-auto w-fit md:hidden min-w-[144px]"
					/>
					<div className=" hidden md:block">
						<FilterSidebar
							sections={buildFilterSections()}
							onApplyFilters={() => {}}
							onClearAll={() => {
								setSelectedCountries([]);
								setSelectedJobTypes([]);
								setSelectedRecruiters([]);
							}}
						/>
					</div>
					{/* Job Grid */}

					{jobs.length === 0 ? (
						<section className="w-full m-auto">
							<EmptyState
								icon={<EmptyStateIcon />}
								title="Sorry, there are no jobs available for that search"
								subtitle="Try removing filters or changing some of your search criteria"
								onClear={() => {
									setSelectedCountries([]);
									setSelectedJobTypes([]);
									setSelectedRecruiters([]);
									// Add any other filter resets here
								}}
							/>
						</section>
					) : (
						<section className="flex-1 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-4">
							{jobs.map((job, i) => (
								<OpportunityCard
									key={i}
									onViewDetails={() => handleViewDetails(job.id)}
									{...job}
								/>
							))}
						</section>
					)}
				</section>
				{/* Newsletter & Footer */}
				<Footer />
			</main>
		</section>
	);
}
