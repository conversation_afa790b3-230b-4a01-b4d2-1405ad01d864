# Africa Skillz - Job Platform Monorepo

A modern job platform connecting African talent with global opportunities. Built as a monorepo with Next.js 15, React 19, and Tailwind CSS v4.

![Africa Skillz](./apps/website/public/africa-skillz-logo.png)

## 🌟 Features

- **Modern Job Search**: Advanced search with filters for keywords, location, categories, and job types
- **Multi-language Support**: 12 languages including African languages (Swahili, Amharic, Hausa, Yoruba, Igbo, Zulu, Afrikaans)
- **Responsive Design**: Mobile-first approach with pixel-perfect implementation
- **Design System**: Comprehensive design tokens based on Figma specifications
- **Component Library**: Reusable, modular components following best practices
- **Performance Optimized**: Built with Next.js 15 and React 19 for optimal performance

## 🏗️ Monorepo Structure

```
africa-skillz/
├── apps/
│   ├── admin-dashboard/   # Admin dashboard (Next.js app)
│   ├── web-app/           # User web app (Next.js app)
│   └── website/           # Main public website (Next.js app)
├── packages/
│   ├── lib/               # Shared libraries (optional)
│   └── ui/                # Shared UI components (optional)
├── docs/                  # Documentation
├── package.json           # Monorepo root dependencies/scripts
├── turbo.json             # Turborepo config (if used)
└── README.md
```

Each app in `apps/` is a standalone Next.js project with its own configuration and dependencies.

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/auxilum-interactive/AfricaSkillz-FrontEnd.git
   cd AfricaSkillz-FrontEnd
   ```

2. **Install dependencies (from the monorepo root)**

   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Run an app (from the monorepo root)**

   For the main website:

   ```bash
   npm run dev --workspace=apps/website
   # or
   yarn workspace apps/website dev
   # or
   cd apps/website && npm run dev
   ```

   For the admin dashboard:

   ```bash
   npm run dev --workspace=apps/admin-dashboard
   # or
   yarn workspace apps/admin-dashboard dev
   # or
   cd apps/admin-dashboard && npm run dev
   ```

   For the web app:

   ```bash
   npm run dev --workspace=apps/web-app
   # or
   yarn workspace apps/web-app dev
   # or
   cd apps/web-app && npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000) (or the port specified by the app)

## 🏗️ App Structure Example (apps/website)

```
apps/website/
├── public/                 # Static assets
├── src/
│   ├── app/               # Next.js App Router
│   ├── components/        # Reusable components
│   └── lib/               # Utilities
├── tailwind.config.ts     # Tailwind configuration
├── next.config.ts         # Next.js configuration
└── package.json           # App dependencies
```

## 🛠️ Development

### Available Scripts (per app)

From the app directory (e.g., `apps/website`):

```bash
# Development server
npm run dev

# Production build
npm run build

# Start production server
npm run start

# Lint code
npm run lint

# Type checking
npm run type-check
```

### Code Style

- **ESLint**: Configured with Next.js recommended rules
- **TypeScript**: Strict mode enabled
- **Prettier**: Code formatting (recommended)
- **Tailwind CSS**: Utility-first styling

### Environment Variables

Create a `.env.local` file in the app's root directory (e.g., `apps/website/.env.local`):

```env
# Add your environment variables here
NEXT_PUBLIC_API_URL=your_api_url
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

## 📦 Deployment

### Deploying an Individual App (e.g., Website)

If you previously deployed the `website` app as a standalone project, you can still deploy it from the monorepo. Most platforms (Vercel, Netlify, etc.) allow you to specify the root directory for the app you want to deploy (e.g., `apps/website`).

**To deploy the website app:**

- Set the project root to `apps/website` in your deployment platform settings.
- All build and output settings remain the same as before.

**Other apps** can be deployed similarly by pointing to their respective directories.

#### Example: Vercel

- Set the root directory to `apps/website` (or the app you want to deploy)
- Use the default build and output settings for Next.js

#### Example: Netlify

- Set the base directory to `apps/website`
- Build command: `npm run build`
- Publish directory: `.next`

## 🧩 Components, Design System, and More

All design system, component, and feature documentation is available in the `/docs` folder and within each app's README (if present).

- [Design System Guide](./docs/DESIGN_SYSTEM.md)
- [Component Library](./docs/COMPONENTS.md)
- [API Integration](./docs/API.md)
- [Deployment Guide](./docs/DEPLOYMENT.md)

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. **Commit your changes**
   ```bash
   git commit -m 'Add some amazing feature'
   ```
4. **Push to the branch**
   ```bash
   git push origin feature/amazing-feature
   ```
5. **Open a Pull Request**

### Coding Standards

- Use TypeScript for all new code
- Follow the existing component structure
- Write meaningful commit messages
- Add JSDoc comments for complex functions
- Ensure responsive design for all components

## 🐛 Troubleshooting

### Common Issues

**Build Errors**

```bash
# Clear Next.js cache
rm -rf .next
npm run build
```

**Styling Issues**

```bash
# Rebuild Tailwind
npm run build:css
```

**TypeScript Errors**

```bash
# Check types
npm run type-check
```

## 📄 License

This project is proprietary software owned by Auxilum Interactive.

## 👥 Team

- **Frontend Development**: [Your Name]
- **Design**: Based on Figma specifications
- **Project Management**: Auxilum Interactive

## 📞 Support

For support and questions:

- Create an issue in this repository
- Contact the development team
- Check the documentation in `/docs`

---

**Built with ❤️ for African talent worldwide**
