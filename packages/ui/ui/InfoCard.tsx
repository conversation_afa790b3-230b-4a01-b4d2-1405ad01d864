import { cn } from "@/lib/utils";
import { ReactNode } from "react";
import Image from "next/image";

interface InfoCardProps {
  title: string;
  description?: string;
  icon?: ReactNode;
  image?: string;
  value?: string | number;
  subtitle?: string;
  actions?: ReactNode;
  className?: string;
  variant?: 'default' | 'feature' | 'metric' | 'service';
  color?: 'blue' | 'purple' | 'orange' | 'green' | 'red' | 'gray';
  hover?: boolean;
  onClick?: () => void;
}

const InfoCard = ({
  title,
  description,
  icon,
  image,
  value,
  subtitle,
  actions,
  className,
  variant = 'default',
  color = 'blue',
  hover = true,
  onClick
}: InfoCardProps) => {
  const colorStyles = {
    blue: {
      bg: "bg-gradient-to-br from-blue-50 to-blue-100",
      icon: "text-blue-600 bg-blue-100",
      accent: "border-blue-200"
    },
    purple: {
      bg: "bg-gradient-to-br from-purple-50 to-purple-100",
      icon: "text-purple-600 bg-purple-100",
      accent: "border-purple-200"
    },
    orange: {
      bg: "bg-gradient-to-br from-orange-50 to-orange-100",
      icon: "text-orange-600 bg-orange-100",
      accent: "border-orange-200"
    },
    green: {
      bg: "bg-gradient-to-br from-green-50 to-green-100",
      icon: "text-green-600 bg-green-100",
      accent: "border-green-200"
    },
    red: {
      bg: "bg-gradient-to-br from-red-50 to-red-100",
      icon: "text-red-600 bg-red-100",
      accent: "border-red-200"
    },
    gray: {
      bg: "bg-gradient-to-br from-brandGray-50 to-brandGray-100",
      icon: "text-brandGray-600 bg-brandGray-100",
      accent: "border-brandGray-200"
    }
  };

  const styles = colorStyles[color];

  const baseClasses = cn(
    "rounded-xl border transition-all duration-200",
    hover && "hover:shadow-lg hover:-translate-y-1",
    onClick && "cursor-pointer",
    styles.accent
  );

  if (variant === 'metric') {
    return (
      <div 
        className={cn(baseClasses, styles.bg, "p-6", className)}
        onClick={onClick}
      >
        <div className="flex items-start justify-between">
          <div>
            <p className="text-sm font-medium text-brandGray-600 mb-1">
              {title}
            </p>
            {value && (
              <p className="text-3xl font-bold text-brandGray-900 mb-1">
                {typeof value === 'number' ? value.toLocaleString() : value}
              </p>
            )}
            {subtitle && (
              <p className="text-sm text-brandGray-500">
                {subtitle}
              </p>
            )}
          </div>
          {icon && (
            <div className={cn("p-3 rounded-lg", styles.icon)}>
              {icon}
            </div>
          )}
        </div>
        {actions && (
          <div className="mt-4 pt-4 border-t border-white/20">
            {actions}
          </div>
        )}
      </div>
    );
  }

  if (variant === 'feature') {
    return (
      <div 
        className={cn(baseClasses, "bg-white p-6", className)}
        onClick={onClick}
      >
        {image && (
          <div className="mb-4">
            <Image 
              src={image} 
              alt={title}
              
              className="w-full h-48 object-cover rounded-lg"
            />
          </div>
        )}
        
        <div className="flex items-start space-x-4">
          {icon && (
            <div className={cn("p-3 rounded-lg flex-shrink-0", styles.icon)}>
              {icon}
            </div>
          )}
          
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-brandGray-900 mb-2">
              {title}
            </h3>
            {description && (
              <p className="text-brandGray-600 mb-4">
                {description}
              </p>
            )}
            {actions && (
              <div className="flex items-center space-x-2">
                {actions}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  if (variant === 'service') {
    return (
      <div 
        className={cn(baseClasses, "bg-white p-6 text-center", className)}
        onClick={onClick}
      >
        {icon && (
          <div className={cn("w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center", styles.icon)}>
            {icon}
          </div>
        )}
        
        <h3 className="text-lg font-semibold text-brandGray-900 mb-2">
          {title}
        </h3>
        
        {description && (
          <p className="text-brandGray-600 mb-4">
            {description}
          </p>
        )}
        
        {value && (
          <div className="text-2xl font-bold text-brandGray-900 mb-1">
            {typeof value === 'number' ? value.toLocaleString() : value}
          </div>
        )}
        
        {subtitle && (
          <p className="text-sm text-brandGray-500 mb-4">
            {subtitle}
          </p>
        )}
        
        {actions && (
          <div className="pt-4 border-t border-brandGray-200">
            {actions}
          </div>
        )}
      </div>
    );
  }

  // Default variant
  return (
    <div 
      className={cn(baseClasses, "bg-white p-6", className)}
      onClick={onClick}
    >
      <div className="flex items-start space-x-4">
        {icon && (
          <div className={cn("p-3 rounded-lg flex-shrink-0", styles.icon)}>
            {icon}
          </div>
        )}
        
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-brandGray-900 mb-1">
            {title}
          </h3>
          
          {description && (
            <p className="text-brandGray-600">
              {description}
            </p>
          )}
          
          {value && (
            <div className="mt-2 text-xl font-bold text-brandGray-900">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </div>
          )}
          
          {subtitle && (
            <p className="text-sm text-brandGray-500 mt-1">
              {subtitle}
            </p>
          )}
        </div>
      </div>
      
      {actions && (
        <div className="mt-4 pt-4 border-t border-brandGray-200">
          {actions}
        </div>
      )}
    </div>
  );
};

export default InfoCard;
