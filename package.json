{"name": "africa-skillz", "private": true, "packageManager": "npm@10.5.0", "workspaces": ["apps/*", "packages/*"], "devDependencies": {"turbo": "^1.13.0"}, "scripts": {"dev": "turbo run dev --parallel", "prebuild": "npm install --include=optional", "build": "turbo run build", "lint": "turbo run lint"}, "dependencies": {"autoprefixer": "^10.4.21", "next-transpile-modules": "^10.0.1", "postcss": "^8.5.6", "postcss-loader": "^8.1.1", "react-hot-toast": "^2.5.2", "tailwindcss": "^4.1.11"}}