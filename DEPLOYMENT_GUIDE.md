# Cross-App Navigation Deployment Guide

This guide explains how to configure cross-app navigation between the website and web-app for both development and production environments.

## 🏗️ Architecture Overview

- **Website** (Marketing): `apps/website` - Handles marketing, login selection
- **Web-App** (Application): `apps/web-app` - Handles authentication, dashboard, functionality

## 🔧 Environment Variables Configuration

### Development Environment

#### Website (`apps/website/.env.local`)

```env
# Website App Configuration (Development)
NEXT_PUBLIC_WEBSITE_URL=http://localhost:3000
NEXT_PUBLIC_WEBAPP_URL=http://localhost:3001

# Environment
NODE_ENV=development
```

#### Web-App (`apps/web-app/.env.local`)

```env
# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://test-render-845v.onrender.com/api/v1

# Cross-App Configuration (Development)
NEXT_PUBLIC_WEBSITE_URL=http://localhost:3000
NEXT_PUBLIC_WEBAPP_URL=http://localhost:3001

# Environment
NODE_ENV=development
```

### Production Environment

#### Website (`apps/website/.env.production`)

```env
# Website App Configuration (Production)
NEXT_PUBLIC_WEBSITE_URL=https://africa-skillz-website.vercel.app
NEXT_PUBLIC_WEBAPP_URL=https://africa-skillz-front-end-web-app.vercel.app

# Environment
NODE_ENV=production
```

#### Web-App (`apps/web-app/.env.production`)

```env
# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://test-render-845v.onrender.com/api/v1

# Cross-App Configuration (Production)
NEXT_PUBLIC_WEBSITE_URL=https://africa-skillz-website.vercel.app
NEXT_PUBLIC_WEBAPP_URL=https://africa-skillz-front-end-web-app.vercel.app

# Environment
NODE_ENV=production
```

## 🚀 Deployment Configuration

### Vercel Deployment

**IMPORTANT**: Vercel ignores `.env.production` files. You MUST set environment variables in the Vercel Dashboard.

#### How to Set Environment Variables in Vercel:

1. Go to your project in **Vercel Dashboard**
2. Navigate to **Settings** → **Environment Variables**
3. Add each variable individually
4. Select **Production** environment (and Preview if needed)

#### Website Deployment

**Environment Variables to Add in Vercel Dashboard:**

```
NEXT_PUBLIC_WEBSITE_URL=https://your-website-domain.vercel.app
NEXT_PUBLIC_WEBAPP_URL=https://your-webapp-domain.vercel.app
WEBAPP_DOMAIN=https://your-webapp-domain.vercel.app
NODE_ENV=production
```

#### Web-App Deployment

**Environment Variables to Add in Vercel Dashboard:**

```
NEXT_PUBLIC_API_BASE_URL=https://test-render-845v.onrender.com/api/v1
NEXT_PUBLIC_WEBSITE_URL=https://your-website-domain.vercel.app
NEXT_PUBLIC_WEBAPP_URL=https://your-webapp-domain.vercel.app
NODE_ENV=production
```

### Other Deployment Platforms

For other platforms (Netlify, AWS, etc.), ensure these environment variables are set in your deployment configuration.

## 🔄 Cross-App Navigation Flow

### User Journey

1. **Website** (`localhost:3000` / production website URL)

   - Header "Login" → Website login selection page
   - Header "Sign Up" → Web-app onboarding page
   - Login selection → Web-app specific login forms

2. **Web-App** (`localhost:3001` / production web-app URL)
   - Onboarding "Sign in" → Website login selection
   - Logout → Website home page
   - Successful login → Web-app dashboard

## 🛠️ Implementation Details

### Key Files Updated

- `apps/website/src/components/layout/Header.tsx` - Navigation buttons
- `apps/website/src/app/auth/login/page.tsx` - Login selection page
- `apps/web-app/src/components/Onboarding-decision.tsx` - Sign in link
- `apps/web-app/src/lib/api.ts` - Logout redirect

### Environment Variable Usage

```typescript
// Website to Web-App navigation
const webAppUrl = process.env.NEXT_PUBLIC_WEBAPP_URL || "http://localhost:3001";

// Web-App to Website navigation
const websiteUrl =
	process.env.NEXT_PUBLIC_WEBSITE_URL || "http://localhost:3000";
```

## ✅ Testing Checklist

### Development Testing

- [ ] Website header "Sign Up" → Web-app onboarding
- [ ] Website header "Login" → Website login selection
- [ ] Website login selection → Web-app login forms
- [ ] Web-app onboarding "Sign in" → Website login selection
- [ ] Web-app logout → Website home page

### Production Testing

- [ ] All above flows work with production URLs
- [ ] Environment variables are correctly set in deployment platform
- [ ] No hardcoded localhost URLs in production build
- [ ] Cross-origin navigation works correctly

## 🚨 Common Issues & Solutions

### Issue: 404 on Cross-App Navigation

**Solution:** Verify environment variables are set correctly in deployment platform

### Issue: Localhost URLs in Production

**Solution:** Ensure `.env.production` files are properly configured and deployed

### Issue: CORS Issues

**Solution:** Both apps should be deployed to same domain or configure CORS properly

## 📝 Notes

- Environment variables must be prefixed with `NEXT_PUBLIC_` to be available in the browser
- Production URLs should be updated to match your actual deployment domains
- Both apps must be deployed and accessible for cross-app navigation to work
